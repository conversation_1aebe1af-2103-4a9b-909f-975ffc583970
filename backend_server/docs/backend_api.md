# Backend Server API 文档

## 概述

Backend Server 提供猫砂盆系统的核心API服务，包括用户管理、设备管理、视频记录、数据分析等功能。

## 认证方式

### 1. 用户认证 (User Auth)
- **路径前缀**: `/api`
- **认证方式**: Bear<PERSON> (Logto OAuth2)
- **用途**: 普通用户访问个人数据

### 2. 服务间认证 (Service Auth)
- **路径前缀**: `/api`
- **认证方式**: Bearer <PERSON> (Service Token)
- **用途**: 微服务间通信

### 3. 工厂认证 (Factory Auth)
- **路径前缀**: `/api/factory`
- **认证方式**: Bear<PERSON> (Factory Token)
- **Token**: `FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K`
- **用途**: 工厂生产线和测试环境

## 备份管理 API

### 获取备份视频列表

**接口**: `GET /api/backup/videos`

**认证**: Factory Token

**描述**: 获取所有behavior_type为normal_poop的视频列表，用于备份下载

**请求参数**:
```
start_time: int (可选) - 开始时间戳
end_time: int (可选) - 结束时间戳  
device_id: string (可选) - 设备ID筛选
animal_id: string (可选) - 猫ID筛选
page: int (可选) - 页码，默认1
page_size: int (可选) - 每页数量，默认20
```

**响应示例**:
```json
{
  "videos": [
    {
      "video_id": "video_123",
      "device_id": "device202502270220f7cbb4421000",
      "animal_id": "cat_456",
      "start_time": 1737712707,
      "end_time": 1737712800,
      "weight_litter": 2.5,
      "weight_cat": 4.2,
      "weight_waste": 0.3,
      "video_folder": "2025-01-23_20-46-05_hls",
      "playlist_path": "records/device202502270220f7cbb4421000/2025-01-23_20-46-05_hls/playlist.m3u8",
      "weight_data_path": "records/device202502270220f7cbb4421000/2025-01-23_20-46-05_hls/cat_weight_1737712707.json",
      "behavior_type": "normal_poop",
      "cat_confidence": 0.95,
      "created_at": "2025-01-23 20:46:05"
    }
  ],
  "total": 150,
  "page": 1,
  "page_size": 20,
  "total_pages": 8
}
```

### 获取MinIO配置

**接口**: `GET /api/backup/minio-config`

**认证**: Factory Token

**描述**: 获取MinIO直接访问配置，用于备份客户端直接下载文件

**响应示例**:
```json
{
  "endpoint": "144.126.146.223:9000",
  "access_key": "animsuper",
  "secret_key": "4vbtCeEQgcN2uB",
  "use_ssl": false,
  "bucket": "records"
}
```

## 使用示例

### cURL 示例

```bash
# 获取备份视频列表
curl -X GET "http://your-server/api/backup/videos?page=1&page_size=20" \
  -H "Authorization: Bearer FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"

# 获取MinIO配置
curl -X GET "http://your-server/api/backup/minio-config" \
  -H "Authorization: Bearer FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"
```

## 数据结构说明

### MinIO文件结构
```
records/
└── device{device_id}/
    └── {date}_hls/
        ├── playlist.m3u8          # HLS播放列表
        ├── segment_0001.ts        # 视频片段
        ├── segment_0002.ts
        ├── ...
        └── cat_weight_{timestamp}.json  # 重量数据
```

### 重量数据格式
```json
{
  "timestamp": 1737712707,
  "weight_data": [
    {"time": 0, "weight": 2.5},
    {"time": 1000, "weight": 2.8},
    {"time": 2000, "weight": 3.1}
  ]
}
```

## 错误码说明

- `400`: 请求参数错误
- `401`: 认证失败
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误
