package cattoilet

import (
	"bytes"
	"cabycare-server/config"
	"cabycare-server/pkg/algo"
	"cabycare-server/pkg/cat"
	"cabycare-server/pkg/notification"
	"cabycare-server/pkg/shadow"
	"cabycare-server/pkg/storage"
	"cabycare-server/pkg/types"
	"cabycare-server/pkg/utils"
	"context"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/minio/minio-go/v7"
	"gorm.io/gorm"
)

type CatToiletService struct {
	db                  *Database
	notificationService *notification.NotificationService
	storageService      *storage.StorageService
	catService          *cat.CatService
	shadowService       *shadow.Service
	cfg                 *config.Config
}

func NewCatToiletService(cfg *config.Config, notiSrv *notification.NotificationService, storageSrv *storage.StorageService) (*CatToiletService, error) {
	db, err := NewDatabase(cfg)
	if err != nil {
		return nil, err
	}

	catService, err := cat.NewCatService(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create cat service: %v", err)
	}

	shadowService := shadow.NewService(cfg)

	return &CatToiletService{
		db:                  db,
		notificationService: notiSrv,
		storageService:      storageSrv,
		catService:          catService,
		shadowService:       shadowService,
		cfg:                 cfg,
	}, nil
}

// GetDB 返回数据库连接
func (s *CatToiletService) GetDB() *gorm.DB {
	return s.db.db
}

// getUserActiveCatStates 获取用户所有活跃的猫咪状态
func (s *CatToiletService) getUserActiveCatStates(userID string) ([]map[string]interface{}, error) {
	// 获取用户的所有猫咪
	cats, err := s.catService.ListUserCats(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user cats: %w", err)
	}

	var catStates []map[string]interface{}

	// 为每只猫咪检查活跃状态
	for _, cat := range cats {
		activeState, err := s.catService.GetActiveCatState(userID, cat.CatID)
		if err != nil {
			log.Printf("Warning: Failed to get active state for cat %s: %v", cat.CatID, err)
			continue
		}

		if activeState != nil {
			stateInfo := map[string]interface{}{
				"cat_id":      activeState.CatID,
				"state":       activeState.State,
				"description": activeState.Description,
				"start_time":  activeState.StartTime.Format(time.RFC3339),
				"end_time":    activeState.ExpectedEndTime.Format(time.RFC3339),
			}
			catStates = append(catStates, stateInfo)
		}
	}

	log.Printf("Found %d active cat states for user %s", len(catStates), userID)
	return catStates, nil
}

// User 相关服务
func (s *CatToiletService) CreateUser(user *User) error {
	// 使用雪花算法生成用户ID
	user.UserID = algo.GenerateUserID()

	// 加密密码
	hasher := sha256.New()
	hasher.Write([]byte(user.PasswordHash)) // 这里 PasswordHash 实际上是明文密码
	user.PasswordHash = hex.EncodeToString(hasher.Sum(nil))

	// 设置默认值
	user.Status = 1
	// LastLogin 保持为 nil，表示从未登录

	// 创建用户
	if err := s.db.CreateUser(user); err != nil {
		return err
	}

	// 创建默认的 unknown 猫咪
	if err := s.catService.CreateDefaultUnknownCat(user.UserID); err != nil {
		log.Printf("Failed to create default unknown cat for user %s: %v", user.UserID, err)
		// 不返回错误，因为这不是致命错误
	}

	// 创建默认的影子模式配置
	if err := s.createDefaultUserShadowConfig(user.UserID); err != nil {
		log.Printf("Failed to create default shadow mode config for user %s: %v", user.UserID, err)
		// 不返回错误，因为这不是致命错误
	}

	return nil
}

// GetUserByUsername 通过用户名获取用户信息
func (s *CatToiletService) GetUserByUsername(username string) (*User, error) {
	return s.db.GetUserByUsername(username)
}

func (s *CatToiletService) ValidateUser(username, password string) (*User, error) {
	user, err := s.db.GetUserByUsername(username)
	if err != nil {
		fmt.Printf("Failed to get user: %v\n", err)
		return nil, err
	}

	hasher := sha256.New()
	hasher.Write([]byte(password))
	hashedPassword := hex.EncodeToString(hasher.Sum(nil))

	fmt.Printf("Input password hash: %s\n", hashedPassword)
	fmt.Printf("Stored password hash: %s\n", user.PasswordHash)

	if user.PasswordHash != hashedPassword {
		return nil, errors.New("invalid password")
	}

	// 更新最后登录时间
	now := time.Now()
	user.LastLogin = &now
	if err := s.db.UpdateUser(user); err != nil {
		return nil, err
	}

	return user, nil
}

func (s *CatToiletService) GetUser(userID string) (*User, error) {
	return s.db.GetUser(userID)
}

// Cat 相关服务
func (s *CatToiletService) CreateCat(cat *cat.Cat) error {
	// 如果猫咪ID为空，则使用雪花算法生成
	if cat.CatID == "" {
		cat.CatID = algo.GenerateCatID(cat.UserID)
	}

	// 验证必填字段
	if cat.UserID == "" {
		return fmt.Errorf("user_id is required")
	}
	if cat.Name == "" {
		return fmt.Errorf("name is required")
	}

	// 确保设置了默认的status和创建时间
	if cat.Status == 0 {
		cat.Status = 1 // 默认为活跃状态
	}
	if cat.CreatedAt.IsZero() {
		cat.CreatedAt = time.Now()
	}
	if cat.UpdatedAt.IsZero() {
		cat.UpdatedAt = time.Now()
	}

	return s.catService.CreateCat(cat)
}

func (s *CatToiletService) GetCat(catID string) (*cat.Cat, error) {
	return s.catService.GetCat(catID)
}

func (s *CatToiletService) ListUserCats(userID string) ([]cat.Cat, error) {
	return s.catService.ListUserCats(userID)
}

// UploadCatAvatar 上传猫咪头像
func (s *CatToiletService) UploadCatAvatar(catID, base64Image string) (string, error) {
	return s.storageService.UploadCatAvatar(catID, base64Image)
}

// GetStorageObject 获取存储对象
func (s *CatToiletService) GetStorageObject(path string) (*minio.Object, error) {
	return s.storageService.GetObject(path)
}

// ProcessCatAvatarURL 处理猫咪头像URL，为存在头像的猫咪生成完整的访问URL
func (s *CatToiletService) ProcessCatAvatarURL(c *cat.Cat) {
	if c.AvatarURL != "" {
		// 将相对路径转换为完整的API访问路径
		c.AvatarURL = fmt.Sprintf("%s/api/storage/assets/%s", s.cfg.CabyBackend.URL, c.AvatarURL)
	}
}

func (s *CatToiletService) ListDeviceRecords(deviceID string, startTime, endTime *time.Time) ([]RecordShit, error) {
	return s.db.ListDeviceRecords(deviceID, startTime, endTime)
}

func (s *CatToiletService) ListUserRecords(userID string, startTime, endTime *time.Time) ([]RecordShit, error) {
	return s.db.ListUserRecords(userID, startTime, endTime)
}

func (s *CatToiletService) ListCatRecords(catID string) ([]RecordShit, error) {
	return s.db.ListCatRecords(catID)
}

// Device 相关服务
func (s *CatToiletService) CreateDevice(device *Device) error {
	// 使用雪花算法生成设备ID
	device.DeviceID = algo.GenerateDeviceID()
	return s.db.CreateDevice(device)
}

func (s *CatToiletService) GetDevice(deviceID string) (*Device, error) {
	return s.db.GetDevice(deviceID)
}

func (s *CatToiletService) UpdateDeviceHeartbeat(deviceID string) error {
	return s.db.UpdateDeviceHeartbeat(deviceID)
}

func (s *CatToiletService) UpdateDeviceTimezone(deviceID string, timezone string) error {
	return s.db.UpdateDeviceTimezone(deviceID, timezone)
}

func (s *CatToiletService) GetDeviceTimezone(deviceID string) (string, error) {
	return s.db.GetDeviceTimezone(deviceID)
}

func (s *CatToiletService) ListUserDevices(userID string) ([]Device, error) {
	return s.db.ListUserDevices(userID)
}

// ==================== 用户相关服务 ====================

// CreateUserProfile 创建用户详细信息
func (s *CatToiletService) CreateUserProfile(profile *UserProfile) error {
	return s.db.CreateUserProfile(profile)
}

// UpdateUserProfile 更新用户详细信息
func (s *CatToiletService) UpdateUserProfile(profile *UserProfile) error {
	return s.db.UpdateUserProfile(profile)
}

// GetUserProfile 获取用户详细信息
func (s *CatToiletService) GetUserProfile(userID string) (*UserProfile, error) {
	return s.db.GetUserProfile(userID)
}

// UpdateUserSettings 更新用户设置
func (s *CatToiletService) UpdateUserSettings(settings *UserSettings) error {
	return s.db.UpdateUserSettings(settings)
}

// GetUserSettings 获取用户设置
func (s *CatToiletService) GetUserSettings(userID string) (*UserSettings, error) {
	return s.db.GetUserSettings(userID)
}

// CreateUserLog 创建用户日志
func (s *CatToiletService) CreateUserLog(log *UserLog) error {
	return s.db.CreateUserLog(log)
}

// ==================== 设备相关服务 ====================

// UpdateDevice 更新设备信息
func (s *CatToiletService) UpdateDevice(device *Device) error {
	return s.db.UpdateDevice(device)
}

// GetDeviceStatus 获取设备状态
func (s *CatToiletService) GetDeviceStatus(deviceID string) (*DeviceStatus, error) {
	return s.db.GetDeviceStatus(deviceID)
}

// UpdateDeviceConfig 更新设备配置
func (s *CatToiletService) UpdateDeviceConfig(config *DeviceConfig) error {
	return s.db.UpdateDeviceConfig(config)
}

// GetDeviceConfig 获取设备配置
func (s *CatToiletService) GetDeviceConfig(deviceID string) (*DeviceConfig, error) {
	return s.db.GetDeviceConfig(deviceID)
}

// CreateDeviceMaintenance 创建设备维护记录
func (s *CatToiletService) CreateDeviceMaintenance(maintenance *DeviceMaintenance) error {
	return s.db.CreateDeviceMaintenance(maintenance)
}

// ==================== 猫咪相关服务 ====================

// UpdateCat 更新猫咪基本信息
func (s *CatToiletService) UpdateCat(cat *cat.Cat) error {
	return s.catService.UpdateCat(cat)
}

// UpdateCatBehavior 更新猫咪行为特征
func (s *CatToiletService) UpdateCatBehavior(behavior *cat.CatBehavior) error {
	return s.catService.UpdateCatBehavior(behavior)
}

// ==================== 视频记录相关服务 ====================

// Check if the user has access to the device
func (s *CatToiletService) CheckUserDeviceAccess(userID, deviceID string) error {
	// Use the newer implementation that considers family group access
	hasAccess, err := s.checkUserDeviceAccessInternal(userID, deviceID)
	if err != nil {
		return err
	}
	if !hasAccess {
		return fmt.Errorf("access denied: user does not have permission to access this device")
	}
	return nil
}

func (s *CatToiletService) GetRecordShit(videoID string) (*RecordShit, error) {
	record, err := s.db.GetRecordShit(videoID)
	if err != nil {
		return nil, fmt.Errorf("failed to get video record: %v", err)
	}
	return record, nil
}

// CreateShitRecord 创建视频记录
func (s *CatToiletService) CreateShitRecord(record *RecordShit, userID string) error {
	// 获取设备时区信息仅用于日志记录
	_, err := s.GetDeviceTimezone(record.DeviceID)
	if err != nil {
		log.Printf("Failed to get device timezone: %v, using UTC", err)
	}

	if record.StartTime == 0 {
		record.StartTime = time.Now().Unix()
	}

	videoID, err := algo.GenerateVideoID(record.DeviceID, userID, record.StartTime)
	if err != nil {
		return fmt.Errorf("failed to generate video ID: %v", err)
	}
	record.VideoID = videoID

	// 检查记录是否已存在
	existingRecord, err := s.db.GetRecordShit(videoID)
	if err == nil && existingRecord.Status == 1 {
		// 记录已存在且状态正常，直接返回成功
		log.Printf("Record already exists with video_id: %s", videoID)
		return nil
	}

	// 设置创建时间
	record.CreatedAt = time.Now()
	record.UpdatedAt = record.CreatedAt

	// 设置初始处理阶段
	record.ProcessStage = 0 // 未处理

	// 创建记录
	if err := s.db.CreateShitRecord(record); err != nil {
		// 如果是唯一键冲突（记录已存在但可能状态不正常），尝试更新
		if strings.Contains(err.Error(), "duplicate") {
			record.Status = 1 // 确保状态正常
			if err := s.db.UpdateRecord(record); err != nil {
				return fmt.Errorf("failed to update existing record: %v", err)
			}
			return nil
		}
		return fmt.Errorf("failed to create video record: %v", err)
	}

	// 计算持续时间（如果EndTime存在）
	var duration int
	if record.EndTime != nil {
		duration = int(*record.EndTime - record.StartTime)
	}

	// 创建通知（此时还没有AI分析结果，使用默认标题）
	notification := &notification.Notification{
		UserID:   userID,
		Type:     notification.TypeDaily,
		SubType:  notification.SubTypeToilet,
		Title:    "某只喵喵：",
		Body:     fmt.Sprintf("本喵便便了，用时%d秒，速来铲屎！", duration),
		Priority: 1,
	}

	// 存储通知（仅当通知服务可用时）
	if s.notificationService != nil {
		if err := s.notificationService.StorePendingNotification(record.VideoID, notification); err != nil {
			log.Printf("Failed to store pending notification: %v", err)
		}
	}

	// 异步处理视频分析
	go s.analyzeVideo(record, userID)

	return nil
}

// AnalyzeVideoAndUpdateDB 同步分析视频并更新数据库
// 这个方法用于静态检测API，需要同步返回结果
func (s *CatToiletService) AnalyzeVideoAndUpdateDB(record *RecordShit, userID string) (*RecordAnalysis, error) {
	// 1. 更新处理状态
	record.ProcessStage = 1 // 开始处理
	if err := s.db.UpdateRecord(record); err != nil {
		log.Printf("Failed to update record status: %v", err)
		return nil, fmt.Errorf("failed to update record status: %v", err)
	}

	// 2. 调用AI服务进行分析
	analysis, err := s.callAIService(record.VideoID, userID)
	if err != nil {
		log.Printf("Failed to analyze video: %v", err)
		// 更新处理状态为失败
		record.ProcessStage = 0
		s.db.UpdateRecord(record)
		return nil, fmt.Errorf("failed to analyze video: %v", err)
	}

	// 3. 更新处理状态为完成
	record.ProcessStage = 2 // 处理完成
	if err := s.db.UpdateRecord(record); err != nil {
		log.Printf("Failed to update record status to completed: %v", err)
		// 即使状态更新失败，也返回分析结果
	}

	log.Printf("Video analysis completed successfully for video %s, animal_id: %s", record.VideoID, analysis.AnimalID)
	return analysis, nil
}

// ==================== 统计相关服务 ====================

// UpdateCatMetricsDaily 更新猫咪每日统计
func (s *CatToiletService) UpdateCatMetricsDaily(metrics *cat.CatMetricsDaily) error {
	return s.catService.UpdateCatMetricsDaily(metrics)
}

// UpdateCatMetricsMonthly 更新猫咪月度统计
func (s *CatToiletService) UpdateCatMetricsMonthly(metrics *cat.CatMetricsMonthly) error {
	return s.catService.UpdateCatMetricsMonthly(metrics)
}

// CreateCatAlert 创建健康警报
func (s *CatToiletService) CreateCatAlert(alert *cat.CatAlert) error {
	if err := s.catService.CreateCatAlert(alert); err != nil {
		return err
	}

	// 获取猫咪信息
	cat, err := s.catService.GetCat(alert.CatID)
	if err != nil {
		return fmt.Errorf("failed to get cat info: %v", err)
	}

	// 创建通知
	notification := &notification.Notification{
		UserID:   cat.UserID,
		Type:     notification.TypeAbnormal,
		SubType:  notification.SubTypeHealth,
		Title:    alert.Title,
		Body:     alert.Description,
		Priority: int(notification.NotificationPriority(alert.Level)),
		Metadata: []byte(fmt.Sprintf(`{"cat_id":"%s","alert_id":%d}`, alert.CatID, alert.ID)),
	}

	return s.notificationService.CreateNotification(context.Background(), notification)
}

// ==================== 辅助方法 ====================

// analyzeVideo 异步分析视频
func (s *CatToiletService) analyzeVideo(record *RecordShit, userID string) (*RecordAnalysis, error) {
	// 1. 更新处理状态
	record.ProcessStage = 1 // 开始处理
	if err := s.db.UpdateRecord(record); err != nil {
		log.Printf("Failed to update record status: %v", err)
		return nil, fmt.Errorf("failed to update record status: %v", err)
	}

	// 计算视频持续时间（秒）
	var duration int
	if record.EndTime != nil {
		duration = int(*record.EndTime - record.StartTime)
	}
	log.Printf("Video duration: %d seconds", duration)

	// 2. 调用AI服务进行分析
	analysis, err := s.callAIService(record.VideoID, userID)
	if err != nil {
		log.Printf("Failed to analyze video: %v", err)
		return nil, fmt.Errorf("failed to analyze video: %v", err)
	}

	// 3. 检查是否是静态视频
	if analysis.BehaviorType == "static_video" {
		// 3.1 更新记录状态为已删除
		record.Status = 2       // 2=删除
		record.ProcessStage = 2 // 2=已完成
		record.UpdatedAt = time.Now()
		if err := s.db.UpdateRecord(record); err != nil {
			log.Printf("Failed to update record status: %v", err)
			return nil, fmt.Errorf("failed to update record status: %v", err)
		}

		// 3.2 保存分析结果
		analysis.VideoID = record.VideoID
		analysis.CreatedAt = time.Now()
		analysis.UpdatedAt = analysis.CreatedAt
		if err := s.db.CreateRecordAnalysis(analysis); err != nil {
			log.Printf("Failed to create record analysis: %v, try to update", err)
			if err := s.db.UpdateRecordAnalysis(analysis); err != nil {
				log.Printf("Failed to update record analysis: %v", err)
				return nil, fmt.Errorf("failed to update record analysis: %v", err)
			}
		}

		// 3.3 删除视频文件
		log.Printf("Delete video file: %s", record.VideoID)
		if err := s.DeleteVideo(record.VideoID); err != nil {
			log.Printf("Failed to delete video file: %v", err)
			return nil, fmt.Errorf("failed to delete video file: %v", err)
		}

		// 3.4 delete pending notification
		if err := s.notificationService.DeletePendingNotification(record.VideoID); err != nil {
			log.Printf("Failed to delete pending notification: %v", err)
			return nil, fmt.Errorf("failed to delete pending notification: %v", err)
		}

		return analysis, nil
	}

	// 4. 如果不是静态视频，继续正常处理
	// 获取原始通知内容
	originalNotification, err := s.notificationService.GetPendingNotification(record.VideoID)
	if err != nil {
		log.Printf("Failed to get pending notification: %v", err)
		return nil, fmt.Errorf("failed to get pending notification: %v", err)
	}

	// 创建元数据
	metadata := map[string]interface{}{
		"duration": duration,
		"analysis": analysis,
	}
	metadataJSON, err := json.Marshal(metadata)
	if err != nil {
		log.Printf("Failed to marshal metadata: %v", err)
		return nil, fmt.Errorf("failed to marshal metadata: %v", err)
	}

	// 格式化持续时间
	formattedDuration := utils.FormatDuration(duration)

	// 获取猫咪名称用于通知标题
	catName := "某只喵喵"
	if analysis.AnimalID != "" && analysis.AnimalID != "unknown" {
		if cat, err := s.GetCat(analysis.AnimalID); err == nil && cat != nil {
			catName = cat.Name
			log.Printf("Found cat name: %s for animal_id: %s", catName, analysis.AnimalID)
		} else {
			log.Printf("Failed to get cat info for animal_id %s: %v", analysis.AnimalID, err)
		}
	} else {
		log.Printf("Using default cat name, animal_id: %s", analysis.AnimalID)
	}

	// 创建完整的通知
	notification := &notification.Notification{
		Type:     originalNotification.Type,
		SubType:  originalNotification.SubType,
		UserID:   userID,
		Title:    fmt.Sprintf("%s：", catName),
		Body:     fmt.Sprintf("本喵便便了，用时%s，速来铲屎！", formattedDuration),
		Priority: originalNotification.Priority,
		Metadata: metadataJSON,
	}

	// 发送通知
	if err := s.notificationService.CreateNotification(context.Background(), notification); err != nil {
		log.Printf("Failed to send notification: %v", err)
		return nil, fmt.Errorf("failed to send notification: %v", err)
	}

	log.Printf("Notification sent: %v", notification)

	// 删除待处理的通知
	if err := s.notificationService.DeletePendingNotification(record.VideoID); err != nil {
		log.Printf("Failed to delete pending notification: %v", err)
		return nil, fmt.Errorf("failed to delete pending notification: %v", err)
	}

	return analysis, nil
}

// callAIService 调用AI服务分析视频
func (s *CatToiletService) callAIService(videoID, userID string) (*RecordAnalysis, error) {
	// 1. 检查视频记录是否存在
	record, err := s.GetRecordShit(videoID)
	if err != nil {
		return nil, fmt.Errorf("failed to get video record: %v", err)
	}

	// 2. 准备请求数据
	// load device timezone
	device, err := s.GetDevice(record.DeviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get device: %v", err)
	}

	// 获取用户的所有猫咪状态信息
	catStates, err := s.getUserActiveCatStates(userID)
	if err != nil {
		log.Printf("Warning: Failed to get cat states for user %s: %v", userID, err)
		catStates = []map[string]interface{}{} // 使用空数组作为默认值
	}

	// 解析时区字符串为*time.Location
	var loc *time.Location
	if device.Timezone != "" {
		loc, err = time.LoadLocation(device.Timezone)
		if err != nil {
			log.Printf("Failed to parse timezone %s: %v, using UTC", device.Timezone, err)
			loc = time.UTC
		}
	} else {
		loc = time.UTC
	}

	startTimeObj := time.Unix(record.StartTime, 0).In(loc)
	startTimeFormatted := startTimeObj.Format(time.RFC3339)

	var endTimeFormatted string
	if record.EndTime != nil {
		endTimeObj := time.Unix(*record.EndTime, 0).In(loc)
		endTimeFormatted = endTimeObj.Format(time.RFC3339)
	} else {
		endTimeFormatted = "null"
	}

	videoPath, err := s.storageService.GetVideoPath(loc, record.StartTime, record.DeviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get video path: %v", err)
	}

	// 获取静态视频判断结果（基于cat_frame_count）
	isStatic, err := s.CheckVideoStatic(record, device)
	if err != nil {
		log.Printf("Failed to check video static status for %s: %v", videoID, err)
		// 如果无法判断静态状态，继续调用caby_ai处理
	} else if isStatic {
		// 如果是静态视频，直接返回结果，不调用caby_ai
		log.Printf("Video %s is static, skipping caby_ai analysis", videoID)

		analysisResult := &RecordAnalysis{
			VideoID:       videoID,
			AnimalID:      "unknown", // 静态视频无法识别动物
			CatConfidence: 0.0,
			BehaviorType:  "static_video",
			IsAbnormal:    false,
			AbnormalType:  "",
			AbnormalProb:  0.0,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}

		return analysisResult, nil
	}

	// 只有非静态视频才调用caby_ai
	log.Printf("Video %s is non-static, calling caby_ai for analysis", videoID)

	reqBody := map[string]interface{}{
		"video_id":      videoID,
		"device_id":     record.DeviceID,
		"user_id":       userID,
		"start_time":    startTimeFormatted,
		"end_time":      endTimeFormatted,
		"weight_litter": record.WeightLitter,
		"weight_cat":    record.WeightCat,
		"weight_waste":  record.WeightWaste,
		"video_path":    videoPath,
		"cat_states":    catStates, // 添加猫咪状态信息
	}

	// 3. 获取AI服务配置
	aiServiceURL := s.cfg.CabyAI.URL + "/api/v1/analyze"
	serviceToken := s.cfg.CabyAI.AuthToken

	// 4. 设置全局超时上下文
	timeoutMinutes := s.cfg.CabyAI.TimeoutMinutes
	if timeoutMinutes <= 0 {
		timeoutMinutes = 30 // 默认30分钟
	}

	log.Printf("Setting AI service timeout to %d minutes", timeoutMinutes)

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeoutMinutes)*time.Minute)
	defer cancel()

	// 创建不带超时的HTTP客户端，超时由上下文控制
	client := &http.Client{}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %v", err)
	}

	// 5. 重试机制
	var resp *http.Response
	maxRetries := s.cfg.CabyAI.MaxRetries
	if maxRetries <= 0 {
		maxRetries = 3 // 默认至少3次重试
	}

	for i := 0; i < maxRetries; i++ {
		// 检查上下文是否已经超时
		select {
		case <-ctx.Done():
			return nil, fmt.Errorf("request timeout after %d attempts: %v", i, ctx.Err())
		default:
			// 继续处理
		}

		// 为每次请求重新创建请求体，避免请求体被消耗
		reqCopy, err := http.NewRequestWithContext(ctx, "POST", aiServiceURL, bytes.NewBuffer(jsonData))
		if err != nil {
			return nil, fmt.Errorf("failed to create request copy: %v", err)
		}

		// 复制所有请求头
		reqCopy.Header.Set("Content-Type", "application/json")
		reqCopy.Header.Set("Authorization", "Bearer "+serviceToken)

		log.Printf("Sending request to AI service, attempt %d/%d, Content-Length: %d",
			i+1, maxRetries, len(jsonData))

		resp, err = client.Do(reqCopy)
		if err == nil {
			break
		}

		// 检查是否是上下文超时导致的错误
		if ctx.Err() != nil {
			log.Printf("Request timed out: %v", ctx.Err())
			return nil, fmt.Errorf("request timed out after %d minutes: %v", timeoutMinutes, ctx.Err())
		}

		log.Printf("Request error (attempt %d/%d): %v", i+1, maxRetries, err)
		if i < maxRetries-1 {
			// 计算退避时间，但不超过10秒
			backoff := time.Duration(i+1) * time.Second
			if backoff > 10*time.Second {
				backoff = 10 * time.Second
			}

			log.Printf("Retrying in %v...", backoff)

			// 使用带超时的Sleep，以便能够尊重上下文取消
			select {
			case <-time.After(backoff):
				// 继续重试
			case <-ctx.Done():
				return nil, fmt.Errorf("request cancelled during retry delay: %v", ctx.Err())
			}
		}
	}

	// 如果所有重试都失败
	if resp == nil {
		return nil, fmt.Errorf("failed to send request after %d attempts", maxRetries)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(io.LimitReader(resp.Body, 1024))
		return nil, fmt.Errorf("AI service returned status code %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// 6. 解析响应 - 先解析为包含AiResults的临时结构体
	var tempResponse struct {
		VideoID       string    `json:"video_id"`
		AnimalID      string    `json:"animal_id"`
		CatConfidence float64   `json:"cat_confidence"`
		BehaviorType  string    `json:"behavior_type"`
		IsAbnormal    bool      `json:"is_abnormal"`
		AbnormalType  string    `json:"abnormal_type"`
		AbnormalProb  float64   `json:"abnormal_prob"`
		AiResults     string    `json:"ai_results"`
		CreatedAt     time.Time `json:"created_at"`
		UpdatedAt     time.Time `json:"updated_at"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&tempResponse); err != nil {
		return nil, fmt.Errorf("failed to decode response: %v", err)
	}

	// 7. 创建最终的分析结果
	analysis := RecordAnalysis{
		VideoID:       tempResponse.VideoID,
		AnimalID:      tempResponse.AnimalID,
		CatConfidence: tempResponse.CatConfidence,
		BehaviorType:  tempResponse.BehaviorType,
		IsAbnormal:    tempResponse.IsAbnormal,
		AbnormalType:  tempResponse.AbnormalType,
		AbnormalProb:  tempResponse.AbnormalProb,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// 8. 解析AiResults中的shadow模式结果
	if err := s.parseShadowResultFromAiResults(&analysis, tempResponse.AiResults); err != nil {
		log.Printf("Failed to parse shadow result from AiResults: %v", err)
	}

	// 9. 保存分析结果
	// 先尝试更新，如果记录不存在则插入
	if err := s.db.CreateRecordAnalysis(&analysis); err != nil {
		// 如果记录已存在，则更新
		if err := s.db.UpdateRecordAnalysis(&analysis); err != nil {
			return nil, fmt.Errorf("failed to save analysis result: %v", err)
		}
	}

	// 10. 影子模式结果处理已经由caby_ai完成，这里只需要记录日志
	if analysis.ShadowMatchedCatID != nil {
		log.Printf("Shadow mode result received for video %s: matched_cat_id=%v, similarity=%v, model_version=%v",
			videoID, analysis.ShadowMatchedCatID, analysis.ShadowSimilarity, analysis.ShadowModelVersion)
	}

	return &analysis, nil
}

// parseShadowResultFromAiResults 从AiResults中解析shadow模式结果
func (s *CatToiletService) parseShadowResultFromAiResults(analysis *RecordAnalysis, aiResultsStr string) error {
	if aiResultsStr == "" {
		return nil
	}

	// 解析AiResults JSON
	var aiResults map[string]interface{}
	if err := json.Unmarshal([]byte(aiResultsStr), &aiResults); err != nil {
		return fmt.Errorf("failed to parse AiResults JSON: %v", err)
	}

	// 提取shadow_mode_result
	shadowModeResultRaw, exists := aiResults["shadow_mode_result"]
	if !exists {
		return nil // 没有shadow结果，这是正常的
	}

	// 解析shadow_mode_result
	var shadowResult map[string]interface{}
	switch v := shadowModeResultRaw.(type) {
	case string:
		if err := json.Unmarshal([]byte(v), &shadowResult); err != nil {
			return fmt.Errorf("failed to parse shadow_mode_result string: %v", err)
		}
	case map[string]interface{}:
		shadowResult = v
	default:
		return fmt.Errorf("unexpected shadow_mode_result type: %T", v)
	}

	// 提取重要字段
	if similarity, ok := shadowResult["similarity"].(float64); ok {
		analysis.ShadowSimilarity = &similarity
	}
	if shadowResultField, ok := shadowResult["shadow_result"].(string); ok {
		analysis.ShadowMatchedCatID = &shadowResultField
	}
	if modelVersion, ok := shadowResult["model_version"].(string); ok {
		analysis.ShadowModelVersion = &modelVersion
	}

	log.Printf("Parsed shadow result for video %s: matched_cat_id=%v, similarity=%v, model_version=%v",
		analysis.VideoID, analysis.ShadowMatchedCatID, analysis.ShadowSimilarity, analysis.ShadowModelVersion)

	return nil
}

// DeviceDailyMetrics 设备每日统计数据结构
type DeviceDailyMetrics struct {
	TotalRecords  int     `json:"total_records"`
	TotalDuration int     `json:"total_duration"`
	AvgDuration   float64 `json:"avg_duration"`
	TotalCats     int     `json:"total_cats"`
	AbnormalCount int     `json:"abnormal_count"`
	StorageUsed   int64   `json:"storage_used"`
	BatteryLevel  int     `json:"battery_level"`
	NetworkUptime float64 `json:"network_uptime"`
	Date          string  `json:"date"`
}

// DeviceMonthlyMetrics 设备月度统计数据结构
type DeviceMonthlyMetrics struct {
	TotalRecords     int     `json:"total_records"`
	TotalDuration    int     `json:"total_duration"`
	AvgDuration      float64 `json:"avg_duration"`
	TotalCats        int     `json:"total_cats"`
	AbnormalCount    int     `json:"abnormal_count"`
	StorageUsed      int64   `json:"storage_used"`
	AvgBatteryLevel  float64 `json:"avg_battery_level"`
	NetworkUptime    float64 `json:"network_uptime"`
	MaintenanceCount int     `json:"maintenance_count"`
	Year             int     `json:"year"`
	Month            int     `json:"month"`
}

// ValidateHardwareSN 验证硬件SN号格式（兼容旧格式和新复合格式）
func (s *CatToiletService) ValidateHardwareSN(sn string) error {
	if sn == "" {
		return fmt.Errorf("hardware SN cannot be empty")
	}

	// 检查是否为复合格式：8位16进制_PCB_SN_时间戳
	if strings.Contains(sn, "_") {
		// 找到第一个下划线的位置
		firstUnderscoreIndex := strings.Index(sn, "_")
		if firstUnderscoreIndex != 8 {
			// 如果第一个下划线不在第8个位置，可能不是复合格式，继续其他验证
		} else {
			// 找到最后一个下划线的位置
			lastUnderscoreIndex := strings.LastIndex(sn, "_")
			if lastUnderscoreIndex == firstUnderscoreIndex {
				// 只有一个下划线，不是复合格式
			} else {
				// 分解SN号：前8位_中间部分_最后部分
				randomHex := sn[:firstUnderscoreIndex]
				pcbSN := sn[firstUnderscoreIndex+1 : lastUnderscoreIndex]
				timestampStr := sn[lastUnderscoreIndex+1:]

				// 验证第一部分：8位16进制
				if len(randomHex) != 8 {
					return fmt.Errorf("invalid composite SN format: random hex part must be 8 characters")
				}
				for _, char := range randomHex {
					if !((char >= '0' && char <= '9') || (char >= 'A' && char <= 'F') || (char >= 'a' && char <= 'f')) {
						return fmt.Errorf("invalid composite SN format: random hex part must contain only hexadecimal characters")
					}
				}

				// 验证第二部分：PCB SN（非空即可）
				if pcbSN == "" {
					return fmt.Errorf("invalid composite SN format: PCB SN part cannot be empty")
				}

				// 验证第三部分：时间戳（必须是数字）
				if len(timestampStr) < 13 {
					return fmt.Errorf("invalid composite SN format: timestamp part too short")
				}
				for _, char := range timestampStr {
					if char < '0' || char > '9' {
						return fmt.Errorf("invalid composite SN format: timestamp part must contain only digits")
					}
				}

				return nil
			}
		}
	}

	// 检查是否为旧的8位16进制格式
	if len(sn) == 8 {
		for _, char := range sn {
			if !((char >= '0' && char <= '9') || (char >= 'A' && char <= 'F') || (char >= 'a' && char <= 'f')) {
				return fmt.Errorf("invalid legacy SN format: must contain only hexadecimal characters (0-9, A-F)")
			}
		}
		return nil
	}

	// 其他长度的SN号，只要不为空就认为有效（兼容其他可能的格式）
	return nil
}

// CreateUserHardware adds a new association between user and hardware
func (s *CatToiletService) CreateUserHardware(userHardware *UserHardware) error {
	// 验证硬件SN号格式
	if err := s.ValidateHardwareSN(userHardware.HardwareSN); err != nil {
		return fmt.Errorf("invalid hardware SN: %w", err)
	}

	// Call the repository to save the data
	return s.db.CreateUserHardware(userHardware)
}

// Additional helper method to create UserHardware from a request
func (s *CatToiletService) CreateUserHardwareFromRequest(request *UserHardwareRequest) (*UserHardware, error) {
	// 验证硬件SN号格式
	if err := s.ValidateHardwareSN(request.HardwareSN); err != nil {
		return nil, fmt.Errorf("invalid hardware SN: %w", err)
	}

	// Create the UserHardware object
	userHardware := &UserHardware{
		UserID:     request.UserID,
		HardwareSN: request.HardwareSN,
		Status:     1, // Default status is active
		Remark:     request.Remark,
	}

	// Call the repository to save the data
	if err := s.db.CreateUserHardware(userHardware); err != nil {
		return nil, fmt.Errorf("failed to create user hardware: %w", err)
	}

	return userHardware, nil
}

// ListUserHardware 获取用户的硬件关联列表
func (s *CatToiletService) ListUserHardware(userID string) ([]UserHardware, error) {
	return s.db.ListUserHardware(userID)
}

// GetHardwareUser 获取硬件关联的用户
func (s *CatToiletService) GetHardwareUser(hardwareSN string) (*HardwareUserResponse, error) {
	return s.db.GetHardwareUser(hardwareSN)
}

// CheckUserHardwareExists 检查用户和硬件关联是否存在
func (s *CatToiletService) CheckUserHardwareExists(userID string, hardwareSN string) (bool, error) {
	return s.db.CheckUserHardwareExists(userID, hardwareSN)
}

// ListUsers 获取所有用户列表
func (s *CatToiletService) ListUsers() ([]User, error) {
	return s.db.ListUsers()
}

// GetCatDailyMetrics 获取猫咪每日统计
func (s *CatToiletService) GetCatDailyMetrics(catID string, date time.Time) (*cat.CatMetricsDaily, error) {
	return s.catService.GetCatDailyMetrics(catID, date)
}

// GetCatMonthlyMetrics 获取猫咪月度统计
func (s *CatToiletService) GetCatMonthlyMetrics(catID string, year, month int) (*cat.CatMetricsMonthly, error) {
	return s.catService.GetCatMonthlyMetrics(catID, year, month)
}

// ListCatAlerts 获取猫咪健康警报
func (s *CatToiletService) ListCatAlerts(catID string, status int8) ([]cat.CatAlert, error) {
	return s.catService.ListCatAlerts(catID, status)
}

func (s *CatToiletService) ListUserNotifications(userID string, isRead *bool) ([]Notification, error) {
	return s.db.ListUserNotifications(userID, isRead)
}

// RegisterClient 注册新客户端
func (s *CatToiletService) RegisterClient(req *ClientRegisterRequest) (*ClientResponse, error) {
	// 解码 base64 客户端ID
	decodedClientID, err := base64.StdEncoding.DecodeString(req.ClientID)
	if err != nil {
		return nil, fmt.Errorf("invalid client ID format: %v", err)
	}
	clientID := string(decodedClientID)

	// 检查用户是否存在
	_, err = s.db.GetUser(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("user not found: %v", err)
	}

	// 检查客户端是否已存在
	existingClient, err := s.db.GetClient(clientID)
	if err == nil {
		// 客户端已存在，更新信息
		existingClient.Name = req.Name
		existingClient.Model = req.Model
		existingClient.OSVersion = req.OSVersion
		existingClient.AppVersion = req.AppVersion
		existingClient.LastActive = time.Now()

		if err := s.db.SaveClient(existingClient); err != nil {
			return nil, fmt.Errorf("failed to update existing client: %v", err)
		}
		return s.createClientResponse(existingClient), nil
	}

	// 创建新客户端记录
	client := &Client{
		UserID:     req.UserID,
		ClientID:   clientID,
		ClientType: req.ClientType,
		Name:       req.Name,
		Model:      req.Model,
		OSVersion:  req.OSVersion,
		AppVersion: req.AppVersion,
		LastActive: time.Now(),
		Status:     1,
	}

	if err := s.db.SaveClient(client); err != nil {
		return nil, fmt.Errorf("failed to create client: %v", err)
	}

	return s.createClientResponse(client), nil
}

// 辅助方法：创建客户端响应
func (s *CatToiletService) createClientResponse(client *Client) *ClientResponse {
	return &ClientResponse{
		ID:         client.ID,
		UserID:     client.UserID,
		ClientID:   client.ClientID,
		ClientType: client.ClientType,
		Name:       client.Name,
		Model:      client.Model,
		OSVersion:  client.OSVersion,
		AppVersion: client.AppVersion,
		LastActive: client.LastActive,
		Status:     client.Status,
		CreatedAt:  client.CreatedAt,
		UpdatedAt:  client.UpdatedAt,
	}
}

// RegisterClientToken 注册客户端令牌
func (s *CatToiletService) RegisterClientToken(clientID string, req *ClientTokenRequest) error {
	// 检查客户端是否存在
	client, err := s.db.GetClient(clientID)
	if err != nil {
		return fmt.Errorf("client not found: %v", err)
	}

	token := &ClientToken{
		ClientID:    clientID,
		UserID:      client.UserID, // 从客户端记录获取用户ID
		ClientToken: req.ClientToken,
		TokenType:   req.TokenType,
		IsSandbox:   req.IsSandbox,
		Status:      1,
	}

	return s.db.SaveClientToken(token)
}

// UpdateClient 更新客户端信息
func (s *CatToiletService) UpdateClient(clientID string, req *ClientUpdateRequest) (*ClientResponse, error) {
	client, err := s.db.GetClient(clientID)
	if err != nil {
		return nil, err
	}

	if req.Name != "" {
		client.Name = req.Name
	}
	if req.Model != "" {
		client.Model = req.Model
	}
	if req.OSVersion != "" {
		client.OSVersion = req.OSVersion
	}
	if req.AppVersion != "" {
		client.AppVersion = req.AppVersion
	}
	if req.Status != nil {
		client.Status = *req.Status
	}

	if err := s.db.SaveClient(client); err != nil {
		return nil, err
	}

	return &ClientResponse{
		ID:         client.ID,
		UserID:     client.UserID,
		ClientID:   client.ClientID,
		ClientType: client.ClientType,
		Name:       client.Name,
		Model:      client.Model,
		OSVersion:  client.OSVersion,
		AppVersion: client.AppVersion,
		LastActive: client.LastActive,
		Status:     client.Status,
		CreatedAt:  client.CreatedAt,
		UpdatedAt:  client.UpdatedAt,
	}, nil
}

// GetClient 获取客户端信息
func (s *CatToiletService) GetClient(clientID string) (*ClientResponse, error) {
	client, err := s.db.GetClient(clientID)
	if err != nil {
		return nil, err
	}

	return &ClientResponse{
		ID:         client.ID,
		UserID:     client.UserID,
		ClientID:   client.ClientID,
		ClientType: client.ClientType,
		Name:       client.Name,
		Model:      client.Model,
		OSVersion:  client.OSVersion,
		AppVersion: client.AppVersion,
		LastActive: client.LastActive,
		Status:     client.Status,
		CreatedAt:  client.CreatedAt,
		UpdatedAt:  client.UpdatedAt,
	}, nil
}

// ListUserClients 获取用户的所有客户端
func (s *CatToiletService) ListUserClients(userID string) ([]ClientResponse, error) {
	clients, err := s.db.ListUserClients(userID)
	if err != nil {
		return nil, err
	}

	var response []ClientResponse
	for _, client := range clients {
		response = append(response, ClientResponse{
			ID:         client.ID,
			UserID:     client.UserID,
			ClientID:   client.ClientID,
			ClientType: client.ClientType,
			Name:       client.Name,
			Model:      client.Model,
			OSVersion:  client.OSVersion,
			AppVersion: client.AppVersion,
			LastActive: client.LastActive,
			Status:     client.Status,
			CreatedAt:  client.CreatedAt,
			UpdatedAt:  client.UpdatedAt,
		})
	}
	return response, nil
}

// UpdateClientStatus 更新客户端状态
func (s *CatToiletService) UpdateClientStatus(clientID string, req *ClientStatusRequest) error {
	return s.db.UpdateClientStatus(clientID, req.Status, req.AppVersion)
}

// DeleteClientToken 删除客户端令牌
func (s *CatToiletService) DeleteClientToken(clientID string) error {
	return s.db.DeleteClientToken(clientID)
}

// GetClientToken 获取客户端令牌信息
func (s *CatToiletService) GetClientToken(clientID string) (*ClientTokenResponse, error) {
	token, err := s.db.GetClientToken(clientID)
	if err != nil {
		return nil, err
	}

	return &ClientTokenResponse{
		ID:          token.ID,
		ClientID:    token.ClientID,
		ClientToken: token.ClientToken,
		TokenType:   token.TokenType,
		IsSandbox:   token.IsSandbox,
		Status:      token.Status,
		CreatedAt:   token.CreatedAt,
		UpdatedAt:   token.UpdatedAt,
	}, nil
}

// DeleteClient 删除客户端及其关联的令牌
func (s *CatToiletService) DeleteClient(clientID string) error {
	// 检查客户端是否存在
	_, err := s.db.GetClient(clientID)
	if err != nil {
		return fmt.Errorf("client not found: %v", err)
	}

	// 删除客户端（数据库层会同时删除关联的令牌）
	return s.db.DeleteClient(clientID)
}

// HandleClientHeartbeat 处理客户端心跳
func (s *CatToiletService) HandleClientHeartbeat(clientID string, req *ClientHeartbeatRequest) error {
	// 检查客户端是否存在
	_, err := s.db.GetClient(clientID)
	if err != nil {
		return fmt.Errorf("client not found: %v", err)
	}

	// 更新客户端状态和最后活跃时间
	return s.db.UpdateClientStatus(clientID, req.Status, req.AppVersion)
}

// CreateUserWithLogto 创建用户并绑定 Logto ID
func (s *CatToiletService) CreateUserWithLogto(logtoUserInfo *types.LogtoUserInfo) (*User, error) {
	// 检查是否已存在该 Logto ID 的用户
	existingUser, err := s.db.GetUserByLogtoID(logtoUserInfo.Sub)
	if err == nil {
		return existingUser, nil // 用户已存在，直接返回
	}

	// 创建新用户
	user := &User{
		UserID:   algo.GenerateUserID(), // 生成新的用户 ID
		LogtoID:  logtoUserInfo.Sub,     // 绑定 Logto ID
		Username: logtoUserInfo.Email,   // 使用邮箱作为用户名
		Email:    logtoUserInfo.Email,
		Nickname: logtoUserInfo.Name,
		Status:   1, // 正常状态
	}

	// 保存到数据库
	if err := s.db.CreateUser(user); err != nil {
		return nil, fmt.Errorf("failed to create user: %v", err)
	}

	// 创建默认的 unknown 猫咪
	if err := s.catService.CreateDefaultUnknownCat(user.UserID); err != nil {
		log.Printf("Failed to create default unknown cat for user %s: %v", user.UserID, err)
		// 不返回错误，因为这不是致命错误
	}

	// 创建默认的影子模式配置
	if err := s.createDefaultUserShadowConfig(user.UserID); err != nil {
		log.Printf("Failed to create default shadow mode config for user %s: %v", user.UserID, err)
		// 不返回错误，因为这不是致命错误
	}

	return user, nil
}

// GetUserByLogtoID 通过 Logto ID 获取用户
func (s *CatToiletService) GetUserByLogtoID(logtoID string) (*User, error) {
	return s.db.GetUserByLogtoID(logtoID)
}

// UpdateUser 更新用户信息
func (s *CatToiletService) UpdateUser(user *User) error {
	// 验证用户ID
	if user.UserID == "" {
		return errors.New("user ID is required")
	}

	// 检查用户是否存在
	existingUser, err := s.db.GetUser(user.UserID)
	if err != nil {
		return err
	}

	// 保留不应该被更新的字段
	user.PasswordHash = existingUser.PasswordHash // 保持原密码不变
	user.LogtoID = existingUser.LogtoID           // 保持 LogtoID 不变
	user.CreatedAt = existingUser.CreatedAt       // 保持创建时间不变
	user.Status = existingUser.Status             // 保持状态不变

	// 更新用户信息
	return s.db.UpdateUser(user)
}

// EnsureNotificationSettings checks if notification settings exist for a user and creates them with defaults if not
func (s *CatToiletService) EnsureNotificationSettings(userID string) error {
	settings, err := s.db.GetNotificationSettings(userID)
	if err != nil {
		// Check if it's a "record not found" error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Create default settings
			settings = &NotificationSettings{
				UserID:          userID,
				EnableDaily:     true,
				EnableStats:     true,
				QuietHoursStart: 22,
				QuietHoursEnd:   7,
			}
			return s.db.CreateNotificationSettings(settings)
		}
		return err // Return other errors
	}
	return nil // Settings already exist
}

// DeleteVideo 删除视频文件
func (s *CatToiletService) DeleteVideo(videoID string) error {
	// 1. 获取视频记录以获取设备ID
	record, err := s.db.GetRecordShit(videoID)
	if err != nil {
		return fmt.Errorf("failed to get video record: %v", err)
	}

	// 2. 构建视频存储路径
	// 格式: device{deviceID}/{timestamp}_hls/playlist.m3u8
	startTimeFormatted := time.Unix(record.StartTime, 0).Format("2006-01-02_15-04-05")
	videoPath := fmt.Sprintf("device%s/%s_hls", record.DeviceID, startTimeFormatted)

	// 3. 删除视频文件
	if err := s.storageService.DeleteObject(videoPath); err != nil {
		return fmt.Errorf("failed to delete video file: %v", err)
	}
	log.Printf("Deleted video file: %s", videoPath)

	return nil
}

// UpdateRecord 更新视频记录
func (s *CatToiletService) UpdateRecord(record *RecordShit) error {
	return s.db.UpdateRecord(record)
}

// CreateRecordAnalysis 创建视频分析记录
func (s *CatToiletService) CreateRecordAnalysis(analysis *RecordAnalysis) error {
	return s.db.CreateRecordAnalysis(analysis)
}

// GetRecordAnalysis 获取视频分析结果
func (s *CatToiletService) GetRecordAnalysis(videoID string) (*RecordAnalysis, error) {
	return s.db.GetRecordAnalysis(videoID)
}

// ListAllRecords 获取所有视频记录
func (s *CatToiletService) ListAllRecords() ([]RecordShit, error) {
	return s.db.ListAllRecords()
}

// ListDeviceRecordsByUnixTime 通过Unix时间戳获取设备记录，只返回状态正常(status=1)的记录
func (s *CatToiletService) ListDeviceRecordsByUnixTime(deviceID string, startUnixTime, endUnixTime int64) ([]RecordShit, error) {
	return s.db.ListDeviceRecordsByUnixTime(deviceID, startUnixTime, endUnixTime)
}

// UpdateDeviceStatus 更新设备状态
func (s *CatToiletService) UpdateDeviceStatus(req *DeviceStatusRequest) error {
	// 1. 校验设备ID
	if req.DeviceID == "" {
		return fmt.Errorf("device ID is required")
	}

	// 2. 检查设备是否存在
	device, err := s.db.GetDevice(req.DeviceID)
	if err != nil {
		return fmt.Errorf("device not found: %v", err)
	}

	// 3. 更新设备状态
	if err := s.db.SaveDeviceStatusRequest(req); err != nil {
		return fmt.Errorf("failed to save device status: %v", err)
	}

	// 4. 更新设备的最后活跃时间和心跳时间
	now := time.Now()
	device.LastActive = &now
	device.LastHeartbeat = &now // 确保更新LastHeartbeat字段

	if err := s.db.UpdateDevice(device); err != nil {
		log.Printf("Warning: failed to update device last_active and last_heartbeat: %v", err)
	}

	return nil
}

// UpdateDeviceFirmwareVersion 更新设备固件版本
func (s *CatToiletService) UpdateDeviceFirmwareVersion(deviceID, firmwareVersion string) error {
	// 1. 校验参数
	if deviceID == "" {
		return fmt.Errorf("device ID is required")
	}
	if firmwareVersion == "" {
		return fmt.Errorf("firmware version is required")
	}

	// 2. 获取设备
	device, err := s.db.GetDevice(deviceID)
	if err != nil {
		return fmt.Errorf("device not found: %v", err)
	}

	// 3. 更新固件版本
	device.FirmwareVersion = firmwareVersion

	if err := s.db.UpdateDevice(device); err != nil {
		return fmt.Errorf("failed to update device firmware version: %v", err)
	}

	log.Printf("Device %s firmware version updated to %s", deviceID, firmwareVersion)
	return nil
}

// UpdateDeviceBasicInfo 更新设备基本信息
func (s *CatToiletService) UpdateDeviceBasicInfo(deviceID string, req *UpdateDeviceBasicInfoRequest) (*UpdateDeviceBasicInfoResponse, error) {
	// 1. 校验设备ID
	if deviceID == "" {
		return nil, fmt.Errorf("device ID is required")
	}

	// 2. 获取设备
	device, err := s.db.GetDevice(deviceID)
	if err != nil {
		return nil, fmt.Errorf("device not found: %v", err)
	}

	// 3. 更新设备信息（只更新提供的字段）
	updated := false
	if req.Name != nil && *req.Name != device.Name {
		device.Name = *req.Name
		updated = true
	}
	if req.Model != nil && *req.Model != device.Model {
		device.Model = *req.Model
		updated = true
	}
	if req.Timezone != nil && *req.Timezone != device.Timezone {
		device.Timezone = *req.Timezone
		updated = true
	}
	if req.FirmwareVersion != nil && *req.FirmwareVersion != device.FirmwareVersion {
		device.FirmwareVersion = *req.FirmwareVersion
		updated = true
	}
	if req.Status != nil && *req.Status != device.Status {
		device.Status = *req.Status
		updated = true
	}

	// 4. 如果有更新，保存到数据库
	if updated {
		if err := s.db.UpdateDevice(device); err != nil {
			return nil, fmt.Errorf("failed to update device basic info: %v", err)
		}
		log.Printf("Device %s basic info updated", deviceID)
	}

	// 5. 构造响应
	response := &UpdateDeviceBasicInfoResponse{
		DeviceID:        device.DeviceID,
		UserID:          device.UserID,
		HardwareSN:      device.HardwareSN,
		Name:            device.Name,
		Model:           device.Model,
		Timezone:        device.Timezone,
		FirmwareVersion: device.FirmwareVersion,
		Status:          device.Status,
		LastHeartbeat:   device.LastHeartbeat,
		LastActive:      device.LastActive,
		CreatedAt:       device.CreatedAt,
		UpdatedAt:       device.UpdatedAt,
	}

	return response, nil
}

// StartDeviceStatusMonitor 启动设备状态监控服务
func (s *CatToiletService) StartDeviceStatusMonitor() {
	// 获取配置
	heartbeatInterval := s.cfg.DeviceConfig.HeartbeatInterval
	if heartbeatInterval <= 0 {
		heartbeatInterval = 15 // 默认15分钟
	}

	statusTimeout := s.cfg.DeviceConfig.StatusTimeout
	if statusTimeout <= 0 {
		statusTimeout = 30 // 默认30分钟
	}

	statisticsWindow := s.cfg.DeviceConfig.StatisticsWindow
	if statisticsWindow <= 0 {
		statisticsWindow = 24 // 默认24小时
	}

	// 1. 定期检查设备在线状态并更新
	go s.runDeviceStatusChecker(time.Duration(heartbeatInterval)*time.Minute, time.Duration(statusTimeout)*time.Minute)

	// 2. 定期计算设备统计数据
	go s.runDeviceStatisticsCalculator(time.Duration(statisticsWindow) * time.Hour)

	// 3. 定期清理过期的历史记录
	go s.runDeviceHistoryCleaner(time.Duration(statisticsWindow) * time.Hour)

	log.Printf("Device status monitor started with settings: heartbeat=%dm, timeout=%dm, stats_window=%dh",
		heartbeatInterval, statusTimeout, statisticsWindow)
}

// runDeviceStatusChecker 运行设备状态检查器
func (s *CatToiletService) runDeviceStatusChecker(interval, timeout time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.checkAllDeviceStatuses(timeout)
		}
	}
}

// checkAllDeviceStatuses 检查所有设备的状态
func (s *CatToiletService) checkAllDeviceStatuses(timeout time.Duration) {
	// 1. 获取所有设备
	var devices []Device
	err := s.db.db.Find(&devices).Error
	if err != nil {
		log.Printf("Failed to get devices for status check: %v", err)
		return
	}

	log.Printf("Checking status for %d devices", len(devices))

	// 2. 检查每个设备的状态
	for _, device := range devices {
		// 检查设备是否在线（基于最后心跳时间）
		online := false
		if device.LastHeartbeat != nil {
			online = time.Since(*device.LastHeartbeat) <= timeout
		}

		// 获取当前设备状态
		var status DeviceStatus
		err := s.db.db.Where("device_id = ?", device.DeviceID).First(&status).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				// 创建新的设备状态记录
				status = DeviceStatus{
					DeviceID: device.DeviceID,
					Online:   online,
				}
				if err := s.db.db.Create(&status).Error; err != nil {
					log.Printf("Failed to create device status for %s: %v", device.DeviceID, err)
				}
			} else {
				log.Printf("Failed to get device status for %s: %v", device.DeviceID, err)
			}
			continue
		}

		// 仅当在线状态改变时更新记录
		if status.Online != online {
			status.Online = online
			status.UpdatedAt = time.Now()
			if err := s.db.db.Save(&status).Error; err != nil {
				log.Printf("Failed to update device status for %s: %v", device.DeviceID, err)
			} else {
				log.Printf("Device %s status updated to %v", device.DeviceID, online)
			}
		}
	}
}

// runDeviceStatisticsCalculator 运行设备统计数据计算器
func (s *CatToiletService) runDeviceStatisticsCalculator(statsWindow time.Duration) {
	// 每小时计算一次统计数据
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.calculateAllDeviceStatistics(statsWindow)
		}
	}
}

// calculateAllDeviceStatistics 计算所有设备的统计数据
func (s *CatToiletService) calculateAllDeviceStatistics(statsWindow time.Duration) {
	// 1. 获取所有设备
	var devices []Device
	err := s.db.db.Find(&devices).Error
	if err != nil {
		log.Printf("Failed to get devices for statistics calculation: %v", err)
		return
	}

	log.Printf("Calculating statistics for %d devices", len(devices))

	// 2. 计算每个设备的统计数据
	now := time.Now()
	startTime := now.Add(-statsWindow)

	for _, device := range devices {
		// 获取设备状态历史
		var history []DeviceStatusHistory
		err := s.db.db.Where("device_id = ? AND created_at >= ?", device.DeviceID, startTime).
			Order("created_at ASC").
			Find(&history).Error
		if err != nil {
			log.Printf("Failed to get history for device %s: %v", device.DeviceID, err)
			continue
		}

		if len(history) == 0 {
			log.Printf("No history data for device %s in the last %v", device.DeviceID, statsWindow)
			continue
		}

		// 计算统计数据
		onlineCount := 0
		var signalStrengthSum int32 = 0
		var storageUsageSum int32 = 0

		for _, record := range history {
			if record.Online {
				onlineCount++
			}
			signalStrengthSum += int32(record.SignalStrength)
			storageUsageSum += int32(record.StorageUsage)
		}

		// 计算百分比
		onlineRate := int8((float64(onlineCount) / float64(len(history))) * 100)
		signalStrengthAvg := int8(0)
		if len(history) > 0 {
			signalStrengthAvg = int8(signalStrengthSum / int32(len(history)))
		}

		storageUsageAvg := int8(0)
		if len(history) > 0 {
			storageUsageAvg = int8(storageUsageSum / int32(len(history)))
		}

		// 获取或创建设备统计记录
		var stats DeviceStatusStatistics
		err = s.db.db.Where("device_id = ?", device.DeviceID).First(&stats).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				stats = DeviceStatusStatistics{
					DeviceID:          device.DeviceID,
					OnlineRate:        onlineRate,
					SignalStrengthAvg: signalStrengthAvg,
					StorageUsage:      storageUsageAvg,
				}
				if err := s.db.db.Create(&stats).Error; err != nil {
					log.Printf("Failed to create statistics for device %s: %v", device.DeviceID, err)
				}
			} else {
				log.Printf("Failed to get statistics for device %s: %v", device.DeviceID, err)
			}
			continue
		}

		// 更新统计记录
		stats.OnlineRate = onlineRate
		stats.SignalStrengthAvg = signalStrengthAvg
		stats.StorageUsage = storageUsageAvg
		if err := s.db.db.Save(&stats).Error; err != nil {
			log.Printf("Failed to update statistics for device %s: %v", device.DeviceID, err)
		} else {
			log.Printf("Updated statistics for device %s: online_rate=%d%%, signal_avg=%d, storage=%d%%",
				device.DeviceID, onlineRate, signalStrengthAvg, storageUsageAvg)
		}
	}
}

// runDeviceHistoryCleaner 运行设备历史记录清理器
func (s *CatToiletService) runDeviceHistoryCleaner(retentionPeriod time.Duration) {
	// 每天清理一次历史数据
	ticker := time.NewTicker(24 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.cleanDeviceStatusHistory(retentionPeriod)
		}
	}
}

// cleanDeviceStatusHistory 清理过期的设备状态历史记录
func (s *CatToiletService) cleanDeviceStatusHistory(retentionPeriod time.Duration) {
	cutoffTime := time.Now().Add(-retentionPeriod)

	result := s.db.db.Where("created_at < ?", cutoffTime).Delete(&DeviceStatusHistory{})
	if result.Error != nil {
		log.Printf("Failed to clean device status history: %v", result.Error)
	} else {
		log.Printf("Cleaned %d device status history records older than %v", result.RowsAffected, cutoffTime)
	}
}

// IsDeviceOnline 检查设备是否在线
func (s *CatToiletService) IsDeviceOnline(deviceID string) (bool, error) {
	// 获取配置
	statusTimeout := s.cfg.DeviceConfig.StatusTimeout
	if statusTimeout <= 0 {
		statusTimeout = 30 // 默认30分钟
	}

	// 获取设备以检查最后心跳时间
	device, err := s.db.GetDevice(deviceID)
	if err != nil {
		return false, fmt.Errorf("failed to get device: %v", err)
	}

	// 检查设备是否有心跳记录
	if device.LastHeartbeat == nil {
		return false, nil
	}

	// 计算心跳时间与当前时间的差值，并与超时时间比较
	timeoutDuration := time.Duration(statusTimeout) * time.Minute
	timeSinceLastHeartbeat := time.Since(*device.LastHeartbeat)

	// 如果自上次心跳以来的时间小于等于超时时间，则设备在线
	return timeSinceLastHeartbeat <= timeoutDuration, nil
}

// GetDeviceStatusWithStatistics 获取设备状态和统计数据
func (s *CatToiletService) GetDeviceStatusWithStatistics(deviceID string) (map[string]interface{}, error) {
	// 获取设备信息
	device, err := s.db.GetDevice(deviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get device: %v", err)
	}

	// 获取设备状态
	status, err := s.db.GetDeviceStatus(deviceID)
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("failed to get device status: %v", err)
		}
		// 如果没有状态记录，创建一个空的
		status = &DeviceStatus{
			DeviceID: deviceID,
			Online:   false,
		}
	}

	// 检查设备是否在线（基于心跳时间）
	isOnline, _ := s.IsDeviceOnline(deviceID)
	status.Online = isOnline

	// 获取设备统计数据
	stats, err := s.db.GetDeviceStatusStatistics(deviceID)
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to get device statistics: %v", err)
	}

	// 如果没有统计数据，创建一个空的
	if stats == nil {
		stats = &DeviceStatusStatistics{
			DeviceID: deviceID,
		}
	}

	// 组装返回数据
	result := map[string]interface{}{
		"device_id":         deviceID,
		"name":              device.Name,
		"model":             device.Model,
		"firmware":          device.FirmwareVersion,
		"timezone":          device.Timezone,
		"online":            status.Online,
		"last_heartbeat":    device.LastHeartbeat,
		"ipv4":              status.IPv4,
		"ipv6":              status.IPv6,
		"status_updated_at": status.UpdatedAt,
		"statistics": map[string]interface{}{
			"online_rate":         stats.OnlineRate,
			"power_supply_rate":   stats.PowerSupplyRate,
			"signal_strength_avg": stats.SignalStrengthAvg,
			"storage_usage":       stats.StorageUsage,
			"stats_updated_at":    stats.UpdatedAt,
		},
	}

	return result, nil
}

// GetDeviceAutoOTASettings 获取设备自动OTA更新设置
func (s *CatToiletService) GetDeviceAutoOTASettings(deviceID string) (*DeviceAllSettings, error) {
	// 验证设备ID
	if deviceID == "" {
		return nil, fmt.Errorf("device ID is required")
	}

	// 检查设备是否存在
	_, err := s.db.GetDevice(deviceID)
	if err != nil {
		return nil, fmt.Errorf("device not found: %v", err)
	}

	// 获取设备自动OTA更新设置
	return s.db.GetDeviceAutoOTASettings(deviceID)
}

// UpdateDeviceAutoOTASettings 更新设备自动OTA更新设置
func (s *CatToiletService) UpdateDeviceAutoOTASettings(deviceID string, autoOTAUpgrade string) error {
	// 验证设备ID
	if deviceID == "" {
		return fmt.Errorf("device ID is required")
	}

	// 验证autoOTAUpgrade值
	if autoOTAUpgrade != "on" && autoOTAUpgrade != "off" {
		return fmt.Errorf("invalid auto_ota_upgrade value, must be 'on' or 'off'")
	}

	// 检查设备是否存在
	_, err := s.db.GetDevice(deviceID)
	if err != nil {
		return fmt.Errorf("device not found: %v", err)
	}

	// 更新设备自动OTA更新设置
	return s.db.UpdateDeviceAutoOTASettings(deviceID, autoOTAUpgrade)
}

// UpdateDeviceIdleUpdateTime 更新设备闲时更新时间设置
func (s *CatToiletService) UpdateDeviceIdleUpdateTime(deviceID string, startHour, endHour int8) error {
	// 验证设备ID
	if deviceID == "" {
		return fmt.Errorf("device ID is required")
	}

	// 验证时间参数
	if startHour < 0 || startHour > 23 {
		return fmt.Errorf("invalid start hour, must be between 0 and 23")
	}
	if endHour < 0 || endHour > 23 {
		return fmt.Errorf("invalid end hour, must be between 0 and 23")
	}

	// 检查设备是否存在
	_, err := s.db.GetDevice(deviceID)
	if err != nil {
		return fmt.Errorf("device not found: %v", err)
	}

	// 更新设备闲时更新时间设置
	return s.db.UpdateDeviceIdleUpdateTime(deviceID, startHour, endHour)
}

// UpdateDeviceAllSettings 更新设备所有设置
func (s *CatToiletService) UpdateDeviceAllSettings(deviceID string, settings *DeviceAllSettings) error {
	// 验证设备ID
	if deviceID == "" {
		return fmt.Errorf("device ID is required")
	}

	// 验证设置参数
	if settings.AutoOTAUpgrade != "" && settings.AutoOTAUpgrade != "on" && settings.AutoOTAUpgrade != "off" {
		return fmt.Errorf("invalid auto_ota_upgrade value, must be 'on' or 'off'")
	}

	if settings.IdleUpdateStartHour < 0 || settings.IdleUpdateStartHour > 23 {
		return fmt.Errorf("invalid idle update start hour, must be between 0 and 23")
	}
	if settings.IdleUpdateEndHour < 0 || settings.IdleUpdateEndHour > 23 {
		return fmt.Errorf("invalid idle update end hour, must be between 0 and 23")
	}

	// 检查设备是否存在
	_, err := s.db.GetDevice(deviceID)
	if err != nil {
		return fmt.Errorf("device not found: %v", err)
	}

	// 更新设备所有设置
	return s.db.UpdateDeviceAllSettings(deviceID, settings)
}

// ==================== 家庭组相关方法 ====================

// CreateFamilyGroup 创建家庭组
func (s *CatToiletService) CreateFamilyGroup(userID string, req *FamilyGroupCreateRequest) (*FamilyGroup, error) {
	// 检查用户是否存在
	_, err := s.db.GetUser(userID)
	if err != nil {
		return nil, fmt.Errorf("用户不存在: %v", err)
	}

	// 生成家庭组ID
	groupID := algo.GenerateFamilyGroupID(userID)

	// 创建家庭组对象
	group := &FamilyGroup{
		GroupID:     groupID,
		GroupName:   req.GroupName,
		OwnerID:     userID,
		Description: req.Description,
		MaxMembers:  8, // 默认最大成员数
	}

	// 调用数据库方法创建家庭组
	if err := s.db.CreateFamilyGroup(group); err != nil {
		return nil, fmt.Errorf("创建家庭组失败: %v", err)
	}

	return group, nil
}

// GetFamilyGroup 获取家庭组信息
func (s *CatToiletService) GetFamilyGroup(userID, groupID string) (*FamilyGroupWithDetails, error) {
	// 检查用户是否在家庭组中
	inGroup, _, err := s.db.CheckUserInFamilyGroup(userID, groupID)
	if err != nil {
		return nil, fmt.Errorf("检查用户权限失败: %v", err)
	}
	if !inGroup {
		return nil, fmt.Errorf("您不是该家庭组的成员，无权查看")
	}

	// 获取家庭组详情
	groupDetails, err := s.db.GetFamilyGroupWithDetails(groupID)
	if err != nil {
		return nil, fmt.Errorf("获取家庭组详情失败: %v", err)
	}

	return groupDetails, nil
}

// ListUserFamilyGroups 获取用户所有的家庭组
func (s *CatToiletService) ListUserFamilyGroups(userID string) ([]FamilyGroupResponse, error) {
	// 检查用户是否存在
	_, err := s.db.GetUser(userID)
	if err != nil {
		return nil, fmt.Errorf("用户不存在: %v", err)
	}

	// 获取用户所有家庭组
	groups, err := s.db.ListUserFamilyGroups(userID)
	if err != nil {
		return nil, fmt.Errorf("查询用户家庭组失败: %v", err)
	}

	// 拼装响应数据
	var response []FamilyGroupResponse
	for _, group := range groups {
		// 获取成员数量
		members, err := s.db.ListFamilyGroupMembers(group.GroupID)
		if err != nil {
			continue
		}

		// 获取设备数量
		devices, err := s.db.ListFamilyGroupDevices(group.GroupID)
		if err != nil {
			continue
		}

		response = append(response, FamilyGroupResponse{
			GroupID:     group.GroupID,
			GroupName:   group.GroupName,
			OwnerID:     group.OwnerID,
			Description: group.Description,
			MaxMembers:  group.MaxMembers,
			MemberCount: len(members),
			DeviceCount: len(devices),
			CreatedAt:   group.CreatedAt,
			UpdatedAt:   group.UpdatedAt,
		})
	}

	return response, nil
}

// UpdateFamilyGroup 更新家庭组信息
func (s *CatToiletService) UpdateFamilyGroup(userID, groupID string, req *FamilyGroupUpdateRequest) (*FamilyGroup, error) {
	// 检查用户是否有权限更新家庭组
	inGroup, role, err := s.db.CheckUserInFamilyGroup(userID, groupID)
	if err != nil {
		return nil, fmt.Errorf("检查用户权限失败: %v", err)
	}
	if !inGroup {
		return nil, fmt.Errorf("您不是该家庭组的成员，无权更新")
	}
	if role != FamilyMemberRoleOwner && role != FamilyMemberRoleAdmin {
		return nil, fmt.Errorf("您没有权限更新家庭组信息，需要管理员或拥有者权限")
	}

	// 获取现有家庭组信息
	group, err := s.db.GetFamilyGroup(groupID)
	if err != nil {
		return nil, fmt.Errorf("获取家庭组信息失败: %v", err)
	}

	// 更新字段
	if req.GroupName != "" {
		group.GroupName = req.GroupName
	}
	if req.Description != "" {
		group.Description = req.Description
	}

	// 保存更新
	if err := s.db.UpdateFamilyGroup(group); err != nil {
		return nil, fmt.Errorf("更新家庭组失败: %v", err)
	}

	return group, nil
}

// DeleteFamilyGroup 删除家庭组
func (s *CatToiletService) DeleteFamilyGroup(userID, groupID string) error {
	// 检查用户是否是家庭组拥有者
	inGroup, role, err := s.db.CheckUserInFamilyGroup(userID, groupID)
	if err != nil {
		return fmt.Errorf("检查用户权限失败: %v", err)
	}
	if !inGroup {
		return fmt.Errorf("您不是该家庭组的成员，无权删除")
	}
	if role != FamilyMemberRoleOwner {
		return fmt.Errorf("只有家庭组拥有者可以删除家庭组")
	}

	// 删除家庭组
	if err := s.db.DeleteFamilyGroup(groupID); err != nil {
		return fmt.Errorf("删除家庭组失败: %v", err)
	}

	return nil
}

// AddFamilyGroupMember 添加家庭组成员
func (s *CatToiletService) AddFamilyGroupMember(userID, groupID string, req *FamilyGroupMemberAddRequest) (*FamilyGroupMember, error) {
	// 检查操作者是否有权添加成员
	inGroup, role, err := s.db.CheckUserInFamilyGroup(userID, groupID)
	if err != nil {
		return nil, fmt.Errorf("检查用户权限失败: %v", err)
	}
	if !inGroup {
		return nil, fmt.Errorf("您不是该家庭组的成员，无权添加成员")
	}
	if role != FamilyMemberRoleOwner && role != FamilyMemberRoleAdmin {
		return nil, fmt.Errorf("您没有权限添加成员，需要管理员或拥有者权限")
	}

	// 检查被添加的用户是否存在
	_, err = s.db.GetUser(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("要添加的用户不存在: %v", err)
	}

	// 设置角色权限（不允许普通用户创建拥有者）
	if req.Role == FamilyMemberRoleOwner && role != FamilyMemberRoleOwner {
		return nil, fmt.Errorf("只有家庭组拥有者可以设置其他用户为拥有者")
	}

	// 如果没有指定昵称，使用用户名
	nickname := req.Nickname
	if nickname == "" {
		user, _ := s.db.GetUser(req.UserID)
		if user != nil {
			nickname = user.Username
		}
	}

	// 创建成员对象
	member := &FamilyGroupMember{
		GroupID:  groupID,
		UserID:   req.UserID,
		Nickname: nickname,
		Role:     req.Role,
	}

	// 添加成员
	if err := s.db.AddFamilyGroupMember(member); err != nil {
		return nil, fmt.Errorf("添加家庭组成员失败: %v", err)
	}

	return member, nil
}

// UpdateFamilyGroupMember 更新家庭组成员
func (s *CatToiletService) UpdateFamilyGroupMember(userID, groupID, targetUserID string, req *FamilyGroupMemberUpdateRequest) (*FamilyGroupMember, error) {
	// 检查操作者权限
	inGroup, role, err := s.db.CheckUserInFamilyGroup(userID, groupID)
	if err != nil {
		return nil, fmt.Errorf("检查用户权限失败: %v", err)
	}
	if !inGroup {
		return nil, fmt.Errorf("您不是该家庭组的成员，无权更新成员信息")
	}

	// 获取目标成员信息
	targetMember, err := s.db.GetFamilyGroupMember(groupID, targetUserID)
	if err != nil {
		return nil, fmt.Errorf("获取成员信息失败: %v", err)
	}

	// 权限检查：只有自己或更高权限的人才能修改
	if userID != targetUserID { // 如果不是修改自己
		if role < targetMember.Role { // 如果操作者权限低于目标成员
			return nil, fmt.Errorf("您没有权限修改该成员信息")
		}

		// 拥有者角色只能由拥有者设置
		if req.Role == FamilyMemberRoleOwner && role != FamilyMemberRoleOwner {
			return nil, fmt.Errorf("只有家庭组拥有者可以设置其他用户为拥有者")
		}
	} else {
		// 自己不能修改自己的角色
		if req.Role != 0 && req.Role != targetMember.Role {
			return nil, fmt.Errorf("您不能修改自己的角色")
		}
	}

	// 更新字段
	if req.Nickname != "" {
		targetMember.Nickname = req.Nickname
	}
	if req.Role != 0 {
		targetMember.Role = req.Role
	}

	// 保存更新
	if err := s.db.UpdateFamilyGroupMember(targetMember); err != nil {
		return nil, fmt.Errorf("更新家庭组成员失败: %v", err)
	}

	return targetMember, nil
}

// RemoveFamilyGroupMember 移除家庭组成员
func (s *CatToiletService) RemoveFamilyGroupMember(userID, groupID, targetUserID string) error {
	// 操作者是自己或有权限的人
	isSelf := userID == targetUserID

	if !isSelf {
		// 检查操作者权限
		inGroup, role, err := s.db.CheckUserInFamilyGroup(userID, groupID)
		if err != nil {
			return fmt.Errorf("检查用户权限失败: %v", err)
		}
		if !inGroup {
			return fmt.Errorf("您不是该家庭组的成员，无权移除成员")
		}

		// 获取目标成员信息
		targetMember, err := s.db.GetFamilyGroupMember(groupID, targetUserID)
		if err != nil {
			return fmt.Errorf("获取成员信息失败: %v", err)
		}

		// 检查权限：只有更高权限的人才能移除
		if role <= targetMember.Role && role != FamilyMemberRoleOwner {
			return fmt.Errorf("您没有权限移除该成员")
		}
	}

	// 移除成员
	return s.db.RemoveFamilyGroupMember(groupID, targetUserID)
}

// AddFamilyGroupDevice 添加设备到家庭组
func (s *CatToiletService) AddFamilyGroupDevice(userID, groupID string, req *FamilyGroupDeviceAddRequest) (*FamilyGroupDevice, error) {
	// 检查用户权限
	inGroup, role, err := s.db.CheckUserInFamilyGroup(userID, groupID)
	if err != nil {
		return nil, fmt.Errorf("检查用户权限失败: %v", err)
	}
	if !inGroup {
		return nil, fmt.Errorf("您不是该家庭组的成员，无权添加设备")
	}
	if role != FamilyMemberRoleOwner && role != FamilyMemberRoleAdmin {
		return nil, fmt.Errorf("您没有权限添加设备，需要管理员或拥有者权限")
	}

	// 检查设备是否存在
	_, err = s.db.GetDevice(req.DeviceID)
	if err != nil {
		return nil, fmt.Errorf("设备不存在: %v", err)
	}

	// 创建设备关联
	device := &FamilyGroupDevice{
		GroupID:  groupID,
		DeviceID: req.DeviceID,
		AddedBy:  userID,
	}

	// 添加设备到家庭组
	if err := s.db.AddFamilyGroupDevice(device); err != nil {
		return nil, fmt.Errorf("添加设备到家庭组失败: %v", err)
	}

	return device, nil
}

// RemoveFamilyGroupDevice 从家庭组移除设备
func (s *CatToiletService) RemoveFamilyGroupDevice(userID, groupID, deviceID string) error {
	// 检查用户权限
	inGroup, role, err := s.db.CheckUserInFamilyGroup(userID, groupID)
	if err != nil {
		return fmt.Errorf("检查用户权限失败: %v", err)
	}
	if !inGroup {
		return fmt.Errorf("您不是该家庭组的成员，无权移除设备")
	}
	if role != FamilyMemberRoleOwner && role != FamilyMemberRoleAdmin {
		return fmt.Errorf("您没有权限移除设备，需要管理员或拥有者权限")
	}

	// 检查设备是否在组内
	_, err = s.db.GetFamilyGroupDevice(groupID, deviceID)
	if err != nil {
		return fmt.Errorf("设备不在该家庭组中: %v", err)
	}

	// 移除设备
	return s.db.RemoveFamilyGroupDevice(groupID, deviceID)
}

// ListFamilyGroupMembers 获取家庭组所有成员
func (s *CatToiletService) ListFamilyGroupMembers(userID, groupID string) ([]FamilyGroupMember, error) {
	// 检查用户是否在家庭组中
	inGroup, _, err := s.db.CheckUserInFamilyGroup(userID, groupID)
	if err != nil {
		return nil, fmt.Errorf("检查用户权限失败: %v", err)
	}
	if !inGroup {
		return nil, fmt.Errorf("您不是该家庭组的成员，无权查看成员列表")
	}

	// 获取成员列表
	members, err := s.db.ListFamilyGroupMembers(groupID)
	if err != nil {
		return nil, fmt.Errorf("获取家庭组成员列表失败: %v", err)
	}

	// 关联用户信息
	for i := range members {
		user, err := s.db.GetUser(members[i].UserID)
		if err == nil {
			members[i].UserInfo = user
		}
	}

	return members, nil
}

// ListFamilyGroupDevices 获取家庭组所有设备
func (s *CatToiletService) ListFamilyGroupDevices(userID, groupID string) ([]FamilyGroupDevice, error) {
	// 检查用户是否在家庭组中
	inGroup, _, err := s.db.CheckUserInFamilyGroup(userID, groupID)
	if err != nil {
		return nil, fmt.Errorf("检查用户权限失败: %v", err)
	}
	if !inGroup {
		return nil, fmt.Errorf("您不是该家庭组的成员，无权查看设备列表")
	}

	// 获取设备列表
	devices, err := s.db.ListFamilyGroupDevices(groupID)
	if err != nil {
		return nil, fmt.Errorf("获取家庭组设备列表失败: %v", err)
	}

	// 关联设备信息
	for i := range devices {
		device, err := s.db.GetDevice(devices[i].DeviceID)
		if err == nil {
			devices[i].DeviceInfo = device
		}
	}

	return devices, nil
}

// checkUserDeviceAccessInternal 检查用户是否有权限访问设备（包括通过家庭组）
// 返回布尔值表示是否有权限，及可能的错误
func (s *CatToiletService) checkUserDeviceAccessInternal(userID, deviceID string) (bool, error) {
	// 检查用户是否直接拥有设备
	device, err := s.db.GetDevice(deviceID)
	if err != nil {
		return false, fmt.Errorf("device not found: %v", err)
	}

	if device.UserID == userID {
		return true, nil
	}

	// 检查用户是否通过家庭组访问设备
	// 1. 获取设备所属的所有家庭组
	groups, err := s.db.ListDeviceFamilyGroups(deviceID)
	if err != nil {
		return false, fmt.Errorf("获取设备所属家庭组失败: %v", err)
	}

	// 2. 检查用户是否在这些家庭组中的任意一个
	for _, group := range groups {
		inGroup, _, err := s.db.CheckUserInFamilyGroup(userID, group.GroupID)
		if err == nil && inGroup {
			return true, nil
		}
	}

	return false, nil
}

// HasUserDeviceAccess 检查用户是否有权限访问设备（包括通过家庭组）
// 此方法为公开API，返回布尔值表示是否有权限
func (s *CatToiletService) HasUserDeviceAccess(userID, deviceID string) (bool, error) {
	return s.checkUserDeviceAccessInternal(userID, deviceID)
}

// ListUserAccessibleDevices 获取用户可访问的所有设备（包括通过家庭组）
func (s *CatToiletService) ListUserAccessibleDevices(userID string) ([]Device, error) {
	// 检查用户是否存在

	_, err := s.db.GetUser(userID)
	if err != nil {
		return nil, fmt.Errorf("用户不存在: %v", err)
	}

	// 获取用户直接拥有的设备
	ownDevices, err := s.db.ListUserDevices(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户设备失败: %v", err)
	}

	// 设备ID去重Map
	deviceMap := make(map[string]Device)
	for _, device := range ownDevices {
		deviceMap[device.DeviceID] = device
	}

	// 获取用户所属的家庭组
	groups, err := s.db.ListUserFamilyGroups(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户家庭组失败: %v", err)
	}

	// 获取家庭组中的设备
	for _, group := range groups {
		devices, err := s.db.ListFamilyGroupDevices(group.GroupID)
		if err != nil {
			continue
		}

		// 添加设备到Map（去重）
		for _, groupDevice := range devices {
			if _, exists := deviceMap[groupDevice.DeviceID]; !exists {
				device, err := s.db.GetDevice(groupDevice.DeviceID)
				if err == nil {
					deviceMap[device.DeviceID] = *device
				}
			}
		}
	}

	// 转换Map为列表
	var result []Device
	for _, device := range deviceMap {
		result = append(result, device)
	}

	return result, nil
}

// CreateFamilyGroupInvitation 创建家庭组邀请
func (s *CatToiletService) CreateFamilyGroupInvitation(userID, groupID string, req *FamilyGroupInvitationCreateRequest) (*FamilyGroupInvitation, error) {
	// 检查操作者是否有权限发送邀请
	inGroup, role, err := s.db.CheckUserInFamilyGroup(userID, groupID)
	if err != nil {
		return nil, fmt.Errorf("检查用户权限失败: %v", err)
	}
	if !inGroup {
		return nil, fmt.Errorf("您不是该家庭组的成员，无权发送邀请")
	}
	if role != FamilyMemberRoleOwner && role != FamilyMemberRoleAdmin {
		return nil, fmt.Errorf("您没有权限发送邀请，需要管理员或拥有者权限")
	}

	// 检查邀请对象是否存在
	_, err = s.db.GetUser(req.InviteeID)
	if err != nil {
		return nil, fmt.Errorf("要邀请的用户不存在: %v", err)
	}

	// 检查组是否已满
	group, err := s.db.GetFamilyGroup(groupID)
	if err != nil {
		return nil, fmt.Errorf("获取家庭组信息失败: %v", err)
	}

	members, err := s.db.ListFamilyGroupMembers(groupID)
	if err != nil {
		return nil, fmt.Errorf("获取家庭组成员失败: %v", err)
	}

	if len(members) >= group.MaxMembers {
		return nil, fmt.Errorf("家庭组成员已达上限 %d 人", group.MaxMembers)
	}

	// 生成邀请ID
	invitationID := algo.GenerateFamilyGroupInvitationID(groupID)

	// 设置有效期（默认24小时）
	var expireAt *time.Time
	if req.ExpireAt != "" {
		t, err := time.Parse(time.RFC3339, req.ExpireAt)
		if err != nil {
			return nil, fmt.Errorf("过期时间格式无效: %v", err)
		}
		expireAt = &t
	} else {
		t := time.Now().Add(24 * time.Hour)
		expireAt = &t
	}

	// 限制角色权限：普通用户只能邀请普通成员
	if req.Role > FamilyMemberRoleAdmin || req.Role < FamilyMemberRoleNormal {
		req.Role = FamilyMemberRoleNormal
	}
	// 只有拥有者可以邀请管理员
	if req.Role == FamilyMemberRoleAdmin && role != FamilyMemberRoleOwner {
		req.Role = FamilyMemberRoleNormal
	}

	// 创建邀请
	invitation := &FamilyGroupInvitation{
		InvitationID: invitationID,
		GroupID:      groupID,
		InviterID:    userID,
		InviteeID:    req.InviteeID,
		Status:       0, // 待处理
		Role:         req.Role,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		ExpireAt:     expireAt,
	}

	if err := s.db.CreateFamilyGroupInvitation(invitation); err != nil {
		return nil, fmt.Errorf("创建邀请失败: %v", err)
	}

	return invitation, nil
}

// CreateFamilyGroupInvitationByEmail 通过邮箱创建家庭组邀请
func (s *CatToiletService) CreateFamilyGroupInvitationByEmail(userID, groupID string, req *FamilyGroupInvitationCreateByEmailRequest) (*FamilyGroupInvitation, error) {
	// 1. 检查操作者是否有权限发送邀请
	inGroup, role, err := s.db.CheckUserInFamilyGroup(userID, groupID)
	if err != nil {
		return nil, fmt.Errorf("检查用户权限失败: %v", err)
	}
	if !inGroup {
		return nil, fmt.Errorf("您不是该家庭组的成员，无权发送邀请")
	}
	if role != FamilyMemberRoleOwner && role != FamilyMemberRoleAdmin {
		return nil, fmt.Errorf("您没有权限发送邀请，需要管理员或拥有者权限")
	}

	// 2. 查询家庭组信息，检查是否存在
	group, err := s.db.GetFamilyGroup(groupID)
	if err != nil {
		return nil, fmt.Errorf("家庭组不存在: %v", err)
	}

	// 3. 通过邮箱查找用户ID
	invitee, err := s.db.GetUserByEmail(req.InviteeEmail)
	if err != nil {
		return nil, fmt.Errorf("未找到邮箱对应的用户: %v", err)
	}
	inviteeID := invitee.UserID

	// 4. 检查家庭组成员数是否已达上限
	members, err := s.db.ListFamilyGroupMembers(groupID)
	if err != nil {
		return nil, fmt.Errorf("获取家庭组成员失败: %v", err)
	}
	if len(members) >= group.MaxMembers {
		return nil, fmt.Errorf("家庭组成员数已达上限(%d)", group.MaxMembers)
	}

	// 5. 检查被邀请者是否已经是组成员
	inGroup, _, err = s.db.CheckUserInFamilyGroup(inviteeID, groupID)
	if err != nil {
		return nil, fmt.Errorf("检查被邀请用户状态失败: %v", err)
	}
	if inGroup {
		return nil, fmt.Errorf("被邀请用户已经是家庭组成员")
	}

	// 6. 创建邀请请求
	invitationReq := &FamilyGroupInvitationCreateRequest{
		InviteeID: inviteeID,
		Role:      req.Role,
		ExpireAt:  req.ExpireAt,
	}

	// 7. 调用原有方法创建邀请
	return s.CreateFamilyGroupInvitation(userID, groupID, invitationReq)
}

// GetFamilyGroupInvitation 获取家庭组邀请详情
func (s *CatToiletService) GetFamilyGroupInvitation(userID, invitationID string) (*FamilyGroupInvitation, error) {
	// 1. 获取邀请详情
	invitation, err := s.db.GetFamilyGroupInvitation(invitationID)
	if err != nil {
		return nil, fmt.Errorf("邀请不存在: %v", err)
	}

	// 2. 检查权限（只有邀请者和被邀请者可以查看）
	if invitation.InviterID != userID && invitation.InviteeID != userID {
		// 检查用户是否是家庭组管理员
		inGroup, role, err := s.db.CheckUserInFamilyGroup(userID, invitation.GroupID)
		if err != nil || !inGroup || (role != FamilyMemberRoleAdmin && role != FamilyMemberRoleOwner) {
			return nil, fmt.Errorf("您没有权限查看此邀请")
		}
	}

	// 3. 补充关联信息
	// 获取邀请者信息
	inviter, err := s.db.GetUser(invitation.InviterID)
	if err == nil {
		invitation.InviterInfo = inviter
	}

	// 获取被邀请者信息
	invitee, err := s.db.GetUser(invitation.InviteeID)
	if err == nil {
		invitation.InviteeInfo = invitee
	}

	// 获取家庭组信息
	group, err := s.db.GetFamilyGroup(invitation.GroupID)
	if err == nil {
		invitation.GroupInfo = group
	}

	return invitation, nil
}

// ListReceivedFamilyGroupInvitations 获取用户收到的所有待处理家庭组邀请
func (s *CatToiletService) ListReceivedFamilyGroupInvitations(userID string) ([]FamilyGroupInvitationResponse, error) {
	// 1. 获取用户收到的邀请
	invitations, err := s.db.ListReceivedFamilyGroupInvitations(userID)
	if err != nil {
		return nil, fmt.Errorf("获取收到的邀请失败: %v", err)
	}

	// 2. 将邀请转化为前端响应格式
	var responses []FamilyGroupInvitationResponse
	for _, invitation := range invitations {
		// 获取邀请者信息
		inviterName := ""
		inviter, err := s.db.GetUser(invitation.InviterID)
		if err == nil {
			inviterName = inviter.Nickname
			if inviterName == "" {
				inviterName = inviter.Username
			}
		}

		// 获取被邀请者信息
		inviteeName := ""
		invitee, err := s.db.GetUser(invitation.InviteeID)
		if err == nil {
			inviteeName = invitee.Nickname
			if inviteeName == "" {
				inviteeName = invitee.Username
			}
		}

		// 获取家庭组信息
		groupName := ""
		group, err := s.db.GetFamilyGroup(invitation.GroupID)
		if err == nil {
			groupName = group.GroupName
		}

		responses = append(responses, FamilyGroupInvitationResponse{
			InvitationID: invitation.InvitationID,
			GroupID:      invitation.GroupID,
			GroupName:    groupName,
			InviterID:    invitation.InviterID,
			InviterName:  inviterName,
			InviteeID:    invitation.InviteeID,
			InviteeName:  inviteeName,
			Status:       invitation.Status,
			Role:         invitation.Role,
			CreatedAt:    invitation.CreatedAt,
			ExpireAt:     invitation.ExpireAt,
		})
	}

	return responses, nil
}

// ListSentFamilyGroupInvitations 获取用户发送的所有家庭组邀请
func (s *CatToiletService) ListSentFamilyGroupInvitations(userID string) ([]FamilyGroupInvitationResponse, error) {
	// 1. 获取用户发送的邀请
	invitations, err := s.db.ListSentFamilyGroupInvitations(userID)
	if err != nil {
		return nil, fmt.Errorf("获取发送的邀请失败: %v", err)
	}

	// 2. 将邀请转化为前端响应格式
	var responses []FamilyGroupInvitationResponse
	for _, invitation := range invitations {
		// 获取邀请者信息
		inviterName := ""
		inviter, err := s.db.GetUser(invitation.InviterID)
		if err == nil {
			inviterName = inviter.Nickname
			if inviterName == "" {
				inviterName = inviter.Username
			}
		}

		// 获取被邀请者信息
		inviteeName := ""
		invitee, err := s.db.GetUser(invitation.InviteeID)
		if err == nil {
			inviteeName = invitee.Nickname
			if inviteeName == "" {
				inviteeName = invitee.Username
			}
		}

		// 获取家庭组信息
		groupName := ""
		group, err := s.db.GetFamilyGroup(invitation.GroupID)
		if err == nil {
			groupName = group.GroupName
		}

		responses = append(responses, FamilyGroupInvitationResponse{
			InvitationID: invitation.InvitationID,
			GroupID:      invitation.GroupID,
			GroupName:    groupName,
			InviterID:    invitation.InviterID,
			InviterName:  inviterName,
			InviteeID:    invitation.InviteeID,
			InviteeName:  inviteeName,
			Status:       invitation.Status,
			Role:         invitation.Role,
			CreatedAt:    invitation.CreatedAt,
			ExpireAt:     invitation.ExpireAt,
		})
	}

	return responses, nil
}

// ProcessFamilyGroupInvitation 处理家庭组邀请（接受或拒绝）
func (s *CatToiletService) ProcessFamilyGroupInvitation(userID, invitationID string, accept bool) error {
	// 1. 检查邀请是否存在
	invitation, err := s.db.GetFamilyGroupInvitation(invitationID)
	if err != nil {
		return fmt.Errorf("邀请不存在: %v", err)
	}

	// 2. 检查邀请状态
	if invitation.Status != 0 {
		return fmt.Errorf("该邀请已经被处理")
	}

	// 3. 检查是否过期
	if invitation.ExpireAt != nil && invitation.ExpireAt.Before(time.Now()) {
		return fmt.Errorf("该邀请已过期")
	}

	// 4. 检查用户权限（只有被邀请者可以处理邀请）
	if invitation.InviteeID != userID {
		return fmt.Errorf("您不是此邀请的接收者")
	}

	// 5. 处理邀请
	if accept {
		// 接受邀请，添加用户到家庭组
		member := &FamilyGroupMember{
			GroupID:  invitation.GroupID,
			UserID:   userID,
			Nickname: "新成员", // 可以后续修改
			Role:     invitation.Role,
		}
		if err := s.db.AddFamilyGroupMember(member); err != nil {
			return fmt.Errorf("添加成员失败: %v", err)
		}
		// 更新邀请状态为已接受
		if err := s.db.UpdateFamilyGroupInvitation(invitationID, 1); err != nil {
			return fmt.Errorf("更新邀请状态失败: %v", err)
		}
	} else {
		// 拒绝邀请，更新邀请状态为已拒绝
		if err := s.db.UpdateFamilyGroupInvitation(invitationID, 2); err != nil {
			return fmt.Errorf("更新邀请状态失败: %v", err)
		}
	}

	return nil
}

// CancelFamilyGroupInvitation 取消家庭组邀请
func (s *CatToiletService) CancelFamilyGroupInvitation(userID, invitationID string) error {
	// 获取邀请信息
	invitation, err := s.db.GetFamilyGroupInvitation(invitationID)
	if err != nil {
		return fmt.Errorf("获取邀请信息失败: %v", err)
	}

	// 验证权限 - 只有邀请者或家庭组拥有者可以取消邀请
	if invitation.InviterID != userID {
		// 检查是否是家庭组拥有者
		group, err := s.db.GetFamilyGroup(invitation.GroupID)
		if err != nil {
			return fmt.Errorf("获取家庭组信息失败: %v", err)
		}
		if group.OwnerID != userID {
			return fmt.Errorf("只有邀请者或家庭组拥有者可以取消邀请")
		}
	}

	// 检查邀请状态
	if invitation.Status != 0 { // 0-待处理
		return fmt.Errorf("只能取消待处理的邀请")
	}

	// 删除邀请
	if err := s.db.DeleteFamilyGroupInvitation(invitationID); err != nil {
		return fmt.Errorf("删除邀请失败: %v", err)
	}

	return nil
}

// ==================== 设备OTA状态服务方法 ====================

// GetDeviceOTAStatus 获取设备OTA状态
func (s *CatToiletService) GetDeviceOTAStatus(deviceID string) (*DeviceOTAStatusResponse, error) {
	// 验证设备是否存在
	_, err := s.db.GetDevice(deviceID)
	if err != nil {
		return nil, fmt.Errorf("设备不存在: %v", err)
	}

	// 获取OTA状态
	status, err := s.db.GetDeviceOTAStatus(deviceID)
	if err != nil {
		return nil, fmt.Errorf("获取OTA状态失败: %v", err)
	}

	return &DeviceOTAStatusResponse{
		DeviceID:    status.DeviceID,
		Status:      status.Status,
		LastUpdated: status.LastUpdated,
	}, nil
}

// UpdateDeviceOTAStatus 更新设备OTA状态（设备端调用）
func (s *CatToiletService) UpdateDeviceOTAStatus(deviceID, statusValue string) error {
	// 验证状态值
	validStatuses := []string{"idle", "updating", "failed", "completed"}
	isValid := false
	for _, validStatus := range validStatuses {
		if statusValue == validStatus {
			isValid = true
			break
		}
	}
	if !isValid {
		return fmt.Errorf("无效的OTA状态值: %s，有效值为: %v", statusValue, validStatuses)
	}

	// 验证设备是否存在
	_, err := s.db.GetDevice(deviceID)
	if err != nil {
		return fmt.Errorf("设备不存在: %v", err)
	}

	// 更新OTA状态
	if err := s.db.UpdateDeviceOTAStatus(deviceID, statusValue); err != nil {
		return fmt.Errorf("更新OTA状态失败: %v", err)
	}

	return nil
}

// StartOTAStatusMonitor 启动OTA状态监控（定期重置超时的状态）
func (s *CatToiletService) StartOTAStatusMonitor() {
	go func() {
		ticker := time.NewTicker(1 * time.Minute) // 每分钟检查一次
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				if err := s.db.ResetExpiredOTAStatus(); err != nil {
					log.Printf("重置超时OTA状态失败: %v", err)
				}
			}
		}
	}()
}

// ==================== OTA下载链接缓存服务方法 ====================

// GetOTADownloadCacheFromDB 从数据库获取OTA下载链接缓存
func (s *CatToiletService) GetOTADownloadCacheFromDB(version string) (*OTADownloadCache, error) {
	return s.db.GetOTADownloadCache(version)
}

// SaveOTADownloadCache 保存OTA下载链接缓存到数据库
func (s *CatToiletService) SaveOTADownloadCache(version, downloadURL, md5URL string, expiresAt time.Time) error {
	cache := &OTADownloadCache{
		Version:     version,
		DownloadURL: downloadURL,
		MD5URL:      md5URL,
		GeneratedAt: time.Now(),
		ExpiresAt:   expiresAt,
	}
	return s.db.CreateOrUpdateOTADownloadCache(cache)
}

// CleanSoonToExpireOTACache 清理剩余时间小于2天的OTA下载链接缓存
func (s *CatToiletService) CleanSoonToExpireOTACache() error {
	return s.db.DeleteSoonToExpireOTADownloadCache()
}

// DeleteCat 软删除猫咪
func (s *CatToiletService) DeleteCat(catID string, userID string) error {
	return s.catService.DeleteCat(catID, userID)
}

// HideCat 隐藏猫咪
func (s *CatToiletService) HideCat(catID string, userID string) error {
	return s.catService.HideCat(catID, userID)
}

// RestoreCat 恢复猫咪（取消隐藏）
func (s *CatToiletService) RestoreCat(catID string, userID string) error {
	return s.catService.RestoreCat(catID, userID)
}

// ListUserHiddenCats 获取用户隐藏的猫咪列表
func (s *CatToiletService) ListUserHiddenCats(userID string) ([]cat.Cat, error) {
	return s.catService.ListUserHiddenCats(userID)
}

// ListUserAllCats 获取用户所有猫咪（包括隐藏的，不包括已删除的）
func (s *CatToiletService) ListUserAllCats(userID string) ([]cat.Cat, error) {
	return s.catService.ListUserAllCats(userID)
}

// CheckUserCatOwnership 检查用户是否拥有指定的猫咪
func (s *CatToiletService) CheckUserCatOwnership(userID, catID string) (bool, error) {
	cat, err := s.catService.GetCat(catID)
	if err != nil {
		return false, err
	}
	return cat.UserID == userID, nil
}

// ==================== 设备传感器状态服务方法 ====================

// GetDeviceSensorStatus 获取设备传感器状态
func (s *CatToiletService) GetDeviceSensorStatus(deviceID string) (*DeviceSensorStatusResponse, error) {
	// 验证设备是否存在
	_, err := s.db.GetDevice(deviceID)
	if err != nil {
		return nil, fmt.Errorf("设备不存在: %v", err)
	}

	// 获取传感器状态
	status, err := s.db.GetDeviceSensorStatus(deviceID)
	if err != nil {
		return nil, fmt.Errorf("获取传感器状态失败: %v", err)
	}

	return &DeviceSensorStatusResponse{
		DeviceID:                               status.DeviceID,
		CameraLastErrorTime:                    status.CameraLastErrorTime,
		CameraLastErrorType:                    status.CameraLastErrorType,
		WeightSensorLastErrorTime:              status.WeightSensorLastErrorTime,
		WeightSensorLastErrorType:              status.WeightSensorLastErrorType,
		TemperatureHumiditySensorLastErrorTime: status.TemperatureHumiditySensorLastErrorTime,
		TemperatureHumiditySensorLastErrorType: status.TemperatureHumiditySensorLastErrorType,
		MicrophoneLastErrorTime:                status.MicrophoneLastErrorTime,
		MicrophoneLastErrorType:                status.MicrophoneLastErrorType,
		WifiLastErrorTime:                      status.WifiLastErrorTime,
		WifiLastErrorType:                      status.WifiLastErrorType,
		BluetoothLastErrorTime:                 status.BluetoothLastErrorTime,
		BluetoothLastErrorType:                 status.BluetoothLastErrorType,
		UpdatedAt:                              status.UpdatedAt,
	}, nil
}

// ReportDeviceSensorError 上报设备传感器错误
func (s *CatToiletService) ReportDeviceSensorError(req *DeviceSensorErrorRequest) error {
	// 验证设备是否存在
	_, err := s.db.GetDevice(req.DeviceID)
	if err != nil {
		return fmt.Errorf("设备不存在: %v", err)
	}

	// 验证传感器类型
	validSensorTypes := []string{"camera", "weight_sensor", "temperature_humidity_sensor", "microphone", "wifi", "bluetooth"}
	isValidSensorType := false
	for _, validType := range validSensorTypes {
		if req.SensorType == validType {
			isValidSensorType = true
			break
		}
	}
	if !isValidSensorType {
		return fmt.Errorf("不支持的传感器类型: %s，有效类型为: %v", req.SensorType, validSensorTypes)
	}

	// 确定错误时间
	var errorTime time.Time
	if req.ReportTime > 0 {
		errorTime = time.Unix(req.ReportTime, 0)
	} else {
		errorTime = time.Now()
	}

	// 构造错误类型描述
	var errorType string
	if req.ErrorCode != 0 && req.ErrorMessage != "" {
		errorType = fmt.Sprintf("Code:%d - %s", req.ErrorCode, req.ErrorMessage)
	} else if req.ErrorCode != 0 {
		errorType = fmt.Sprintf("Code:%d", req.ErrorCode)
	} else if req.ErrorMessage != "" {
		errorType = req.ErrorMessage
	} else {
		errorType = "Unknown Error"
	}

	// 添加附加信息
	if req.AdditionalInfo != "" {
		errorType = fmt.Sprintf("%s (%s)", errorType, req.AdditionalInfo)
	}

	// 更新传感器错误时间和类型
	if err := s.db.UpdateDeviceSensorError(req.DeviceID, req.SensorType, errorTime, errorType); err != nil {
		return fmt.Errorf("更新传感器错误信息失败: %v", err)
	}

	// TODO: 这里可以添加发送通知的逻辑
	// 例如：当传感器出现错误时，通知用户

	return nil
}

// ListDevicesWithSensorErrors 获取有传感器错误的设备列表
func (s *CatToiletService) ListDevicesWithSensorErrors() ([]DeviceSensorStatus, error) {
	return s.db.ListDevicesWithSensorErrors()
}

// ClearDeviceSensorError 清除设备传感器错误状态
func (s *CatToiletService) ClearDeviceSensorError(deviceID, sensorType string) error {
	// 验证设备是否存在
	_, err := s.db.GetDevice(deviceID)
	if err != nil {
		return fmt.Errorf("设备不存在: %v", err)
	}

	// 验证传感器类型
	validSensorTypes := []string{"camera", "weight_sensor", "temperature_humidity_sensor", "microphone", "wifi", "bluetooth"}
	isValidSensorType := false
	for _, validType := range validSensorTypes {
		if sensorType == validType {
			isValidSensorType = true
			break
		}
	}
	if !isValidSensorType {
		return fmt.Errorf("不支持的传感器类型: %s，有效类型为: %v", sensorType, validSensorTypes)
	}

	// 调用数据库方法清除传感器错误状态
	return s.db.ClearDeviceSensorError(deviceID, sensorType)
}

// processWithShadowMode 使用影子模式处理视频分析
func (s *CatToiletService) processWithShadowMode(ctx context.Context, analysis *RecordAnalysis, userID, videoID string) {
	// 检查用户是否启用影子模式
	if !s.isUserShadowModeEnabled(userID) {
		log.Printf("Shadow mode disabled for user %s, skipping", userID)
		return
	}

	// 获取视频缩略图进行影子模式识别
	thumbnailBase64, err := s.getVideoThumbnailBase64(videoID)
	if err != nil {
		log.Printf("Failed to get video thumbnail for shadow mode: %v", err)
		return
	}

	// 调用影子模式服务
	shadowResult, err := s.shadowService.ProcessImage(ctx, thumbnailBase64, userID)
	if err != nil {
		log.Printf("Shadow mode processing failed: %v", err)
		return
	}

	// 更新分析结果中的影子模式字段
	s.updateAnalysisWithShadowResult(analysis, shadowResult)

	// 保存更新后的分析结果
	if err := s.db.UpdateRecordAnalysis(analysis); err != nil {
		log.Printf("Failed to update analysis with shadow result: %v", err)
	}

	log.Printf("Shadow mode processing completed for video %s: cat_id=%s, similarity=%.4f, is_new_cat=%v",
		videoID, shadowResult.CatID, shadowResult.Similarity, shadowResult.IsNewCat)
}

// getVideoThumbnailBase64 获取视频缩略图的base64编码
func (s *CatToiletService) getVideoThumbnailBase64(videoID string) (string, error) {
	// 获取视频记录
	record, err := s.GetRecordShit(videoID)
	if err != nil {
		return "", fmt.Errorf("failed to get video record: %v", err)
	}

	// 获取缩略图路径 - 使用records桶中的路径格式
	thumbnailPath := fmt.Sprintf("records/device%s/%s/cover.jpg", record.DeviceID, videoID)

	// 从MinIO获取缩略图内容
	thumbnailData, err := s.storageService.GetObjectContent(thumbnailPath)
	if err != nil {
		return "", fmt.Errorf("failed to get thumbnail from storage: %v", err)
	}

	// 转换为base64
	thumbnailBase64 := base64.StdEncoding.EncodeToString(thumbnailData)
	return thumbnailBase64, nil
}

// updateAnalysisWithShadowResult 使用影子模式结果更新分析（只保留重要信息）
func (s *CatToiletService) updateAnalysisWithShadowResult(analysis *RecordAnalysis, shadowResult *shadow.ShadowModeResult) {
	analysis.ShadowSimilarity = &shadowResult.Similarity
	analysis.ShadowMatchedCatID = &shadowResult.CatID
	analysis.ShadowModelVersion = &shadowResult.ModelVersion
}

// isUserShadowModeEnabled 检查用户是否启用影子模式
func (s *CatToiletService) isUserShadowModeEnabled(userID string) bool {
	// 首先检查全局配置是否启用
	if !s.shadowService.IsEnabled() {
		return false
	}

	// 查询用户配置
	config, err := s.getUserShadowConfig(userID)
	if err != nil {
		// 如果没有找到用户配置，返回默认值（禁用）
		log.Printf("No shadow mode config found for user %s, using default (disabled)", userID)
		return false
	}

	return config.Enabled
}

// getUserShadowConfig 获取用户影子模式配置
func (s *CatToiletService) getUserShadowConfig(userID string) (*UserShadowConfig, error) {
	// 添加到database.go中的方法
	return s.db.GetUserShadowConfig(userID)
}

// enableUserShadowMode 为用户启用影子模式
func (s *CatToiletService) enableUserShadowMode(userID string, config *UserShadowConfig) error {
	if config == nil {
		config = &UserShadowConfig{
			UserID:              userID,
			Enabled:             true,
			SimilarityThreshold: 0.85,
			NewCatThreshold:     0.70,
			TopK:                5,
			NotificationEnabled: false,
		}
	}

	config.UserID = userID
	config.Enabled = true

	// 添加到database.go中的方法
	return s.db.SaveUserShadowConfig(config)
}

// disableUserShadowMode 为用户禁用影子模式
func (s *CatToiletService) disableUserShadowMode(userID string) error {
	// 添加到database.go中的方法
	return s.db.DisableUserShadowMode(userID)
}

// GetUserShadowConfig 获取用户影子模式配置（公开方法）
func (s *CatToiletService) GetUserShadowConfig(userID string) (*UserShadowConfig, error) {
	return s.getUserShadowConfig(userID)
}

// EnableUserShadowMode 启用用户影子模式（公开方法）
func (s *CatToiletService) EnableUserShadowMode(userID string, config *UserShadowConfig) error {
	return s.enableUserShadowMode(userID, config)
}

// DisableUserShadowMode 禁用用户影子模式（公开方法）
func (s *CatToiletService) DisableUserShadowMode(userID string) error {
	return s.disableUserShadowMode(userID)
}

// IsUserShadowModeEnabled 检查用户是否启用影子模式（公开方法）
func (s *CatToiletService) IsUserShadowModeEnabled(userID string) bool {
	return s.isUserShadowModeEnabled(userID)
}

// createDefaultUserShadowConfig 为新用户创建默认的影子模式配置
func (s *CatToiletService) createDefaultUserShadowConfig(userID string) error {
	// 检查是否已存在配置
	_, err := s.db.GetUserShadowConfig(userID)
	if err == nil {
		// 配置已存在，无需创建
		return nil
	}

	// 创建默认配置
	defaultConfig := &UserShadowConfig{
		UserID:              userID,
		Enabled:             false, // 默认禁用
		SimilarityThreshold: 0.85,  // 默认相似度阈值
		NewCatThreshold:     0.70,  // 默认新猫阈值
		TopK:                5,     // 默认返回结果数量
		NotificationEnabled: false, // 默认禁用通知
	}

	return s.db.SaveUserShadowConfig(defaultConfig)
}

// CheckVideoStatic 检查视频是否为静态视频（基于cat_frame_count）
func (s *CatToiletService) CheckVideoStatic(record *RecordShit, device *Device) (bool, error) {
	// 解析时区
	var loc *time.Location
	if device.Timezone != "" {
		var err error
		loc, err = time.LoadLocation(device.Timezone)
		if err != nil {
			loc = time.UTC
		}
	} else {
		loc = time.UTC
	}

	// 构建video_data.json的minio路径
	startTimeObj := time.Unix(record.StartTime, 0).In(loc)
	folderName := startTimeObj.Format("2006-01-02_15-04-05") + "_hls"
	devicePath := fmt.Sprintf("device%s", record.DeviceID)
	videoDataPath := fmt.Sprintf("records/%s/%s/video_data.json", devicePath, folderName)
	videoDataContent, err := s.storageService.GetObjectContent(videoDataPath)
	if err != nil {
		return false, fmt.Errorf("video_data.json not found: %v", err)
	}

	// 检查文件内容是否为空
	if len(videoDataContent) == 0 {
		return false, fmt.Errorf("video_data.json is empty")
	}
	// 解析video_data.json
	var videoData struct {
		CatFrameCount int `json:"cat_frame_count"`
	}

	if err := json.Unmarshal(videoDataContent, &videoData); err != nil {
		return false, fmt.Errorf("failed to parse video_data.json: %v", err)
	}

	// 使用简化的静态视频判断规则：cat_frame_count < 50
	isStatic := videoData.CatFrameCount < 50

	log.Printf("Video %s static check: cat_frame_count=%d, is_static=%v",
		record.VideoID, videoData.CatFrameCount, isStatic)

	return isStatic, nil
}

// ==================== 备份相关方法 ====================

// GetBackupVideoList 获取备份视频列表
func (s *CatToiletService) GetBackupVideoList(req *BackupListRequest) (*BackupListResponse, error) {
	videos, total, err := s.db.GetBackupVideoList(req)
	if err != nil {
		return nil, fmt.Errorf("获取备份视频列表失败: %v", err)
	}

	// 计算总页数
	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))
	if req.PageSize <= 0 {
		totalPages = 1
	}

	return &BackupListResponse{
		Videos:     videos,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// GetMinioDirectDownloadInfo 获取MinIO直接下载信息
func (s *CatToiletService) GetMinioDirectDownloadInfo() map[string]interface{} {
	return map[string]interface{}{
		"endpoint":   s.cfg.Minio.Endpoint,
		"access_key": s.cfg.Minio.AccessKey,
		"secret_key": s.cfg.Minio.SecretKey,
		"use_ssl":    s.cfg.Minio.UseSSL,
		"bucket":     "records",
	}
}
