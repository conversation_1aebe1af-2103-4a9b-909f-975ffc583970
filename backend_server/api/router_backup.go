package api

import (
	"cabycare-server/config"
	"cabycare-server/pkg/cattoilet"
	"cabycare-server/pkg/factory"

	"github.com/gin-gonic/gin"
)

// RegisterBackupRoutes 注册备份相关路由（使用factory token认证）
func RegisterBackupRoutes(routerGroup *gin.RouterGroup, catHandler *cattoilet.Handler, cfg *config.Config) {
	backupGroup := routerGroup.Group("/backup")
	backupGroup.Use(factory.FactoryTokenAuth(cfg))
	{
		// 获取备份视频列表
		backupGroup.GET("/videos", catHandler.GetBackupVideoList)
		
		// 获取MinIO配置信息
		backupGroup.GET("/minio-config", catHandler.GetMinioConfig)
	}
}
