ffmpeg version 7.0.2 Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 14.2.0 (GCC)
  configuration: --disable-stripping --enable-pic --enable-shared --enable-pthreads --cross-prefix=aarch64-oe-linux- --ld='aarch64-oe-linux-gcc -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --cc='aarch64-oe-linux-gcc -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --cxx='aarch64-oe-linux-g++ -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --arch=aarch64 --target-os=linux --enable-cross-compile --extra-cflags=' -O2 -g -fcanon-prefix-map -fmacro-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fmacro-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/recipe-sysroot= -fmacro-prefix-map=/recipe-sysroot= -fdebug-prefix-map=/recipe-sysroot-native= -fmacro-prefix-map=/recipe-sysroot-native= -pipe -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --extra-ldflags='-Wl,-O1 -Wl,--hash-style=gnu -Wl,--as-needed -fcanon-prefix-map -fmacro-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fmacro-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/recipe-sysroot= -fmacro-prefix-map=/recipe-sysroot= -fdebug-prefix-map=/recipe-sysroot-native= -fmacro-prefix-map=/recipe-sysroot-native= ' --sysroot=/recipe-sysroot --libdir=/usr/lib --shlibdir=/usr/lib --datadir=/usr/share/ffmpeg --disable-mipsdsp --disable-mipsdspr2 --cpu=cortex-a53+crc --pkg-config=pkg-config --enable-shared --enable-demuxer=rtp --enable-demuxer=rtsp --enable-protocol=udp --enable-protocol=rtp --enable-libfdk-aac --enable-nonfree --enable-protocol=http --enable-protocol=https --enable-muxer=hls --enable-demuxer=hls --enable-libmp3lame --enable-alsa --disable-altivec --enable-avcodec --enable-avdevice --enable-avfilter --enable-avformat --enable-bzlib --enable-libfdk-aac --enable-nonfree --enable-gpl --disable-libgsm --disable-indev=jack --enable-libopus --disable-libvorbis --enable-lzma --disable-libmfx --enable-libmp3lame --disable-openssl --enable-postproc --disable-sdl2 --disable-libspeex --disable-libsrt --enable-swresample --enable-swscale --enable-libtheora --disable-libv4l2 --disable-vaapi --disable-vdpau --disable-libvpx --enable-libx264 --disable-libx265 --enable-libxcb --enable-outdev=xv --enable-zlib
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
Input #0, rtsp, from 'rtsp://127.0.0.1/h264':
  Metadata:
    title           : 
    comment         : h264
  Duration: N/A, start: 0.000000, bitrate: N/A
  Stream #0:0: Video: h264 (Constrained Baseline), yuv420p(progressive), 1280x720, 120 tbr, 90k tbn
  Stream #0:1: Audio: pcm_s16be, 16000 Hz, mono, s16, 256 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (pcm_s16be (native) -> aac (libfdk_aac))
Press [q] to stop, [?] for help
Output #0, hls, to '/mnt/recordings/2025-07-29_10-53-17_hls/playlist.m3u8':
  Metadata:
    title           : 
    comment         : h264
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (Constrained Baseline), yuv420p(progressive), 1280x720, q=2-31, 120 tbr, 90k tbn
  Stream #0:1: Audio: aac, 8000 Hz, mono, s16, 32 kb/s
      Metadata:
        encoder         : Lavc61.3.100 libfdk_aac
[hls @ 0x4a9040] Stream 0 packet with pts 51054 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 59038 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 65957 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 69033 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 77024 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 77958 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 80108 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 90009 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 92031 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 92992 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 95058 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 98133 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 101039 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 104091 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 107030 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 110099 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 113029 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 116097 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 119075 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 122054 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 125088 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 128013 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 131062 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 134115 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 137152 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 140128 has duration 0. The segment duration may not be precise.
[hls @ 0x4a9040] Stream 0 packet with pts 143027 has duration 0. The segment duration may not be precise.
size=N/A time=00:00:01.53 bitrate=N/A speed=3.07x    
size=N/A time=00:00:02.03 bitrate=N/A speed=2.04x    
size=N/A time=00:00:02.56 bitrate=N/A speed=1.71x    
size=N/A time=00:00:03.06 bitrate=N/A speed=1.53x    
size=N/A time=00:00:03.57 bitrate=N/A speed=1.43x    
size=N/A time=00:00:04.07 bitrate=N/A speed=1.36x    
size=N/A time=00:00:04.59 bitrate=N/A speed=1.31x    
size=N/A time=00:00:05.10 bitrate=N/A speed=1.27x    
size=N/A time=00:00:05.61 bitrate=N/A speed=1.25x    
size=N/A time=00:00:06.00 bitrate=N/A speed= 1.2x    
[hls @ 0x4a9040] Opening '/mnt/recordings/2025-07-29_10-53-17_hls/segment_0000.ts' for writing
size=N/A time=00:00:06.64 bitrate=N/A speed=1.21x    
size=N/A time=00:00:07.02 bitrate=N/A speed=1.17x    
size=N/A time=00:00:07.54 bitrate=N/A speed=1.16x    
size=N/A time=00:00:08.05 bitrate=N/A speed=1.15x    
size=N/A time=00:00:08.56 bitrate=N/A speed=1.14x    
size=N/A time=00:00:09.07 bitrate=N/A speed=1.13x    
size=N/A time=00:00:09.59 bitrate=N/A speed=1.13x    
size=N/A time=00:00:10.10 bitrate=N/A speed=1.12x    
size=N/A time=00:00:10.60 bitrate=N/A speed=1.12x    
size=N/A time=00:00:11.12 bitrate=N/A speed=1.11x    
size=N/A time=00:00:11.63 bitrate=N/A speed=1.11x    
size=N/A time=00:00:12.01 bitrate=N/A speed=1.09x    
size=N/A time=00:00:12.53 bitrate=N/A speed=1.09x    
[hls @ 0x4a9040] Opening '/mnt/recordings/2025-07-29_10-53-17_hls/segment_0001.ts' for writing
size=N/A time=00:00:13.04 bitrate=N/A speed=1.09x    
size=N/A time=00:00:13.55 bitrate=N/A speed=1.08x    
size=N/A time=00:00:14.06 bitrate=N/A speed=1.08x    
size=N/A time=00:00:14.58 bitrate=N/A speed=1.08x    
size=N/A time=00:00:15.09 bitrate=N/A speed=1.08x    
size=N/A time=00:00:15.59 bitrate=N/A speed=1.07x    
size=N/A time=00:00:16.11 bitrate=N/A speed=1.07x    
size=N/A time=00:00:16.62 bitrate=N/A speed=1.07x    
size=N/A time=00:00:17.00 bitrate=N/A speed=1.06x    
size=N/A time=00:00:17.53 bitrate=N/A speed=1.06x    
size=N/A time=00:00:18.03 bitrate=N/A speed=1.06x    
size=N/A time=00:00:18.55 bitrate=N/A speed=1.06x    
[hls @ 0x4a9040] Opening '/mnt/recordings/2025-07-29_10-53-17_hls/segment_0002.ts' for writing
size=N/A time=00:00:19.05 bitrate=N/A speed=1.06x    
size=N/A time=00:00:19.57 bitrate=N/A speed=1.06x    
size=N/A time=00:00:20.08 bitrate=N/A speed=1.06x    
size=N/A time=00:00:20.59 bitrate=N/A speed=1.05x    
size=N/A time=00:00:21.11 bitrate=N/A speed=1.05x    
size=N/A time=00:00:21.62 bitrate=N/A speed=1.05x    
size=N/A time=00:00:22.14 bitrate=N/A speed=1.05x    
size=N/A time=00:00:22.64 bitrate=N/A speed=1.05x    
size=N/A time=00:00:23.15 bitrate=N/A speed=1.05x    
size=N/A time=00:00:23.54 bitrate=N/A speed=1.05x    
size=N/A time=00:00:24.04 bitrate=N/A speed=1.04x    
size=N/A time=00:00:24.56 bitrate=N/A speed=1.04x    
[hls @ 0x4a9040] Opening '/mnt/recordings/2025-07-29_10-53-17_hls/segment_0003.ts' for writing
size=N/A time=00:00:25.07 bitrate=N/A speed=1.04x    
size=N/A time=00:00:25.58 bitrate=N/A speed=1.04x    
size=N/A time=00:00:26.10 bitrate=N/A speed=1.04x    
size=N/A time=00:00:26.61 bitrate=N/A speed=1.04x    
size=N/A time=00:00:27.13 bitrate=N/A speed=1.04x    
size=N/A time=00:00:27.63 bitrate=N/A speed=1.04x    
size=N/A time=00:00:28.15 bitrate=N/A speed=1.04x    
size=N/A time=00:00:28.53 bitrate=N/A speed=1.04x    
size=N/A time=00:00:29.04 bitrate=N/A speed=1.04x    
size=N/A time=00:00:29.68 bitrate=N/A speed=1.04x    
size=N/A time=00:00:30.06 bitrate=N/A speed=1.04x    
size=N/A time=00:00:30.57 bitrate=N/A speed=1.04x    
[hls @ 0x4a9040] Opening '/mnt/recordings/2025-07-29_10-53-17_hls/segment_0004.ts' for writing
size=N/A time=00:00:31.09 bitrate=N/A speed=1.04x    
size=N/A time=00:00:31.60 bitrate=N/A speed=1.04x    
size=N/A time=00:00:32.12 bitrate=N/A speed=1.04x    
size=N/A time=00:00:32.62 bitrate=N/A speed=1.03x    
size=N/A time=00:00:33.14 bitrate=N/A speed=1.03x    
size=N/A time=00:00:33.52 bitrate=N/A speed=1.03x    
size=N/A time=00:00:34.16 bitrate=N/A speed=1.03x    
size=N/A time=00:00:34.55 bitrate=N/A speed=1.03x    
[hls @ 0x4a9040] Opening '/mnt/recordings/2025-07-29_10-53-17_hls/segment_0005.ts' for writing
[out#0/hls @ 0x825d90] video:14234KiB audio:140KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:00:35.36 bitrate=N/A speed=1.04x    
Exiting normally, received signal 15.
