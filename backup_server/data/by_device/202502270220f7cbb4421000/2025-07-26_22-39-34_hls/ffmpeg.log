ffmpeg version 7.0.2 Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 14.2.0 (GCC)
  configuration: --disable-stripping --enable-pic --enable-shared --enable-pthreads --cross-prefix=aarch64-oe-linux- --ld='aarch64-oe-linux-gcc -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --cc='aarch64-oe-linux-gcc -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --cxx='aarch64-oe-linux-g++ -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --arch=aarch64 --target-os=linux --enable-cross-compile --extra-cflags=' -O2 -g -fcanon-prefix-map -fmacro-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fmacro-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/recipe-sysroot= -fmacro-prefix-map=/recipe-sysroot= -fdebug-prefix-map=/recipe-sysroot-native= -fmacro-prefix-map=/recipe-sysroot-native= -pipe -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --extra-ldflags='-Wl,-O1 -Wl,--hash-style=gnu -Wl,--as-needed -fcanon-prefix-map -fmacro-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fmacro-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/recipe-sysroot= -fmacro-prefix-map=/recipe-sysroot= -fdebug-prefix-map=/recipe-sysroot-native= -fmacro-prefix-map=/recipe-sysroot-native= ' --sysroot=/recipe-sysroot --libdir=/usr/lib --shlibdir=/usr/lib --datadir=/usr/share/ffmpeg --disable-mipsdsp --disable-mipsdspr2 --cpu=cortex-a53+crc --pkg-config=pkg-config --enable-shared --enable-demuxer=rtp --enable-demuxer=rtsp --enable-protocol=udp --enable-protocol=rtp --enable-libfdk-aac --enable-nonfree --enable-protocol=http --enable-protocol=https --enable-muxer=hls --enable-demuxer=hls --enable-libmp3lame --enable-alsa --disable-altivec --enable-avcodec --enable-avdevice --enable-avfilter --enable-avformat --enable-bzlib --enable-libfdk-aac --enable-nonfree --enable-gpl --disable-libgsm --disable-indev=jack --enable-libopus --disable-libvorbis --enable-lzma --disable-libmfx --enable-libmp3lame --disable-openssl --enable-postproc --disable-sdl2 --disable-libspeex --disable-libsrt --enable-swresample --enable-swscale --enable-libtheora --disable-libv4l2 --disable-vaapi --disable-vdpau --disable-libvpx --enable-libx264 --disable-libx265 --enable-libxcb --enable-outdev=xv --enable-zlib
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[h264 @ 0x465f00] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465f00] decode_slice_header error
[h264 @ 0x465f00] no frame!
[h264 @ 0x465f00] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465f00] decode_slice_header error
[h264 @ 0x465f00] no frame!
[h264 @ 0x465f00] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465f00] decode_slice_header error
[h264 @ 0x465f00] no frame!
[h264 @ 0x465f00] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465f00] decode_slice_header error
[h264 @ 0x465f00] no frame!
[h264 @ 0x465f00] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465f00] decode_slice_header error
[h264 @ 0x465f00] no frame!
[h264 @ 0x465f00] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465f00] decode_slice_header error
[h264 @ 0x465f00] no frame!
[h264 @ 0x465f00] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465f00] decode_slice_header error
[h264 @ 0x465f00] no frame!
[h264 @ 0x465f00] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465f00] decode_slice_header error
[h264 @ 0x465f00] no frame!
[h264 @ 0x465f00] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465f00] decode_slice_header error
[h264 @ 0x465f00] no frame!
[h264 @ 0x465f00] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465f00] decode_slice_header error
[h264 @ 0x465f00] no frame!
[h264 @ 0x465f00] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465f00] decode_slice_header error
[h264 @ 0x465f00] no frame!
[h264 @ 0x465f00] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465f00] decode_slice_header error
[h264 @ 0x465f00] no frame!
[h264 @ 0x465f00] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465f00] decode_slice_header error
[h264 @ 0x465f00] no frame!
[h264 @ 0x465f00] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465f00] decode_slice_header error
[h264 @ 0x465f00] no frame!
[h264 @ 0x465f00] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465f00] decode_slice_header error
[h264 @ 0x465f00] no frame!
[h264 @ 0x465f00] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465f00] decode_slice_header error
[h264 @ 0x465f00] no frame!
[h264 @ 0x465f00] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465f00] decode_slice_header error
[h264 @ 0x465f00] no frame!
Input #0, rtsp, from 'rtsp://127.0.0.1/h264':
  Metadata:
    title           : 
    comment         : h264
  Duration: N/A, start: 0.000000, bitrate: N/A
  Stream #0:0: Video: h264 (Constrained Baseline), yuv420p(progressive), 1280x720, 29.92 tbr, 90k tbn
  Stream #0:1: Audio: pcm_s16be, 16000 Hz, mono, s16, 256 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (pcm_s16be (native) -> aac (libfdk_aac))
Press [q] to stop, [?] for help
Output #0, hls, to '/mnt/recordings/2025-07-26_22-39-34_hls/playlist.m3u8':
  Metadata:
    title           : 
    comment         : h264
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (Constrained Baseline), yuv420p(progressive), 1280x720, q=2-31, 29.92 tbr, 90k tbn
  Stream #0:1: Audio: aac, 8000 Hz, mono, s16, 32 kb/s
      Metadata:
        encoder         : Lavc61.3.100 libfdk_aac
[hls @ 0x4bafa0] Stream 0 packet with pts 78046 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 85213 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 86137 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 95985 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 105028 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 106189 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 107114 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 112105 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 116064 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 119016 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 119943 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 122146 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 125116 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 128068 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 131112 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 134099 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 137073 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 140068 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 143196 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 146040 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 149064 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 152124 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 155172 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 158036 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 161110 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 164063 has duration 0. The segment duration may not be precise.
[hls @ 0x4bafa0] Stream 0 packet with pts 167176 has duration 0. The segment duration may not be precise.
size=N/A time=00:00:01.78 bitrate=N/A speed=3.57x    
size=N/A time=00:00:02.30 bitrate=N/A speed= 2.3x    
size=N/A time=00:00:02.81 bitrate=N/A speed=1.87x    
size=N/A time=00:00:03.36 bitrate=N/A speed=1.68x    
size=N/A time=00:00:03.87 bitrate=N/A speed=1.55x    
size=N/A time=00:00:04.38 bitrate=N/A speed=1.46x    
size=N/A time=00:00:04.77 bitrate=N/A speed=1.36x    
size=N/A time=00:00:05.40 bitrate=N/A speed=1.35x    
size=N/A time=00:00:05.79 bitrate=N/A speed=1.29x    
size=N/A time=00:00:06.30 bitrate=N/A speed=1.26x    
size=N/A time=00:00:06.81 bitrate=N/A speed=1.24x    
[hls @ 0x4bafa0] Opening '/mnt/recordings/2025-07-26_22-39-34_hls/segment_0000.ts' for writing
size=N/A time=00:00:07.32 bitrate=N/A speed=1.22x    
size=N/A time=00:00:07.83 bitrate=N/A speed= 1.2x    
size=N/A time=00:00:08.34 bitrate=N/A speed=1.19x    
size=N/A time=00:00:08.86 bitrate=N/A speed=1.18x    
size=N/A time=00:00:09.37 bitrate=N/A speed=1.17x    
size=N/A time=00:00:09.89 bitrate=N/A speed=1.16x    
size=N/A time=00:00:10.39 bitrate=N/A speed=1.15x    
size=N/A time=00:00:10.91 bitrate=N/A speed=1.15x    
size=N/A time=00:00:11.29 bitrate=N/A speed=1.13x    
size=N/A time=00:00:11.80 bitrate=N/A speed=1.12x    
size=N/A time=00:00:12.32 bitrate=N/A speed=1.12x    
size=N/A time=00:00:12.82 bitrate=N/A speed=1.11x    
[hls @ 0x4bafa0] Opening '/mnt/recordings/2025-07-26_22-39-34_hls/segment_0001.ts' for writing
size=N/A time=00:00:13.35 bitrate=N/A speed=1.11x    
size=N/A time=00:00:13.85 bitrate=N/A speed=1.11x    
size=N/A time=00:00:14.36 bitrate=N/A speed= 1.1x    
size=N/A time=00:00:14.88 bitrate=N/A speed= 1.1x    
size=N/A time=00:00:15.39 bitrate=N/A speed= 1.1x    
size=N/A time=00:00:15.90 bitrate=N/A speed= 1.1x    
size=N/A time=00:00:16.28 bitrate=N/A speed=1.08x    
size=N/A time=00:00:16.93 bitrate=N/A speed=1.09x    
size=N/A time=00:00:17.31 bitrate=N/A speed=1.08x    
size=N/A time=00:00:17.82 bitrate=N/A speed=1.08x    
size=N/A time=00:00:18.34 bitrate=N/A speed=1.08x    
size=N/A time=00:00:18.84 bitrate=N/A speed=1.08x    
[hls @ 0x4bafa0] Opening '/mnt/recordings/2025-07-26_22-39-34_hls/segment_0002.ts' for writing
size=N/A time=00:00:19.35 bitrate=N/A speed=1.07x    
size=N/A time=00:00:19.86 bitrate=N/A speed=1.07x    
size=N/A time=00:00:20.38 bitrate=N/A speed=1.07x    
size=N/A time=00:00:20.89 bitrate=N/A speed=1.07x    
size=N/A time=00:00:21.40 bitrate=N/A speed=1.07x    
size=N/A time=00:00:21.92 bitrate=N/A speed=1.07x    
size=N/A time=00:00:22.30 bitrate=N/A speed=1.06x    
size=N/A time=00:00:22.81 bitrate=N/A speed=1.06x    
size=N/A time=00:00:23.33 bitrate=N/A speed=1.06x    
size=N/A time=00:00:23.83 bitrate=N/A speed=1.06x    
size=N/A time=00:00:24.35 bitrate=N/A speed=1.06x    
size=N/A time=00:00:24.86 bitrate=N/A speed=1.06x    
[hls @ 0x4bafa0] Opening '/mnt/recordings/2025-07-26_22-39-34_hls/segment_0003.ts' for writing
size=N/A time=00:00:25.37 bitrate=N/A speed=1.06x    
size=N/A time=00:00:25.88 bitrate=N/A speed=1.06x    
size=N/A time=00:00:26.39 bitrate=N/A speed=1.05x    
size=N/A time=00:00:26.90 bitrate=N/A speed=1.05x    
size=N/A time=00:00:27.29 bitrate=N/A speed=1.05x    
size=N/A time=00:00:27.93 bitrate=N/A speed=1.05x    
size=N/A time=00:00:28.45 bitrate=N/A speed=1.05x    
size=N/A time=00:00:28.95 bitrate=N/A speed=1.05x    
size=N/A time=00:00:29.47 bitrate=N/A speed=1.05x    
size=N/A time=00:00:29.98 bitrate=N/A speed=1.05x    
size=N/A time=00:00:30.36 bitrate=N/A speed=1.04x    
[hls @ 0x4bafa0] Opening '/mnt/recordings/2025-07-26_22-39-34_hls/segment_0004.ts' for writing
size=N/A time=00:00:31.00 bitrate=N/A speed=1.05x    
size=N/A time=00:00:31.38 bitrate=N/A speed=1.04x    
size=N/A time=00:00:31.91 bitrate=N/A speed=1.04x    
size=N/A time=00:00:32.41 bitrate=N/A speed=1.04x    
size=N/A time=00:00:32.92 bitrate=N/A speed=1.04x    
size=N/A time=00:00:33.44 bitrate=N/A speed=1.04x    
size=N/A time=00:00:33.95 bitrate=N/A speed=1.04x    
size=N/A time=00:00:34.46 bitrate=N/A speed=1.04x    
size=N/A time=00:00:34.85 bitrate=N/A speed=1.04x    
size=N/A time=00:00:35.49 bitrate=N/A speed=1.04x    
size=N/A time=00:00:35.87 bitrate=N/A speed=1.04x    
size=N/A time=00:00:36.38 bitrate=N/A speed=1.04x    
size=N/A time=00:00:36.90 bitrate=N/A speed=1.04x    
[hls @ 0x4bafa0] Opening '/mnt/recordings/2025-07-26_22-39-34_hls/segment_0005.ts' for writing
size=N/A time=00:00:37.40 bitrate=N/A speed=1.04x    
size=N/A time=00:00:37.91 bitrate=N/A speed=1.04x    
size=N/A time=00:00:38.42 bitrate=N/A speed=1.04x    
size=N/A time=00:00:38.94 bitrate=N/A speed=1.04x    
size=N/A time=00:00:39.45 bitrate=N/A speed=1.04x    
size=N/A time=00:00:39.97 bitrate=N/A speed=1.04x    
size=N/A time=00:00:40.48 bitrate=N/A speed=1.04x    
size=N/A time=00:00:40.99 bitrate=N/A speed=1.04x    
size=N/A time=00:00:41.37 bitrate=N/A speed=1.03x    
size=N/A time=00:00:41.89 bitrate=N/A speed=1.03x    
size=N/A time=00:00:42.39 bitrate=N/A speed=1.03x    
size=N/A time=00:00:42.91 bitrate=N/A speed=1.03x    
[hls @ 0x4bafa0] Opening '/mnt/recordings/2025-07-26_22-39-34_hls/segment_0006.ts' for writing
size=N/A time=00:00:43.43 bitrate=N/A speed=1.03x    
size=N/A time=00:00:43.93 bitrate=N/A speed=1.03x    
size=N/A time=00:00:44.44 bitrate=N/A speed=1.03x    
size=N/A time=00:00:44.96 bitrate=N/A speed=1.03x    
size=N/A time=00:00:45.47 bitrate=N/A speed=1.03x    
size=N/A time=00:00:45.98 bitrate=N/A speed=1.03x    
size=N/A time=00:00:46.36 bitrate=N/A speed=1.03x    
size=N/A time=00:00:47.01 bitrate=N/A speed=1.03x    
size=N/A time=00:00:47.39 bitrate=N/A speed=1.03x    
size=N/A time=00:00:47.90 bitrate=N/A speed=1.03x    
size=N/A time=00:00:48.42 bitrate=N/A speed=1.03x    
size=N/A time=00:00:48.92 bitrate=N/A speed=1.03x    
[hls @ 0x4bafa0] Opening '/mnt/recordings/2025-07-26_22-39-34_hls/segment_0007.ts' for writing
size=N/A time=00:00:49.43 bitrate=N/A speed=1.03x    
size=N/A time=00:00:49.94 bitrate=N/A speed=1.03x    
size=N/A time=00:00:50.47 bitrate=N/A speed=1.03x    
size=N/A time=00:00:50.97 bitrate=N/A speed=1.03x    
size=N/A time=00:00:51.49 bitrate=N/A speed=1.03x    
size=N/A time=00:00:52.00 bitrate=N/A speed=1.03x    
size=N/A time=00:00:52.51 bitrate=N/A speed=1.03x    
size=N/A time=00:00:52.89 bitrate=N/A speed=1.02x    
size=N/A time=00:00:53.41 bitrate=N/A speed=1.02x    
size=N/A time=00:00:53.91 bitrate=N/A speed=1.02x    
size=N/A time=00:00:54.43 bitrate=N/A speed=1.02x    
[hls @ 0x4bafa0] Opening '/mnt/recordings/2025-07-26_22-39-34_hls/segment_0008.ts' for writing
size=N/A time=00:00:54.94 bitrate=N/A speed=1.02x    
size=N/A time=00:00:55.46 bitrate=N/A speed=1.02x    
size=N/A time=00:00:55.96 bitrate=N/A speed=1.02x    
size=N/A time=00:00:56.48 bitrate=N/A speed=1.02x    
size=N/A time=00:00:56.99 bitrate=N/A speed=1.02x    
size=N/A time=00:00:57.50 bitrate=N/A speed=1.02x    
size=N/A time=00:00:57.88 bitrate=N/A speed=1.02x    
size=N/A time=00:00:58.53 bitrate=N/A speed=1.02x    
size=N/A time=00:00:58.91 bitrate=N/A speed=1.02x    
size=N/A time=00:00:59.42 bitrate=N/A speed=1.02x    
size=N/A time=00:00:59.94 bitrate=N/A speed=1.02x    
size=N/A time=00:01:00.45 bitrate=N/A speed=1.02x    
[hls @ 0x4bafa0] Opening '/mnt/recordings/2025-07-26_22-39-34_hls/segment_0009.ts' for writing
size=N/A time=00:01:00.95 bitrate=N/A speed=1.02x    
size=N/A time=00:01:01.47 bitrate=N/A speed=1.02x    
size=N/A time=00:01:01.98 bitrate=N/A speed=1.02x    
size=N/A time=00:01:02.49 bitrate=N/A speed=1.02x    
size=N/A time=00:01:03.01 bitrate=N/A speed=1.02x    
size=N/A time=00:01:03.52 bitrate=N/A speed=1.02x    
size=N/A time=00:01:03.90 bitrate=N/A speed=1.02x    
size=N/A time=00:01:04.41 bitrate=N/A speed=1.02x    
size=N/A time=00:01:04.93 bitrate=N/A speed=1.02x    
size=N/A time=00:01:05.44 bitrate=N/A speed=1.02x    
size=N/A time=00:01:05.95 bitrate=N/A speed=1.02x    
size=N/A time=00:01:06.47 bitrate=N/A speed=1.02x    
[hls @ 0x4bafa0] Opening '/mnt/recordings/2025-07-26_22-39-34_hls/segment_0010.ts' for writing
size=N/A time=00:01:06.97 bitrate=N/A speed=1.02x    
size=N/A time=00:01:07.48 bitrate=N/A speed=1.02x    
size=N/A time=00:01:08.00 bitrate=N/A speed=1.02x    
[hls @ 0x4bafa0] Opening '/mnt/recordings/2025-07-26_22-39-34_hls/segment_0011.ts' for writing
[out#0/hls @ 0x50b6f0] video:26405KiB audio:270KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:01:08.75 bitrate=N/A speed=1.02x    
Exiting normally, received signal 15.
