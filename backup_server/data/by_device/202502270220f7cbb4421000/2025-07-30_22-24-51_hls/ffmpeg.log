ffmpeg version 7.0.2 Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 14.2.0 (GCC)
  configuration: --disable-stripping --enable-pic --enable-shared --enable-pthreads --cross-prefix=aarch64-oe-linux- --ld='aarch64-oe-linux-gcc -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --cc='aarch64-oe-linux-gcc -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --cxx='aarch64-oe-linux-g++ -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --arch=aarch64 --target-os=linux --enable-cross-compile --extra-cflags=' -O2 -g -fcanon-prefix-map -fmacro-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fmacro-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/recipe-sysroot= -fmacro-prefix-map=/recipe-sysroot= -fdebug-prefix-map=/recipe-sysroot-native= -fmacro-prefix-map=/recipe-sysroot-native= -pipe -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --extra-ldflags='-Wl,-O1 -Wl,--hash-style=gnu -Wl,--as-needed -fcanon-prefix-map -fmacro-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fmacro-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/recipe-sysroot= -fmacro-prefix-map=/recipe-sysroot= -fdebug-prefix-map=/recipe-sysroot-native= -fmacro-prefix-map=/recipe-sysroot-native= ' --sysroot=/recipe-sysroot --libdir=/usr/lib --shlibdir=/usr/lib --datadir=/usr/share/ffmpeg --disable-mipsdsp --disable-mipsdspr2 --cpu=cortex-a53+crc --pkg-config=pkg-config --enable-shared --enable-demuxer=rtp --enable-demuxer=rtsp --enable-protocol=udp --enable-protocol=rtp --enable-libfdk-aac --enable-nonfree --enable-protocol=http --enable-protocol=https --enable-muxer=hls --enable-demuxer=hls --enable-libmp3lame --enable-alsa --disable-altivec --enable-avcodec --enable-avdevice --enable-avfilter --enable-avformat --enable-bzlib --enable-libfdk-aac --enable-nonfree --enable-gpl --disable-libgsm --disable-indev=jack --enable-libopus --disable-libvorbis --enable-lzma --disable-libmfx --enable-libmp3lame --disable-openssl --enable-postproc --disable-sdl2 --disable-libspeex --disable-libsrt --enable-swresample --enable-swscale --enable-libtheora --disable-libv4l2 --disable-vaapi --disable-vdpau --disable-libvpx --enable-libx264 --disable-libx265 --enable-libxcb --enable-outdev=xv --enable-zlib
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
Input #0, rtsp, from 'rtsp://127.0.0.1/h264':
  Metadata:
    title           : 
    comment         : h264
  Duration: N/A, start: 0.000000, bitrate: N/A
  Stream #0:0: Video: h264 (Constrained Baseline), yuv420p(progressive), 1280x720, 30 tbr, 90k tbn
  Stream #0:1: Audio: pcm_s16be, 16000 Hz, mono, s16, 256 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (pcm_s16be (native) -> aac (libfdk_aac))
Press [q] to stop, [?] for help
Output #0, hls, to '/mnt/recordings/2025-07-30_22-24-51_hls/playlist.m3u8':
  Metadata:
    title           : 
    comment         : h264
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (Constrained Baseline), yuv420p(progressive), 1280x720, q=2-31, 30 tbr, 90k tbn
  Stream #0:1: Audio: aac, 8000 Hz, mono, s16, 32 kb/s
      Metadata:
        encoder         : Lavc61.3.100 libfdk_aac
[hls @ 0x4b14d0] Stream 0 packet with pts 189021 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 196222 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 202157 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 203080 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 212007 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 217126 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 221888 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 227916 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 230043 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 233133 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 236005 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 238968 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 242077 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 245053 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 247988 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 251066 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 254052 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 257144 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 260038 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 263001 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 266060 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 268992 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 271991 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 274979 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 278108 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 280969 has duration 0. The segment duration may not be precise.
[hls @ 0x4b14d0] Stream 0 packet with pts 284010 has duration 0. The segment duration may not be precise.
size=N/A time=00:00:03.08 bitrate=N/A speed=6.17x    
size=N/A time=00:00:03.58 bitrate=N/A speed=3.59x    
size=N/A time=00:00:04.10 bitrate=N/A speed=2.73x    
size=N/A time=00:00:04.61 bitrate=N/A speed=2.31x    
size=N/A time=00:00:05.12 bitrate=N/A speed=2.05x    
size=N/A time=00:00:05.64 bitrate=N/A speed=1.88x    
size=N/A time=00:00:06.15 bitrate=N/A speed=1.76x    
[hls @ 0x4b14d0] Opening '/mnt/recordings/2025-07-30_22-24-51_hls/segment_0000.ts' for writing
size=N/A time=00:00:06.66 bitrate=N/A speed=1.67x    
size=N/A time=00:00:07.18 bitrate=N/A speed=1.59x    
size=N/A time=00:00:07.69 bitrate=N/A speed=1.54x    
size=N/A time=00:00:08.07 bitrate=N/A speed=1.47x    
size=N/A time=00:00:08.59 bitrate=N/A speed=1.43x    
size=N/A time=00:00:09.09 bitrate=N/A speed= 1.4x    
size=N/A time=00:00:09.61 bitrate=N/A speed=1.37x    
size=N/A time=00:00:10.11 bitrate=N/A speed=1.35x    
size=N/A time=00:00:10.64 bitrate=N/A speed=1.33x    
size=N/A time=00:00:11.14 bitrate=N/A speed=1.31x    
size=N/A time=00:00:11.66 bitrate=N/A speed=1.29x    
size=N/A time=00:00:12.16 bitrate=N/A speed=1.28x    
[hls @ 0x4b14d0] Opening '/mnt/recordings/2025-07-30_22-24-51_hls/segment_0001.ts' for writing
size=N/A time=00:00:12.68 bitrate=N/A speed=1.27x    
size=N/A time=00:00:13.19 bitrate=N/A speed=1.26x    
size=N/A time=00:00:13.71 bitrate=N/A speed=1.24x    
size=N/A time=00:00:14.09 bitrate=N/A speed=1.22x    
size=N/A time=00:00:14.60 bitrate=N/A speed=1.22x    
size=N/A time=00:00:15.11 bitrate=N/A speed=1.21x    
size=N/A time=00:00:15.63 bitrate=N/A speed= 1.2x    
size=N/A time=00:00:16.13 bitrate=N/A speed=1.19x    
size=N/A time=00:00:16.65 bitrate=N/A speed=1.19x    
size=N/A time=00:00:17.15 bitrate=N/A speed=1.18x    
size=N/A time=00:00:17.67 bitrate=N/A speed=1.18x    
size=N/A time=00:00:18.18 bitrate=N/A speed=1.17x    
[hls @ 0x4b14d0] Opening '/mnt/recordings/2025-07-30_22-24-51_hls/segment_0002.ts' for writing
size=N/A time=00:00:18.70 bitrate=N/A speed=1.17x    
size=N/A time=00:00:19.21 bitrate=N/A speed=1.16x    
size=N/A time=00:00:19.59 bitrate=N/A speed=1.15x    
size=N/A time=00:00:20.10 bitrate=N/A speed=1.15x    
size=N/A time=00:00:20.62 bitrate=N/A speed=1.14x    
size=N/A time=00:00:21.13 bitrate=N/A speed=1.14x    
size=N/A time=00:00:21.64 bitrate=N/A speed=1.14x    
size=N/A time=00:00:22.14 bitrate=N/A speed=1.13x    
size=N/A time=00:00:22.66 bitrate=N/A speed=1.13x    
size=N/A time=00:00:23.17 bitrate=N/A speed=1.13x    
size=N/A time=00:00:23.68 bitrate=N/A speed=1.13x    
size=N/A time=00:00:24.20 bitrate=N/A speed=1.12x    
[hls @ 0x4b14d0] Opening '/mnt/recordings/2025-07-30_22-24-51_hls/segment_0003.ts' for writing
size=N/A time=00:00:24.58 bitrate=N/A speed=1.12x    
size=N/A time=00:00:25.23 bitrate=N/A speed=1.12x    
size=N/A time=00:00:25.61 bitrate=N/A speed=1.11x    
size=N/A time=00:00:26.12 bitrate=N/A speed=1.11x    
size=N/A time=00:00:26.63 bitrate=N/A speed=1.11x    
size=N/A time=00:00:27.15 bitrate=N/A speed=1.11x    
size=N/A time=00:00:27.65 bitrate=N/A speed= 1.1x    
size=N/A time=00:00:28.17 bitrate=N/A speed= 1.1x    
size=N/A time=00:00:28.67 bitrate=N/A speed= 1.1x    
size=N/A time=00:00:29.19 bitrate=N/A speed= 1.1x    
size=N/A time=00:00:29.70 bitrate=N/A speed= 1.1x    
size=N/A time=00:00:30.22 bitrate=N/A speed= 1.1x    
[hls @ 0x4b14d0] Opening '/mnt/recordings/2025-07-30_22-24-51_hls/segment_0004.ts' for writing
size=N/A time=00:00:30.73 bitrate=N/A speed= 1.1x    
size=N/A time=00:00:31.11 bitrate=N/A speed=1.09x    
size=N/A time=00:00:31.62 bitrate=N/A speed=1.09x    
size=N/A time=00:00:32.14 bitrate=N/A speed=1.09x    
size=N/A time=00:00:32.65 bitrate=N/A speed=1.09x    
size=N/A time=00:00:33.16 bitrate=N/A speed=1.09x    
size=N/A time=00:00:33.67 bitrate=N/A speed=1.09x    
size=N/A time=00:00:34.19 bitrate=N/A speed=1.08x    
size=N/A time=00:00:34.69 bitrate=N/A speed=1.08x    
size=N/A time=00:00:35.21 bitrate=N/A speed=1.08x    
size=N/A time=00:00:35.72 bitrate=N/A speed=1.08x    
size=N/A time=00:00:36.10 bitrate=N/A speed=1.08x    
[hls @ 0x4b14d0] Opening '/mnt/recordings/2025-07-30_22-24-51_hls/segment_0005.ts' for writing
size=N/A time=00:00:36.75 bitrate=N/A speed=1.08x    
size=N/A time=00:00:37.12 bitrate=N/A speed=1.07x    
size=N/A time=00:00:37.64 bitrate=N/A speed=1.07x    
size=N/A time=00:00:38.15 bitrate=N/A speed=1.07x    
size=N/A time=00:00:38.67 bitrate=N/A speed=1.07x    
size=N/A time=00:00:39.18 bitrate=N/A speed=1.07x    
size=N/A time=00:00:39.69 bitrate=N/A speed=1.07x    
size=N/A time=00:00:40.20 bitrate=N/A speed=1.07x    
size=N/A time=00:00:40.71 bitrate=N/A speed=1.07x    
size=N/A time=00:00:41.22 bitrate=N/A speed=1.07x    
size=N/A time=00:00:41.74 bitrate=N/A speed=1.07x    
size=N/A time=00:00:42.11 bitrate=N/A speed=1.07x    
[hls @ 0x4b14d0] Opening '/mnt/recordings/2025-07-30_22-24-51_hls/segment_0006.ts' for writing
size=N/A time=00:00:42.63 bitrate=N/A speed=1.06x    
size=N/A time=00:00:43.14 bitrate=N/A speed=1.06x    
size=N/A time=00:00:43.66 bitrate=N/A speed=1.06x    
size=N/A time=00:00:44.17 bitrate=N/A speed=1.06x    
size=N/A time=00:00:44.68 bitrate=N/A speed=1.06x    
size=N/A time=00:00:45.19 bitrate=N/A speed=1.06x    
size=N/A time=00:00:45.71 bitrate=N/A speed=1.06x    
size=N/A time=00:00:46.21 bitrate=N/A speed=1.06x    
size=N/A time=00:00:46.73 bitrate=N/A speed=1.06x    
size=N/A time=00:00:47.24 bitrate=N/A speed=1.06x    
size=N/A time=00:00:47.62 bitrate=N/A speed=1.06x    
[hls @ 0x4b14d0] Opening '/mnt/recordings/2025-07-30_22-24-51_hls/segment_0007.ts' for writing
size=N/A time=00:00:48.26 bitrate=N/A speed=1.06x    
size=N/A time=00:00:48.64 bitrate=N/A speed=1.06x    
size=N/A time=00:00:49.16 bitrate=N/A speed=1.06x    
size=N/A time=00:00:49.67 bitrate=N/A speed=1.06x    
size=N/A time=00:00:50.19 bitrate=N/A speed=1.06x    
size=N/A time=00:00:50.70 bitrate=N/A speed=1.06x    
size=N/A time=00:00:51.21 bitrate=N/A speed=1.05x    
size=N/A time=00:00:51.73 bitrate=N/A speed=1.05x    
size=N/A time=00:00:52.23 bitrate=N/A speed=1.05x    
size=N/A time=00:00:52.75 bitrate=N/A speed=1.05x    
size=N/A time=00:00:53.25 bitrate=N/A speed=1.05x    
size=N/A time=00:00:53.64 bitrate=N/A speed=1.05x    
size=N/A time=00:00:54.16 bitrate=N/A speed=1.05x    
[hls @ 0x4b14d0] Opening '/mnt/recordings/2025-07-30_22-24-51_hls/segment_0008.ts' for writing
size=N/A time=00:00:54.66 bitrate=N/A speed=1.05x    
size=N/A time=00:00:55.18 bitrate=N/A speed=1.05x    
size=N/A time=00:00:55.69 bitrate=N/A speed=1.05x    
size=N/A time=00:00:56.20 bitrate=N/A speed=1.05x    
size=N/A time=00:00:56.72 bitrate=N/A speed=1.05x    
size=N/A time=00:00:57.23 bitrate=N/A speed=1.05x    
size=N/A time=00:00:57.74 bitrate=N/A speed=1.05x    
size=N/A time=00:00:58.25 bitrate=N/A speed=1.05x    
size=N/A time=00:00:58.64 bitrate=N/A speed=1.05x    
size=N/A time=00:00:59.15 bitrate=N/A speed=1.05x    
size=N/A time=00:00:59.65 bitrate=N/A speed=1.05x    
size=N/A time=00:01:00.17 bitrate=N/A speed=1.05x    
[hls @ 0x4b14d0] Opening '/mnt/recordings/2025-07-30_22-24-51_hls/segment_0009.ts' for writing
size=N/A time=00:01:00.68 bitrate=N/A speed=1.04x    
size=N/A time=00:01:01.19 bitrate=N/A speed=1.04x    
size=N/A time=00:01:01.71 bitrate=N/A speed=1.04x    
size=N/A time=00:01:02.22 bitrate=N/A speed=1.04x    
size=N/A time=00:01:02.73 bitrate=N/A speed=1.04x    
size=N/A time=00:01:03.24 bitrate=N/A speed=1.04x    
size=N/A time=00:01:03.63 bitrate=N/A speed=1.04x    
size=N/A time=00:01:04.26 bitrate=N/A speed=1.04x    
size=N/A time=00:01:04.65 bitrate=N/A speed=1.04x    
size=N/A time=00:01:05.16 bitrate=N/A speed=1.04x    
size=N/A time=00:01:05.68 bitrate=N/A speed=1.04x    
size=N/A time=00:01:06.18 bitrate=N/A speed=1.04x    
[hls @ 0x4b14d0] Opening '/mnt/recordings/2025-07-30_22-24-51_hls/segment_0010.ts' for writing
size=N/A time=00:01:06.70 bitrate=N/A speed=1.04x    
size=N/A time=00:01:07.21 bitrate=N/A speed=1.04x    
size=N/A time=00:01:07.72 bitrate=N/A speed=1.04x    
size=N/A time=00:01:08.23 bitrate=N/A speed=1.04x    
size=N/A time=00:01:08.75 bitrate=N/A speed=1.04x    
size=N/A time=00:01:09.26 bitrate=N/A speed=1.04x    
size=N/A time=00:01:09.77 bitrate=N/A speed=1.04x    
size=N/A time=00:01:10.16 bitrate=N/A speed=1.04x    
size=N/A time=00:01:10.67 bitrate=N/A speed=1.04x    
size=N/A time=00:01:11.17 bitrate=N/A speed=1.04x    
size=N/A time=00:01:11.69 bitrate=N/A speed=1.04x    
size=N/A time=00:01:12.20 bitrate=N/A speed=1.04x    
[hls @ 0x4b14d0] Opening '/mnt/recordings/2025-07-30_22-24-51_hls/segment_0011.ts' for writing
size=N/A time=00:01:12.72 bitrate=N/A speed=1.04x    
size=N/A time=00:01:13.22 bitrate=N/A speed=1.04x    
size=N/A time=00:01:13.74 bitrate=N/A speed=1.04x    
size=N/A time=00:01:14.25 bitrate=N/A speed=1.04x    
size=N/A time=00:01:14.76 bitrate=N/A speed=1.04x    
size=N/A time=00:01:15.15 bitrate=N/A speed=1.04x    
size=N/A time=00:01:15.78 bitrate=N/A speed=1.04x    
size=N/A time=00:01:16.17 bitrate=N/A speed=1.04x    
size=N/A time=00:01:16.68 bitrate=N/A speed=1.04x    
size=N/A time=00:01:17.20 bitrate=N/A speed=1.04x    
size=N/A time=00:01:17.71 bitrate=N/A speed=1.04x    
size=N/A time=00:01:18.21 bitrate=N/A speed=1.03x    
[hls @ 0x4b14d0] Opening '/mnt/recordings/2025-07-30_22-24-51_hls/segment_0012.ts' for writing
size=N/A time=00:01:18.74 bitrate=N/A speed=1.03x    
size=N/A time=00:01:19.24 bitrate=N/A speed=1.03x    
size=N/A time=00:01:19.75 bitrate=N/A speed=1.03x    
size=N/A time=00:01:20.27 bitrate=N/A speed=1.03x    
size=N/A time=00:01:20.78 bitrate=N/A speed=1.03x    
size=N/A time=00:01:21.16 bitrate=N/A speed=1.03x    
size=N/A time=00:01:21.68 bitrate=N/A speed=1.03x    
size=N/A time=00:01:22.19 bitrate=N/A speed=1.03x    
size=N/A time=00:01:22.70 bitrate=N/A speed=1.03x    
size=N/A time=00:01:23.21 bitrate=N/A speed=1.03x    
size=N/A time=00:01:23.73 bitrate=N/A speed=1.03x    
size=N/A time=00:01:24.23 bitrate=N/A speed=1.03x    
[hls @ 0x4b14d0] Opening '/mnt/recordings/2025-07-30_22-24-51_hls/segment_0013.ts' for writing
size=N/A time=00:01:24.74 bitrate=N/A speed=1.03x    
size=N/A time=00:01:25.26 bitrate=N/A speed=1.03x    
size=N/A time=00:01:25.77 bitrate=N/A speed=1.03x    
size=N/A time=00:01:26.28 bitrate=N/A speed=1.03x    
size=N/A time=00:01:26.67 bitrate=N/A speed=1.03x    
size=N/A time=00:01:27.31 bitrate=N/A speed=1.03x    
size=N/A time=00:01:27.69 bitrate=N/A speed=1.03x    
size=N/A time=00:01:28.20 bitrate=N/A speed=1.03x    
size=N/A time=00:01:28.72 bitrate=N/A speed=1.03x    
[hls @ 0x4b14d0] Opening '/mnt/recordings/2025-07-30_22-24-51_hls/segment_0014.ts' for writing
[out#0/hls @ 0x4810f0] video:34167KiB audio:351KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:01:29.51 bitrate=N/A speed=1.03x    
Exiting normally, received signal 15.
