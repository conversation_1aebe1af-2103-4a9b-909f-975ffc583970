ffmpeg version 7.0.2 Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 14.2.0 (GCC)
  configuration: --disable-stripping --enable-pic --enable-shared --enable-pthreads --cross-prefix=aarch64-oe-linux- --ld='aarch64-oe-linux-gcc -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --cc='aarch64-oe-linux-gcc -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --cxx='aarch64-oe-linux-g++ -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --arch=aarch64 --target-os=linux --enable-cross-compile --extra-cflags=' -O2 -g -fcanon-prefix-map -fmacro-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fmacro-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/recipe-sysroot= -fmacro-prefix-map=/recipe-sysroot= -fdebug-prefix-map=/recipe-sysroot-native= -fmacro-prefix-map=/recipe-sysroot-native= -pipe -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --extra-ldflags='-Wl,-O1 -Wl,--hash-style=gnu -Wl,--as-needed -fcanon-prefix-map -fmacro-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fmacro-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/recipe-sysroot= -fmacro-prefix-map=/recipe-sysroot= -fdebug-prefix-map=/recipe-sysroot-native= -fmacro-prefix-map=/recipe-sysroot-native= ' --sysroot=/recipe-sysroot --libdir=/usr/lib --shlibdir=/usr/lib --datadir=/usr/share/ffmpeg --disable-mipsdsp --disable-mipsdspr2 --cpu=cortex-a53+crc --pkg-config=pkg-config --enable-shared --enable-demuxer=rtp --enable-demuxer=rtsp --enable-protocol=udp --enable-protocol=rtp --enable-libfdk-aac --enable-nonfree --enable-protocol=http --enable-protocol=https --enable-muxer=hls --enable-demuxer=hls --enable-libmp3lame --enable-alsa --disable-altivec --enable-avcodec --enable-avdevice --enable-avfilter --enable-avformat --enable-bzlib --enable-libfdk-aac --enable-nonfree --enable-gpl --disable-libgsm --disable-indev=jack --enable-libopus --disable-libvorbis --enable-lzma --disable-libmfx --enable-libmp3lame --disable-openssl --enable-postproc --disable-sdl2 --disable-libspeex --disable-libsrt --enable-swresample --enable-swscale --enable-libtheora --disable-libv4l2 --disable-vaapi --disable-vdpau --disable-libvpx --enable-libx264 --disable-libx265 --enable-libxcb --enable-outdev=xv --enable-zlib
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[h264 @ 0x465f00] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465f00] decode_slice_header error
[h264 @ 0x465f00] no frame!
[h264 @ 0x465f00] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465f00] decode_slice_header error
[h264 @ 0x465f00] no frame!
[h264 @ 0x465f00] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465f00] decode_slice_header error
[h264 @ 0x465f00] no frame!
Input #0, rtsp, from 'rtsp://127.0.0.1/h264':
  Metadata:
    title           : 
    comment         : h264
  Duration: N/A, start: 0.000000, bitrate: N/A
  Stream #0:0: Video: h264 (Constrained Baseline), yuv420p(progressive), 1280x720, 90k tbr, 90k tbn
  Stream #0:1: Audio: pcm_s16be, 16000 Hz, mono, s16, 256 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (pcm_s16be (native) -> aac (libfdk_aac))
Press [q] to stop, [?] for help
Output #0, hls, to '/mnt/recordings/2025-07-28_08-42-14_hls/playlist.m3u8':
  Metadata:
    title           : 
    comment         : h264
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (Constrained Baseline), yuv420p(progressive), 1280x720, q=2-31, 90k tbr, 90k tbn
  Stream #0:1: Audio: aac, 8000 Hz, mono, s16, 32 kb/s
      Metadata:
        encoder         : Lavc61.3.100 libfdk_aac
[hls @ 0x4a7bc0] Stream 0 packet with pts 36045 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 43249 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 44172 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 52190 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 53122 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 62042 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 68259 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 69180 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 71075 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 76185 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 77117 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 80025 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 83049 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 86151 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 89142 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 92074 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 95070 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 98094 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 101038 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 104121 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 107073 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 110124 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 113031 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 116036 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 119049 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 122118 has duration 0. The segment duration may not be precise.
[hls @ 0x4a7bc0] Stream 0 packet with pts 125091 has duration 0. The segment duration may not be precise.
size=N/A time=00:00:01.40 bitrate=N/A speed=2.81x    
size=N/A time=00:00:01.91 bitrate=N/A speed=1.91x    
size=N/A time=00:00:02.30 bitrate=N/A speed=1.53x    
size=N/A time=00:00:02.82 bitrate=N/A speed=1.41x    
size=N/A time=00:00:03.33 bitrate=N/A speed=1.33x    
size=N/A time=00:00:03.85 bitrate=N/A speed=1.28x    
size=N/A time=00:00:04.36 bitrate=N/A speed=1.25x    
size=N/A time=00:00:04.87 bitrate=N/A speed=1.22x    
size=N/A time=00:00:05.39 bitrate=N/A speed= 1.2x    
size=N/A time=00:00:05.90 bitrate=N/A speed=1.18x    
[hls @ 0x4a7bc0] Opening '/mnt/recordings/2025-07-28_08-42-14_hls/segment_0000.ts' for writing
size=N/A time=00:00:06.41 bitrate=N/A speed=1.17x    
size=N/A time=00:00:06.91 bitrate=N/A speed=1.15x    
size=N/A time=00:00:07.30 bitrate=N/A speed=1.12x    
size=N/A time=00:00:07.94 bitrate=N/A speed=1.13x    
size=N/A time=00:00:08.33 bitrate=N/A speed=1.11x    
size=N/A time=00:00:08.85 bitrate=N/A speed= 1.1x    
size=N/A time=00:00:09.35 bitrate=N/A speed= 1.1x    
size=N/A time=00:00:09.86 bitrate=N/A speed=1.09x    
size=N/A time=00:00:10.38 bitrate=N/A speed=1.09x    
size=N/A time=00:00:10.89 bitrate=N/A speed=1.09x    
size=N/A time=00:00:11.40 bitrate=N/A speed=1.08x    
size=N/A time=00:00:11.91 bitrate=N/A speed=1.08x    
[hls @ 0x4a7bc0] Opening '/mnt/recordings/2025-07-28_08-42-14_hls/segment_0001.ts' for writing
size=N/A time=00:00:12.43 bitrate=N/A speed=1.08x    
size=N/A time=00:00:12.93 bitrate=N/A speed=1.08x    
size=N/A time=00:00:13.32 bitrate=N/A speed=1.06x    
size=N/A time=00:00:13.84 bitrate=N/A speed=1.06x    
size=N/A time=00:00:14.34 bitrate=N/A speed=1.06x    
size=N/A time=00:00:14.85 bitrate=N/A speed=1.06x    
size=N/A time=00:00:15.36 bitrate=N/A speed=1.06x    
size=N/A time=00:00:15.88 bitrate=N/A speed=1.06x    
size=N/A time=00:00:16.39 bitrate=N/A speed=1.06x    
size=N/A time=00:00:16.90 bitrate=N/A speed=1.06x    
size=N/A time=00:00:17.42 bitrate=N/A speed=1.05x    
size=N/A time=00:00:17.93 bitrate=N/A speed=1.05x    
[hls @ 0x4a7bc0] Opening '/mnt/recordings/2025-07-28_08-42-14_hls/segment_0002.ts' for writing
size=N/A time=00:00:18.44 bitrate=N/A speed=1.05x    
size=N/A time=00:00:18.83 bitrate=N/A speed=1.04x    
size=N/A time=00:00:19.46 bitrate=N/A speed=1.05x    
size=N/A time=00:00:19.85 bitrate=N/A speed=1.04x    
size=N/A time=00:00:20.35 bitrate=N/A speed=1.04x    
size=N/A time=00:00:20.87 bitrate=N/A speed=1.04x    
size=N/A time=00:00:21.38 bitrate=N/A speed=1.04x    
size=N/A time=00:00:21.90 bitrate=N/A speed=1.04x    
size=N/A time=00:00:22.41 bitrate=N/A speed=1.04x    
size=N/A time=00:00:22.92 bitrate=N/A speed=1.04x    
size=N/A time=00:00:23.44 bitrate=N/A speed=1.04x    
size=N/A time=00:00:23.95 bitrate=N/A speed=1.04x    
[hls @ 0x4a7bc0] Opening '/mnt/recordings/2025-07-28_08-42-14_hls/segment_0003.ts' for writing
size=N/A time=00:00:24.45 bitrate=N/A speed=1.04x    
size=N/A time=00:00:24.84 bitrate=N/A speed=1.03x    
size=N/A time=00:00:25.36 bitrate=N/A speed=1.03x    
size=N/A time=00:00:25.86 bitrate=N/A speed=1.03x    
size=N/A time=00:00:26.38 bitrate=N/A speed=1.03x    
size=N/A time=00:00:26.88 bitrate=N/A speed=1.03x    
size=N/A time=00:00:27.41 bitrate=N/A speed=1.03x    
size=N/A time=00:00:27.91 bitrate=N/A speed=1.03x    
size=N/A time=00:00:28.43 bitrate=N/A speed=1.03x    
size=N/A time=00:00:28.94 bitrate=N/A speed=1.03x    
size=N/A time=00:00:29.45 bitrate=N/A speed=1.03x    
size=N/A time=00:00:29.83 bitrate=N/A speed=1.03x    
size=N/A time=00:00:30.35 bitrate=N/A speed=1.03x    
[hls @ 0x4a7bc0] Opening '/mnt/recordings/2025-07-28_08-42-14_hls/segment_0004.ts' for writing
size=N/A time=00:00:30.99 bitrate=N/A speed=1.03x    
size=N/A time=00:00:31.37 bitrate=N/A speed=1.03x    
size=N/A time=00:00:31.88 bitrate=N/A speed=1.03x    
size=N/A time=00:00:32.40 bitrate=N/A speed=1.03x    
size=N/A time=00:00:32.90 bitrate=N/A speed=1.03x    
size=N/A time=00:00:33.42 bitrate=N/A speed=1.03x    
size=N/A time=00:00:33.93 bitrate=N/A speed=1.03x    
size=N/A time=00:00:34.44 bitrate=N/A speed=1.03x    
size=N/A time=00:00:34.95 bitrate=N/A speed=1.03x    
size=N/A time=00:00:35.47 bitrate=N/A speed=1.03x    
size=N/A time=00:00:35.98 bitrate=N/A speed=1.03x    
[hls @ 0x4a7bc0] Opening '/mnt/recordings/2025-07-28_08-42-14_hls/segment_0005.ts' for writing
size=N/A time=00:00:36.36 bitrate=N/A speed=1.02x    
size=N/A time=00:00:36.87 bitrate=N/A speed=1.02x    
size=N/A time=00:00:37.39 bitrate=N/A speed=1.02x    
size=N/A time=00:00:37.89 bitrate=N/A speed=1.02x    
size=N/A time=00:00:38.41 bitrate=N/A speed=1.02x    
size=N/A time=00:00:38.92 bitrate=N/A speed=1.02x    
size=N/A time=00:00:39.43 bitrate=N/A speed=1.02x    
size=N/A time=00:00:39.94 bitrate=N/A speed=1.02x    
size=N/A time=00:00:40.46 bitrate=N/A speed=1.02x    
[hls @ 0x4a7bc0] Opening '/mnt/recordings/2025-07-28_08-42-14_hls/segment_0006.ts' for writing
[out#0/hls @ 0x7ec1d0] video:16315KiB audio:163KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:00:41.19 bitrate=N/A speed=1.03x    
Exiting normally, received signal 15.
