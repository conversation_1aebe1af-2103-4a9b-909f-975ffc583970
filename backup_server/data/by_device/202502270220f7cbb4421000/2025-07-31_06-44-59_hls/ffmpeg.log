ffmpeg version 7.0.2 Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 14.2.0 (GCC)
  configuration: --disable-stripping --enable-pic --enable-shared --enable-pthreads --cross-prefix=aarch64-oe-linux- --ld='aarch64-oe-linux-gcc -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --cc='aarch64-oe-linux-gcc -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --cxx='aarch64-oe-linux-g++ -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --arch=aarch64 --target-os=linux --enable-cross-compile --extra-cflags=' -O2 -g -fcanon-prefix-map -fmacro-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fmacro-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/recipe-sysroot= -fmacro-prefix-map=/recipe-sysroot= -fdebug-prefix-map=/recipe-sysroot-native= -fmacro-prefix-map=/recipe-sysroot-native= -pipe -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --extra-ldflags='-Wl,-O1 -Wl,--hash-style=gnu -Wl,--as-needed -fcanon-prefix-map -fmacro-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fmacro-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/recipe-sysroot= -fmacro-prefix-map=/recipe-sysroot= -fdebug-prefix-map=/recipe-sysroot-native= -fmacro-prefix-map=/recipe-sysroot-native= ' --sysroot=/recipe-sysroot --libdir=/usr/lib --shlibdir=/usr/lib --datadir=/usr/share/ffmpeg --disable-mipsdsp --disable-mipsdspr2 --cpu=cortex-a53+crc --pkg-config=pkg-config --enable-shared --enable-demuxer=rtp --enable-demuxer=rtsp --enable-protocol=udp --enable-protocol=rtp --enable-libfdk-aac --enable-nonfree --enable-protocol=http --enable-protocol=https --enable-muxer=hls --enable-demuxer=hls --enable-libmp3lame --enable-alsa --disable-altivec --enable-avcodec --enable-avdevice --enable-avfilter --enable-avformat --enable-bzlib --enable-libfdk-aac --enable-nonfree --enable-gpl --disable-libgsm --disable-indev=jack --enable-libopus --disable-libvorbis --enable-lzma --disable-libmfx --enable-libmp3lame --disable-openssl --enable-postproc --disable-sdl2 --disable-libspeex --disable-libsrt --enable-swresample --enable-swscale --enable-libtheora --disable-libv4l2 --disable-vaapi --disable-vdpau --disable-libvpx --enable-libx264 --disable-libx265 --enable-libxcb --enable-outdev=xv --enable-zlib
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
Input #0, rtsp, from 'rtsp://127.0.0.1/h264':
  Metadata:
    title           : 
    comment         : h264
  Duration: N/A, start: 0.000000, bitrate: N/A
  Stream #0:0: Video: h264 (Constrained Baseline), yuv420p(progressive), 1280x720, 90k tbr, 90k tbn
  Stream #0:1: Audio: pcm_s16be, 16000 Hz, mono, s16, 256 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (pcm_s16be (native) -> aac (libfdk_aac))
Press [q] to stop, [?] for help
Output #0, hls, to '/mnt/recordings/2025-07-31_06-44-59_hls/playlist.m3u8':
  Metadata:
    title           : 
    comment         : h264
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (Constrained Baseline), yuv420p(progressive), 1280x720, q=2-31, 90k tbr, 90k tbn
  Stream #0:1: Audio: aac, 8000 Hz, mono, s16, 32 kb/s
      Metadata:
        encoder         : Lavc61.3.100 libfdk_aac
[vost#0:0/copy @ 0x5a1c40] Non-monotonic DTS; previous: 240012, current: 239801; changing to 240013. This may result in incorrect timestamps in the output file.
[hls @ 0x568fd0] Stream 0 packet with pts 206957 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 215150 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 216091 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 223910 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 226993 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 230841 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 235001 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 239877 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 240989 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 242848 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 245089 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 247947 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 250974 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 254000 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 257100 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 259967 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 263052 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 263053 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 265974 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 268797 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 271840 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 274857 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 278013 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 280784 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 283824 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 286862 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 289945 has duration 0. The segment duration may not be precise.
[hls @ 0x568fd0] Stream 0 packet with pts 292844 has duration 0. The segment duration may not be precise.
size=N/A time=00:00:03.19 bitrate=N/A speed=6.38x    
size=N/A time=00:00:03.70 bitrate=N/A speed= 3.7x    
size=N/A time=00:00:04.21 bitrate=N/A speed=2.81x    
size=N/A time=00:00:04.73 bitrate=N/A speed=2.37x    
size=N/A time=00:00:05.24 bitrate=N/A speed= 2.1x    
size=N/A time=00:00:05.75 bitrate=N/A speed=1.92x    
[hls @ 0x568fd0] Opening '/mnt/recordings/2025-07-31_06-44-59_hls/segment_0000.ts' for writing
size=N/A time=00:00:06.27 bitrate=N/A speed=1.79x    
size=N/A time=00:00:06.77 bitrate=N/A speed=1.69x    
size=N/A time=00:00:07.30 bitrate=N/A speed=1.62x    
size=N/A time=00:00:07.80 bitrate=N/A speed=1.56x    
size=N/A time=00:00:08.18 bitrate=N/A speed=1.49x    
size=N/A time=00:00:08.70 bitrate=N/A speed=1.45x    
size=N/A time=00:00:09.20 bitrate=N/A speed=1.42x    
size=N/A time=00:00:09.72 bitrate=N/A speed=1.39x    
size=N/A time=00:00:10.23 bitrate=N/A speed=1.36x    
size=N/A time=00:00:10.74 bitrate=N/A speed=1.34x    
size=N/A time=00:00:11.26 bitrate=N/A speed=1.32x    
size=N/A time=00:00:11.77 bitrate=N/A speed=1.31x    
[hls @ 0x568fd0] Opening '/mnt/recordings/2025-07-31_06-44-59_hls/segment_0001.ts' for writing
size=N/A time=00:00:12.29 bitrate=N/A speed=1.29x    
size=N/A time=00:00:12.79 bitrate=N/A speed=1.28x    
size=N/A time=00:00:13.18 bitrate=N/A speed=1.25x    
size=N/A time=00:00:13.69 bitrate=N/A speed=1.24x    
size=N/A time=00:00:14.20 bitrate=N/A speed=1.23x    
size=N/A time=00:00:14.72 bitrate=N/A speed=1.23x    
size=N/A time=00:00:15.22 bitrate=N/A speed=1.22x    
size=N/A time=00:00:15.73 bitrate=N/A speed=1.21x    
size=N/A time=00:00:16.25 bitrate=N/A speed= 1.2x    
size=N/A time=00:00:16.76 bitrate=N/A speed= 1.2x    
size=N/A time=00:00:17.27 bitrate=N/A speed=1.19x    
size=N/A time=00:00:17.79 bitrate=N/A speed=1.18x    
size=N/A time=00:00:18.17 bitrate=N/A speed=1.17x    
[hls @ 0x568fd0] Opening '/mnt/recordings/2025-07-31_06-44-59_hls/segment_0002.ts' for writing
size=N/A time=00:00:18.81 bitrate=N/A speed=1.17x    
size=N/A time=00:00:19.19 bitrate=N/A speed=1.16x    
size=N/A time=00:00:19.71 bitrate=N/A speed=1.16x    
size=N/A time=00:00:20.22 bitrate=N/A speed=1.15x    
size=N/A time=00:00:20.72 bitrate=N/A speed=1.15x    
size=N/A time=00:00:21.24 bitrate=N/A speed=1.15x    
size=N/A time=00:00:21.75 bitrate=N/A speed=1.14x    
size=N/A time=00:00:22.26 bitrate=N/A speed=1.14x    
size=N/A time=00:00:22.78 bitrate=N/A speed=1.14x    
size=N/A time=00:00:23.29 bitrate=N/A speed=1.14x    
size=N/A time=00:00:23.80 bitrate=N/A speed=1.13x    
size=N/A time=00:00:24.31 bitrate=N/A speed=1.13x    
[hls @ 0x568fd0] Opening '/mnt/recordings/2025-07-31_06-44-59_hls/segment_0003.ts' for writing
size=N/A time=00:00:24.70 bitrate=N/A speed=1.12x    
size=N/A time=00:00:25.21 bitrate=N/A speed=1.12x    
size=N/A time=00:00:25.72 bitrate=N/A speed=1.12x    
size=N/A time=00:00:26.24 bitrate=N/A speed=1.12x    
size=N/A time=00:00:26.74 bitrate=N/A speed=1.11x    
size=N/A time=00:00:27.27 bitrate=N/A speed=1.11x    
size=N/A time=00:00:27.77 bitrate=N/A speed=1.11x    
size=N/A time=00:00:28.28 bitrate=N/A speed=1.11x    
size=N/A time=00:00:28.79 bitrate=N/A speed=1.11x    
size=N/A time=00:00:29.31 bitrate=N/A speed= 1.1x    
size=N/A time=00:00:29.69 bitrate=N/A speed= 1.1x    
[hls @ 0x568fd0] Opening '/mnt/recordings/2025-07-31_06-44-59_hls/segment_0004.ts' for writing
size=N/A time=00:00:30.33 bitrate=N/A speed= 1.1x    
size=N/A time=00:00:30.71 bitrate=N/A speed= 1.1x    
size=N/A time=00:00:31.23 bitrate=N/A speed=1.09x    
size=N/A time=00:00:31.74 bitrate=N/A speed=1.09x    
size=N/A time=00:00:32.26 bitrate=N/A speed=1.09x    
size=N/A time=00:00:32.76 bitrate=N/A speed=1.09x    
size=N/A time=00:00:33.28 bitrate=N/A speed=1.09x    
size=N/A time=00:00:33.78 bitrate=N/A speed=1.09x    
size=N/A time=00:00:34.30 bitrate=N/A speed=1.09x    
size=N/A time=00:00:34.81 bitrate=N/A speed=1.09x    
size=N/A time=00:00:35.32 bitrate=N/A speed=1.09x    
size=N/A time=00:00:35.70 bitrate=N/A speed=1.08x    
size=N/A time=00:00:36.22 bitrate=N/A speed=1.08x    
[hls @ 0x568fd0] Opening '/mnt/recordings/2025-07-31_06-44-59_hls/segment_0005.ts' for writing
size=N/A time=00:00:36.73 bitrate=N/A speed=1.08x    
size=N/A time=00:00:37.25 bitrate=N/A speed=1.08x    
size=N/A time=00:00:37.75 bitrate=N/A speed=1.08x    
size=N/A time=00:00:38.27 bitrate=N/A speed=1.08x    
size=N/A time=00:00:38.78 bitrate=N/A speed=1.08x    
size=N/A time=00:00:39.29 bitrate=N/A speed=1.08x    
size=N/A time=00:00:39.80 bitrate=N/A speed=1.07x    
size=N/A time=00:00:40.31 bitrate=N/A speed=1.07x    
size=N/A time=00:00:40.83 bitrate=N/A speed=1.07x    
size=N/A time=00:00:41.21 bitrate=N/A speed=1.07x    
size=N/A time=00:00:41.85 bitrate=N/A speed=1.07x    
size=N/A time=00:00:42.23 bitrate=N/A speed=1.07x    
[hls @ 0x568fd0] Opening '/mnt/recordings/2025-07-31_06-44-59_hls/segment_0006.ts' for writing
size=N/A time=00:00:42.75 bitrate=N/A speed=1.07x    
size=N/A time=00:00:43.26 bitrate=N/A speed=1.07x    
size=N/A time=00:00:43.78 bitrate=N/A speed=1.07x    
size=N/A time=00:00:44.28 bitrate=N/A speed=1.07x    
size=N/A time=00:00:44.80 bitrate=N/A speed=1.07x    
size=N/A time=00:00:45.30 bitrate=N/A speed=1.06x    
size=N/A time=00:00:45.83 bitrate=N/A speed=1.06x    
size=N/A time=00:00:46.33 bitrate=N/A speed=1.06x    
size=N/A time=00:00:46.71 bitrate=N/A speed=1.06x    
size=N/A time=00:00:47.23 bitrate=N/A speed=1.06x    
size=N/A time=00:00:47.74 bitrate=N/A speed=1.06x    
size=N/A time=00:00:48.25 bitrate=N/A speed=1.06x    
[hls @ 0x568fd0] Opening '/mnt/recordings/2025-07-31_06-44-59_hls/segment_0007.ts' for writing
size=N/A time=00:00:48.77 bitrate=N/A speed=1.06x    
size=N/A time=00:00:49.28 bitrate=N/A speed=1.06x    
size=N/A time=00:00:49.79 bitrate=N/A speed=1.06x    
size=N/A time=00:00:50.82 bitrate=N/A speed=1.06x    
size=N/A time=00:00:51.20 bitrate=N/A speed=1.06x    
size=N/A time=00:00:51.71 bitrate=N/A speed=1.06x    
size=N/A time=00:00:52.22 bitrate=N/A speed=1.05x    
size=N/A time=00:00:52.73 bitrate=N/A speed=1.05x    
size=N/A time=00:00:53.24 bitrate=N/A speed=1.05x    
size=N/A time=00:00:53.75 bitrate=N/A speed=1.05x    
size=N/A time=00:00:54.27 bitrate=N/A speed=1.05x    
[hls @ 0x568fd0] Opening '/mnt/recordings/2025-07-31_06-44-59_hls/segment_0008.ts' for writing
size=N/A time=00:00:54.78 bitrate=N/A speed=1.05x    
size=N/A time=00:00:55.29 bitrate=N/A speed=1.05x    
size=N/A time=00:00:55.81 bitrate=N/A speed=1.05x    
size=N/A time=00:00:56.19 bitrate=N/A speed=1.05x    
size=N/A time=00:00:56.70 bitrate=N/A speed=1.05x    
size=N/A time=00:00:57.22 bitrate=N/A speed=1.05x    
size=N/A time=00:00:57.72 bitrate=N/A speed=1.05x    
size=N/A time=00:00:58.24 bitrate=N/A speed=1.05x    
size=N/A time=00:00:58.75 bitrate=N/A speed=1.05x    
size=N/A time=00:00:59.26 bitrate=N/A speed=1.05x    
size=N/A time=00:00:59.77 bitrate=N/A speed=1.05x    
size=N/A time=00:01:00.28 bitrate=N/A speed=1.05x    
[hls @ 0x568fd0] Opening '/mnt/recordings/2025-07-31_06-44-59_hls/segment_0009.ts' for writing
size=N/A time=00:01:00.80 bitrate=N/A speed=1.05x    
size=N/A time=00:01:01.18 bitrate=N/A speed=1.05x    
size=N/A time=00:01:01.69 bitrate=N/A speed=1.05x    
size=N/A time=00:01:02.21 bitrate=N/A speed=1.05x    
size=N/A time=00:01:02.72 bitrate=N/A speed=1.04x    
size=N/A time=00:01:03.23 bitrate=N/A speed=1.04x    
size=N/A time=00:01:03.75 bitrate=N/A speed=1.04x    
size=N/A time=00:01:04.25 bitrate=N/A speed=1.04x    
size=N/A time=00:01:04.76 bitrate=N/A speed=1.04x    
size=N/A time=00:01:05.27 bitrate=N/A speed=1.04x    
size=N/A time=00:01:05.79 bitrate=N/A speed=1.04x    
size=N/A time=00:01:06.30 bitrate=N/A speed=1.04x    
[hls @ 0x568fd0] Opening '/mnt/recordings/2025-07-31_06-44-59_hls/segment_0010.ts' for writing
size=N/A time=00:01:06.81 bitrate=N/A speed=1.04x    
size=N/A time=00:01:07.33 bitrate=N/A speed=1.04x    
size=N/A time=00:01:07.71 bitrate=N/A speed=1.04x    
size=N/A time=00:01:08.22 bitrate=N/A speed=1.04x    
size=N/A time=00:01:08.74 bitrate=N/A speed=1.04x    
size=N/A time=00:01:09.25 bitrate=N/A speed=1.04x    
size=N/A time=00:01:09.76 bitrate=N/A speed=1.04x    
size=N/A time=00:01:10.27 bitrate=N/A speed=1.04x    
size=N/A time=00:01:10.79 bitrate=N/A speed=1.04x    
size=N/A time=00:01:11.29 bitrate=N/A speed=1.04x    
size=N/A time=00:01:11.80 bitrate=N/A speed=1.04x    
size=N/A time=00:01:12.31 bitrate=N/A speed=1.04x    
[hls @ 0x568fd0] Opening '/mnt/recordings/2025-07-31_06-44-59_hls/segment_0011.ts' for writing
size=N/A time=00:01:12.70 bitrate=N/A speed=1.04x    
[hls @ 0x568fd0] Opening '/mnt/recordings/2025-07-31_06-44-59_hls/segment_0012.ts' for writing
[out#0/hls @ 0x5599c0] video:27859KiB audio:289KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:01:13.51 bitrate=N/A speed=1.04x    
Exiting normally, received signal 15.
