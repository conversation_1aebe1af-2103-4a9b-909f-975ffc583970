ffmpeg version 7.0.2 Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 14.2.0 (GCC)
  configuration: --disable-stripping --enable-pic --enable-shared --enable-pthreads --cross-prefix=aarch64-oe-linux- --ld='aarch64-oe-linux-gcc -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --cc='aarch64-oe-linux-gcc -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --cxx='aarch64-oe-linux-g++ -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --arch=aarch64 --target-os=linux --enable-cross-compile --extra-cflags=' -O2 -g -fcanon-prefix-map -fmacro-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fmacro-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/recipe-sysroot= -fmacro-prefix-map=/recipe-sysroot= -fdebug-prefix-map=/recipe-sysroot-native= -fmacro-prefix-map=/recipe-sysroot-native= -pipe -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --extra-ldflags='-Wl,-O1 -Wl,--hash-style=gnu -Wl,--as-needed -fcanon-prefix-map -fmacro-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fmacro-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/recipe-sysroot= -fmacro-prefix-map=/recipe-sysroot= -fdebug-prefix-map=/recipe-sysroot-native= -fmacro-prefix-map=/recipe-sysroot-native= ' --sysroot=/recipe-sysroot --libdir=/usr/lib --shlibdir=/usr/lib --datadir=/usr/share/ffmpeg --disable-mipsdsp --disable-mipsdspr2 --cpu=cortex-a53+crc --pkg-config=pkg-config --enable-shared --enable-demuxer=rtp --enable-demuxer=rtsp --enable-protocol=udp --enable-protocol=rtp --enable-libfdk-aac --enable-nonfree --enable-protocol=http --enable-protocol=https --enable-muxer=hls --enable-demuxer=hls --enable-libmp3lame --enable-alsa --disable-altivec --enable-avcodec --enable-avdevice --enable-avfilter --enable-avformat --enable-bzlib --enable-libfdk-aac --enable-nonfree --enable-gpl --disable-libgsm --disable-indev=jack --enable-libopus --disable-libvorbis --enable-lzma --disable-libmfx --enable-libmp3lame --disable-openssl --enable-postproc --disable-sdl2 --disable-libspeex --disable-libsrt --enable-swresample --enable-swscale --enable-libtheora --disable-libv4l2 --disable-vaapi --disable-vdpau --disable-libvpx --enable-libx264 --disable-libx265 --enable-libxcb --enable-outdev=xv --enable-zlib
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, rtsp, from 'rtsp://127.0.0.1/h264':
  Metadata:
    title           : 
    comment         : h264
  Duration: N/A, start: 0.000000, bitrate: N/A
  Stream #0:0: Video: h264 (Constrained Baseline), yuv420p(progressive), 1280x720, 30 tbr, 90k tbn
  Stream #0:1: Audio: pcm_s16be, 16000 Hz, mono, s16, 256 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (pcm_s16be (native) -> aac (libfdk_aac))
Press [q] to stop, [?] for help
Output #0, hls, to '/mnt/recordings/2025-07-28_00-03-39_hls/playlist.m3u8':
  Metadata:
    title           : 
    comment         : h264
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (Constrained Baseline), yuv420p(progressive), 1280x720, q=2-31, 30 tbr, 90k tbn
  Stream #0:1: Audio: aac, 8000 Hz, mono, s16, 32 kb/s
      Metadata:
        encoder         : Lavc61.3.100 libfdk_aac
[hls @ 0x5982a0] Timestamps are unset in a packet for stream 0. This is deprecated and will stop working in the future. Fix your code to set the timestamps properly
[hls @ 0x5982a0] Stream 0 packet with pts 27039 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 31129 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 35065 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 38966 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 44201 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 49178 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 53946 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 59942 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 62158 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 63089 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 65240 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 68094 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 71069 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 74081 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 77186 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 80122 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 83138 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 86110 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 89022 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 92031 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 95054 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 98186 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 101065 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 104062 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 107071 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 110008 has duration 0. The segment duration may not be precise.
[hls @ 0x5982a0] Stream 0 packet with pts 113124 has duration 0. The segment duration may not be precise.
size=N/A time=00:00:01.28 bitrate=N/A speed=2.57x    
size=N/A time=00:00:01.79 bitrate=N/A speed= 1.8x    
size=N/A time=00:00:02.31 bitrate=N/A speed=1.54x    
size=N/A time=00:00:02.82 bitrate=N/A speed=1.41x    
size=N/A time=00:00:03.32 bitrate=N/A speed=1.33x    
size=N/A time=00:00:03.71 bitrate=N/A speed=1.24x    
size=N/A time=00:00:04.23 bitrate=N/A speed=1.21x    
size=N/A time=00:00:04.73 bitrate=N/A speed=1.18x    
size=N/A time=00:00:05.25 bitrate=N/A speed=1.16x    
size=N/A time=00:00:05.76 bitrate=N/A speed=1.15x    
[hls @ 0x5982a0] Opening '/mnt/recordings/2025-07-28_00-03-39_hls/segment_0000.ts' for writing
size=N/A time=00:00:06.27 bitrate=N/A speed=1.14x    
size=N/A time=00:00:06.78 bitrate=N/A speed=1.13x    
size=N/A time=00:00:07.30 bitrate=N/A speed=1.12x    
size=N/A time=00:00:07.81 bitrate=N/A speed=1.11x    
size=N/A time=00:00:08.32 bitrate=N/A speed=1.11x    
size=N/A time=00:00:08.71 bitrate=N/A speed=1.09x    
size=N/A time=00:00:09.22 bitrate=N/A speed=1.08x    
size=N/A time=00:00:09.73 bitrate=N/A speed=1.08x    
size=N/A time=00:00:10.24 bitrate=N/A speed=1.08x    
size=N/A time=00:00:10.76 bitrate=N/A speed=1.07x    
size=N/A time=00:00:11.26 bitrate=N/A speed=1.07x    
size=N/A time=00:00:11.79 bitrate=N/A speed=1.07x    
[hls @ 0x5982a0] Opening '/mnt/recordings/2025-07-28_00-03-39_hls/segment_0001.ts' for writing
size=N/A time=00:00:12.29 bitrate=N/A speed=1.07x    
size=N/A time=00:00:12.81 bitrate=N/A speed=1.07x    
size=N/A time=00:00:13.31 bitrate=N/A speed=1.06x    
size=N/A time=00:00:13.83 bitrate=N/A speed=1.06x    
size=N/A time=00:00:14.34 bitrate=N/A speed=1.06x    
size=N/A time=00:00:14.85 bitrate=N/A speed=1.06x    
size=N/A time=00:00:15.23 bitrate=N/A speed=1.05x    
size=N/A time=00:00:15.75 bitrate=N/A speed=1.05x    
size=N/A time=00:00:16.26 bitrate=N/A speed=1.05x    
size=N/A time=00:00:16.78 bitrate=N/A speed=1.05x    
size=N/A time=00:00:17.28 bitrate=N/A speed=1.05x    
size=N/A time=00:00:17.80 bitrate=N/A speed=1.05x    
[hls @ 0x5982a0] Opening '/mnt/recordings/2025-07-28_00-03-39_hls/segment_0002.ts' for writing
size=N/A time=00:00:18.30 bitrate=N/A speed=1.04x    
size=N/A time=00:00:18.82 bitrate=N/A speed=1.04x    
size=N/A time=00:00:19.33 bitrate=N/A speed=1.04x    
size=N/A time=00:00:19.84 bitrate=N/A speed=1.04x    
size=N/A time=00:00:20.22 bitrate=N/A speed=1.04x    
size=N/A time=00:00:20.74 bitrate=N/A speed=1.04x    
size=N/A time=00:00:21.25 bitrate=N/A speed=1.04x    
size=N/A time=00:00:21.76 bitrate=N/A speed=1.04x    
size=N/A time=00:00:22.28 bitrate=N/A speed=1.04x    
size=N/A time=00:00:22.79 bitrate=N/A speed=1.03x    
size=N/A time=00:00:23.31 bitrate=N/A speed=1.03x    
size=N/A time=00:00:23.81 bitrate=N/A speed=1.03x    
[hls @ 0x5982a0] Opening '/mnt/recordings/2025-07-28_00-03-39_hls/segment_0003.ts' for writing
size=N/A time=00:00:24.33 bitrate=N/A speed=1.03x    
size=N/A time=00:00:24.83 bitrate=N/A speed=1.03x    
size=N/A time=00:00:25.22 bitrate=N/A speed=1.03x    
size=N/A time=00:00:25.86 bitrate=N/A speed=1.03x    
size=N/A time=00:00:26.24 bitrate=N/A speed=1.03x    
size=N/A time=00:00:26.76 bitrate=N/A speed=1.03x    
size=N/A time=00:00:27.27 bitrate=N/A speed=1.03x    
size=N/A time=00:00:27.78 bitrate=N/A speed=1.03x    
size=N/A time=00:00:28.30 bitrate=N/A speed=1.03x    
size=N/A time=00:00:28.80 bitrate=N/A speed=1.03x    
size=N/A time=00:00:29.32 bitrate=N/A speed=1.03x    
size=N/A time=00:00:29.82 bitrate=N/A speed=1.03x    
[hls @ 0x5982a0] Opening '/mnt/recordings/2025-07-28_00-03-39_hls/segment_0004.ts' for writing
size=N/A time=00:00:30.35 bitrate=N/A speed=1.03x    
size=N/A time=00:00:30.85 bitrate=N/A speed=1.03x    
size=N/A time=00:00:31.24 bitrate=N/A speed=1.02x    
size=N/A time=00:00:31.76 bitrate=N/A speed=1.02x    
size=N/A time=00:00:32.26 bitrate=N/A speed=1.02x    
size=N/A time=00:00:32.77 bitrate=N/A speed=1.02x    
size=N/A time=00:00:33.28 bitrate=N/A speed=1.02x    
size=N/A time=00:00:33.80 bitrate=N/A speed=1.02x    
size=N/A time=00:00:34.31 bitrate=N/A speed=1.02x    
size=N/A time=00:00:34.82 bitrate=N/A speed=1.02x    
size=N/A time=00:00:35.34 bitrate=N/A speed=1.02x    
size=N/A time=00:00:35.85 bitrate=N/A speed=1.02x    
size=N/A time=00:00:36.23 bitrate=N/A speed=1.02x    
[hls @ 0x5982a0] Opening '/mnt/recordings/2025-07-28_00-03-39_hls/segment_0005.ts' for writing
size=N/A time=00:00:36.75 bitrate=N/A speed=1.02x    
size=N/A time=00:00:37.25 bitrate=N/A speed=1.02x    
size=N/A time=00:00:37.77 bitrate=N/A speed=1.02x    
size=N/A time=00:00:38.27 bitrate=N/A speed=1.02x    
size=N/A time=00:00:38.79 bitrate=N/A speed=1.02x    
size=N/A time=00:00:39.30 bitrate=N/A speed=1.02x    
size=N/A time=00:00:39.81 bitrate=N/A speed=1.02x    
size=N/A time=00:00:40.33 bitrate=N/A speed=1.02x    
size=N/A time=00:00:40.84 bitrate=N/A speed=1.02x    
size=N/A time=00:00:41.35 bitrate=N/A speed=1.02x    
size=N/A time=00:00:41.86 bitrate=N/A speed=1.02x    
[hls @ 0x5982a0] Opening '/mnt/recordings/2025-07-28_00-03-39_hls/segment_0006.ts' for writing
size=N/A time=00:00:42.25 bitrate=N/A speed=1.02x    
size=N/A time=00:00:42.76 bitrate=N/A speed=1.02x    
size=N/A time=00:00:43.28 bitrate=N/A speed=1.02x    
size=N/A time=00:00:43.78 bitrate=N/A speed=1.02x    
size=N/A time=00:00:44.29 bitrate=N/A speed=1.02x    
size=N/A time=00:00:44.80 bitrate=N/A speed=1.02x    
size=N/A time=00:00:45.32 bitrate=N/A speed=1.02x    
size=N/A time=00:00:45.83 bitrate=N/A speed=1.02x    
size=N/A time=00:00:46.34 bitrate=N/A speed=1.02x    
size=N/A time=00:00:46.86 bitrate=N/A speed=1.02x    
size=N/A time=00:00:47.37 bitrate=N/A speed=1.02x    
size=N/A time=00:00:47.75 bitrate=N/A speed=1.01x    
size=N/A time=00:00:48.27 bitrate=N/A speed=1.02x    
[hls @ 0x5982a0] Opening '/mnt/recordings/2025-07-28_00-03-39_hls/segment_0007.ts' for writing
size=N/A time=00:00:48.77 bitrate=N/A speed=1.01x    
size=N/A time=00:00:49.29 bitrate=N/A speed=1.02x    
size=N/A time=00:00:49.79 bitrate=N/A speed=1.02x    
size=N/A time=00:00:50.32 bitrate=N/A speed=1.02x    
size=N/A time=00:00:50.82 bitrate=N/A speed=1.02x    
size=N/A time=00:00:51.33 bitrate=N/A speed=1.02x    
size=N/A time=00:00:51.84 bitrate=N/A speed=1.02x    
size=N/A time=00:00:52.36 bitrate=N/A speed=1.02x    
size=N/A time=00:00:52.87 bitrate=N/A speed=1.02x    
size=N/A time=00:00:53.39 bitrate=N/A speed=1.02x    
size=N/A time=00:00:53.77 bitrate=N/A speed=1.01x    
size=N/A time=00:00:54.28 bitrate=N/A speed=1.01x    
[hls @ 0x5982a0] Opening '/mnt/recordings/2025-07-28_00-03-39_hls/segment_0008.ts' for writing
size=N/A time=00:00:54.79 bitrate=N/A speed=1.01x    
[hls @ 0x5982a0] Opening '/mnt/recordings/2025-07-28_00-03-39_hls/segment_0009.ts' for writing
[out#0/hls @ 0x4d01f0] video:22458KiB audio:219KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:00:55.61 bitrate=N/A speed=1.02x    
Exiting normally, received signal 15.
