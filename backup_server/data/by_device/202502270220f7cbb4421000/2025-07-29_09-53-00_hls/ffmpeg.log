ffmpeg version 7.0.2 Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 14.2.0 (GCC)
  configuration: --disable-stripping --enable-pic --enable-shared --enable-pthreads --cross-prefix=aarch64-oe-linux- --ld='aarch64-oe-linux-gcc -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --cc='aarch64-oe-linux-gcc -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --cxx='aarch64-oe-linux-g++ -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --arch=aarch64 --target-os=linux --enable-cross-compile --extra-cflags=' -O2 -g -fcanon-prefix-map -fmacro-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fmacro-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/recipe-sysroot= -fmacro-prefix-map=/recipe-sysroot= -fdebug-prefix-map=/recipe-sysroot-native= -fmacro-prefix-map=/recipe-sysroot-native= -pipe -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --extra-ldflags='-Wl,-O1 -Wl,--hash-style=gnu -Wl,--as-needed -fcanon-prefix-map -fmacro-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fmacro-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/recipe-sysroot= -fmacro-prefix-map=/recipe-sysroot= -fdebug-prefix-map=/recipe-sysroot-native= -fmacro-prefix-map=/recipe-sysroot-native= ' --sysroot=/recipe-sysroot --libdir=/usr/lib --shlibdir=/usr/lib --datadir=/usr/share/ffmpeg --disable-mipsdsp --disable-mipsdspr2 --cpu=cortex-a53+crc --pkg-config=pkg-config --enable-shared --enable-demuxer=rtp --enable-demuxer=rtsp --enable-protocol=udp --enable-protocol=rtp --enable-libfdk-aac --enable-nonfree --enable-protocol=http --enable-protocol=https --enable-muxer=hls --enable-demuxer=hls --enable-libmp3lame --enable-alsa --disable-altivec --enable-avcodec --enable-avdevice --enable-avfilter --enable-avformat --enable-bzlib --enable-libfdk-aac --enable-nonfree --enable-gpl --disable-libgsm --disable-indev=jack --enable-libopus --disable-libvorbis --enable-lzma --disable-libmfx --enable-libmp3lame --disable-openssl --enable-postproc --disable-sdl2 --disable-libspeex --disable-libsrt --enable-swresample --enable-swscale --enable-libtheora --disable-libv4l2 --disable-vaapi --disable-vdpau --disable-libvpx --enable-libx264 --disable-libx265 --enable-libxcb --enable-outdev=xv --enable-zlib
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
Input #0, rtsp, from 'rtsp://127.0.0.1/h264':
  Metadata:
    title           : 
    comment         : h264
  Duration: N/A, start: 0.000000, bitrate: N/A
  Stream #0:0: Video: h264 (Constrained Baseline), yuv420p(progressive), 1280x720, 30 tbr, 90k tbn
  Stream #0:1: Audio: pcm_s16be, 16000 Hz, mono, s16, 256 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (pcm_s16be (native) -> aac (libfdk_aac))
Press [q] to stop, [?] for help
Output #0, hls, to '/mnt/recordings/2025-07-29_09-53-00_hls/playlist.m3u8':
  Metadata:
    title           : 
    comment         : h264
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (Constrained Baseline), yuv420p(progressive), 1280x720, q=2-31, 30 tbr, 90k tbn
  Stream #0:1: Audio: aac, 8000 Hz, mono, s16, 32 kb/s
      Metadata:
        encoder         : Lavc61.3.100 libfdk_aac
[hls @ 0x4d1b20] Stream 0 packet with pts 63102 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 68927 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 74968 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 77951 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 82099 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 86065 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 90066 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 95164 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 99018 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 100168 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 101129 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 103988 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 107021 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 110052 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 113151 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 116122 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 119071 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 121988 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 125027 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 128060 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 131004 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 134080 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 137025 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 140041 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 142983 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 146028 has duration 0. The segment duration may not be precise.
[hls @ 0x4d1b20] Stream 0 packet with pts 149051 has duration 0. The segment duration may not be precise.
size=N/A time=00:00:01.66 bitrate=N/A speed=3.33x    
size=N/A time=00:00:02.18 bitrate=N/A speed=2.18x    
size=N/A time=00:00:02.69 bitrate=N/A speed= 1.8x    
size=N/A time=00:00:03.20 bitrate=N/A speed= 1.6x    
size=N/A time=00:00:03.57 bitrate=N/A speed=1.43x    
size=N/A time=00:00:04.09 bitrate=N/A speed=1.36x    
size=N/A time=00:00:04.59 bitrate=N/A speed=1.31x    
size=N/A time=00:00:05.11 bitrate=N/A speed=1.28x    
size=N/A time=00:00:05.61 bitrate=N/A speed=1.25x    
size=N/A time=00:00:06.14 bitrate=N/A speed=1.23x    
size=N/A time=00:00:06.64 bitrate=N/A speed=1.21x    
[hls @ 0x4d1b20] Opening '/mnt/recordings/2025-07-29_09-53-00_hls/segment_0000.ts' for writing
size=N/A time=00:00:07.16 bitrate=N/A speed=1.19x    
size=N/A time=00:00:07.66 bitrate=N/A speed=1.18x    
size=N/A time=00:00:08.18 bitrate=N/A speed=1.17x    
size=N/A time=00:00:08.56 bitrate=N/A speed=1.14x    
size=N/A time=00:00:09.21 bitrate=N/A speed=1.15x    
size=N/A time=00:00:09.72 bitrate=N/A speed=1.14x    
size=N/A time=00:00:10.10 bitrate=N/A speed=1.12x    
size=N/A time=00:00:10.61 bitrate=N/A speed=1.12x    
size=N/A time=00:00:11.13 bitrate=N/A speed=1.11x    
size=N/A time=00:00:11.63 bitrate=N/A speed=1.11x    
size=N/A time=00:00:12.15 bitrate=N/A speed= 1.1x    
[hls @ 0x4d1b20] Opening '/mnt/recordings/2025-07-29_09-53-00_hls/segment_0001.ts' for writing
[hls @ 0x4d1b20] Opening '/mnt/recordings/2025-07-29_09-53-00_hls/segment_0002.ts' for writing
[out#0/hls @ 0x4acf60] video:4959KiB audio:52KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:00:12.89 bitrate=N/A speed=1.12x    
Exiting normally, received signal 15.
