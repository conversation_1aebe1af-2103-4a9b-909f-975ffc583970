ffmpeg version 7.0.2 Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 14.2.0 (GCC)
  configuration: --disable-stripping --enable-pic --enable-shared --enable-pthreads --cross-prefix=aarch64-oe-linux- --ld='aarch64-oe-linux-gcc -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --cc='aarch64-oe-linux-gcc -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --cxx='aarch64-oe-linux-g++ -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --arch=aarch64 --target-os=linux --enable-cross-compile --extra-cflags=' -O2 -g -fcanon-prefix-map -fmacro-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fmacro-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/recipe-sysroot= -fmacro-prefix-map=/recipe-sysroot= -fdebug-prefix-map=/recipe-sysroot-native= -fmacro-prefix-map=/recipe-sysroot-native= -pipe -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --extra-ldflags='-Wl,-O1 -Wl,--hash-style=gnu -Wl,--as-needed -fcanon-prefix-map -fmacro-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fmacro-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/recipe-sysroot= -fmacro-prefix-map=/recipe-sysroot= -fdebug-prefix-map=/recipe-sysroot-native= -fmacro-prefix-map=/recipe-sysroot-native= ' --sysroot=/recipe-sysroot --libdir=/usr/lib --shlibdir=/usr/lib --datadir=/usr/share/ffmpeg --disable-mipsdsp --disable-mipsdspr2 --cpu=cortex-a53+crc --pkg-config=pkg-config --enable-shared --enable-demuxer=rtp --enable-demuxer=rtsp --enable-protocol=udp --enable-protocol=rtp --enable-libfdk-aac --enable-nonfree --enable-protocol=http --enable-protocol=https --enable-muxer=hls --enable-demuxer=hls --enable-libmp3lame --enable-alsa --disable-altivec --enable-avcodec --enable-avdevice --enable-avfilter --enable-avformat --enable-bzlib --enable-libfdk-aac --enable-nonfree --enable-gpl --disable-libgsm --disable-indev=jack --enable-libopus --disable-libvorbis --enable-lzma --disable-libmfx --enable-libmp3lame --disable-openssl --enable-postproc --disable-sdl2 --disable-libspeex --disable-libsrt --enable-swresample --enable-swscale --enable-libtheora --disable-libv4l2 --disable-vaapi --disable-vdpau --disable-libvpx --enable-libx264 --disable-libx265 --enable-libxcb --enable-outdev=xv --enable-zlib
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
Input #0, rtsp, from 'rtsp://127.0.0.1/h264':
  Metadata:
    title           : 
    comment         : h264
  Duration: N/A, start: 0.000000, bitrate: N/A
  Stream #0:0: Video: h264 (Constrained Baseline), yuv420p(progressive), 1280x720, 90k tbr, 90k tbn
  Stream #0:1: Audio: pcm_s16be, 16000 Hz, mono, s16, 256 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (pcm_s16be (native) -> aac (libfdk_aac))
Press [q] to stop, [?] for help
Output #0, hls, to '/mnt/recordings/2025-07-30_19-33-37_hls/playlist.m3u8':
  Metadata:
    title           : 
    comment         : h264
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (Constrained Baseline), yuv420p(progressive), 1280x720, q=2-31, 90k tbr, 90k tbn
  Stream #0:1: Audio: aac, 8000 Hz, mono, s16, 32 kb/s
      Metadata:
        encoder         : Lavc61.3.100 libfdk_aac
[hls @ 0x8462e0] Stream 0 packet with pts 114027 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 121081 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 122008 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 134121 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 135045 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 143979 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 145039 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 146871 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 149908 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 154020 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 154975 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 157951 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 160980 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 163929 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 167011 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 170005 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 173008 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 175910 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 178952 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 181965 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 186057 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 188031 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 190959 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 193960 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 197043 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 199942 has duration 0. The segment duration may not be precise.
[hls @ 0x8462e0] Stream 0 packet with pts 203025 has duration 0. The segment duration may not be precise.
size=N/A time=00:00:02.16 bitrate=N/A speed=4.33x    
size=N/A time=00:00:02.68 bitrate=N/A speed=2.68x    
size=N/A time=00:00:03.19 bitrate=N/A speed=2.13x    
size=N/A time=00:00:03.70 bitrate=N/A speed=1.85x    
size=N/A time=00:00:04.22 bitrate=N/A speed=1.69x    
size=N/A time=00:00:04.73 bitrate=N/A speed=1.58x    
size=N/A time=00:00:05.25 bitrate=N/A speed= 1.5x    
size=N/A time=00:00:05.75 bitrate=N/A speed=1.44x    
size=N/A time=00:00:06.14 bitrate=N/A speed=1.36x    
size=N/A time=00:00:06.65 bitrate=N/A speed=1.33x    
size=N/A time=00:00:07.15 bitrate=N/A speed= 1.3x    
[hls @ 0x8462e0] Opening '/mnt/recordings/2025-07-30_19-33-37_hls/segment_0000.ts' for writing
size=N/A time=00:00:07.68 bitrate=N/A speed=1.28x    
size=N/A time=00:00:08.18 bitrate=N/A speed=1.26x    
size=N/A time=00:00:08.69 bitrate=N/A speed=1.24x    
size=N/A time=00:00:09.21 bitrate=N/A speed=1.23x    
size=N/A time=00:00:09.72 bitrate=N/A speed=1.22x    
size=N/A time=00:00:10.23 bitrate=N/A speed= 1.2x    
size=N/A time=00:00:10.74 bitrate=N/A speed=1.19x    
size=N/A time=00:00:11.13 bitrate=N/A speed=1.17x    
size=N/A time=00:00:11.76 bitrate=N/A speed=1.18x    
size=N/A time=00:00:12.29 bitrate=N/A speed=1.17x    
size=N/A time=00:00:12.67 bitrate=N/A speed=1.15x    
size=N/A time=00:00:13.18 bitrate=N/A speed=1.15x    
[hls @ 0x8462e0] Opening '/mnt/recordings/2025-07-30_19-33-37_hls/segment_0001.ts' for writing
size=N/A time=00:00:13.70 bitrate=N/A speed=1.14x    
size=N/A time=00:00:14.20 bitrate=N/A speed=1.13x    
size=N/A time=00:00:14.72 bitrate=N/A speed=1.13x    
size=N/A time=00:00:15.22 bitrate=N/A speed=1.13x    
size=N/A time=00:00:15.74 bitrate=N/A speed=1.12x    
size=N/A time=00:00:16.25 bitrate=N/A speed=1.12x    
size=N/A time=00:00:16.76 bitrate=N/A speed=1.12x    
size=N/A time=00:00:17.27 bitrate=N/A speed=1.11x    
size=N/A time=00:00:17.66 bitrate=N/A speed= 1.1x    
size=N/A time=00:00:18.17 bitrate=N/A speed= 1.1x    
size=N/A time=00:00:18.69 bitrate=N/A speed= 1.1x    
size=N/A time=00:00:19.19 bitrate=N/A speed= 1.1x    
[hls @ 0x8462e0] Opening '/mnt/recordings/2025-07-30_19-33-37_hls/segment_0002.ts' for writing
size=N/A time=00:00:19.71 bitrate=N/A speed=1.09x    
size=N/A time=00:00:20.21 bitrate=N/A speed=1.09x    
size=N/A time=00:00:20.73 bitrate=N/A speed=1.09x    
size=N/A time=00:00:21.24 bitrate=N/A speed=1.09x    
size=N/A time=00:00:21.75 bitrate=N/A speed=1.09x    
size=N/A time=00:00:22.26 bitrate=N/A speed=1.08x    
size=N/A time=00:00:22.65 bitrate=N/A speed=1.08x    
size=N/A time=00:00:23.29 bitrate=N/A speed=1.08x    
size=N/A time=00:00:23.67 bitrate=N/A speed=1.07x    
size=N/A time=00:00:24.19 bitrate=N/A speed=1.07x    
size=N/A time=00:00:24.70 bitrate=N/A speed=1.07x    
size=N/A time=00:00:25.20 bitrate=N/A speed=1.07x    
[hls @ 0x8462e0] Opening '/mnt/recordings/2025-07-30_19-33-37_hls/segment_0003.ts' for writing
size=N/A time=00:00:25.72 bitrate=N/A speed=1.07x    
size=N/A time=00:00:26.24 bitrate=N/A speed=1.07x    
size=N/A time=00:00:26.74 bitrate=N/A speed=1.07x    
size=N/A time=00:00:27.25 bitrate=N/A speed=1.07x    
size=N/A time=00:00:27.77 bitrate=N/A speed=1.07x    
size=N/A time=00:00:28.28 bitrate=N/A speed=1.07x    
size=N/A time=00:00:28.79 bitrate=N/A speed=1.07x    
size=N/A time=00:00:29.18 bitrate=N/A speed=1.06x    
size=N/A time=00:00:29.69 bitrate=N/A speed=1.06x    
size=N/A time=00:00:30.21 bitrate=N/A speed=1.06x    
size=N/A time=00:00:30.71 bitrate=N/A speed=1.06x    
size=N/A time=00:00:31.23 bitrate=N/A speed=1.06x    
[hls @ 0x8462e0] Opening '/mnt/recordings/2025-07-30_19-33-37_hls/segment_0004.ts' for writing
size=N/A time=00:00:31.74 bitrate=N/A speed=1.06x    
size=N/A time=00:00:32.26 bitrate=N/A speed=1.06x    
size=N/A time=00:00:32.76 bitrate=N/A speed=1.06x    
size=N/A time=00:00:33.28 bitrate=N/A speed=1.06x    
size=N/A time=00:00:33.78 bitrate=N/A speed=1.05x    
size=N/A time=00:00:34.17 bitrate=N/A speed=1.05x    
size=N/A time=00:00:34.81 bitrate=N/A speed=1.05x    
size=N/A time=00:00:35.19 bitrate=N/A speed=1.05x    
size=N/A time=00:00:35.70 bitrate=N/A speed=1.05x    
size=N/A time=00:00:36.22 bitrate=N/A speed=1.05x    
size=N/A time=00:00:36.73 bitrate=N/A speed=1.05x    
size=N/A time=00:00:37.25 bitrate=N/A speed=1.05x    
[hls @ 0x8462e0] Opening '/mnt/recordings/2025-07-30_19-33-37_hls/segment_0005.ts' for writing
size=N/A time=00:00:37.76 bitrate=N/A speed=1.05x    
size=N/A time=00:00:38.27 bitrate=N/A speed=1.05x    
size=N/A time=00:00:38.77 bitrate=N/A speed=1.05x    
size=N/A time=00:00:39.29 bitrate=N/A speed=1.05x    
size=N/A time=00:00:39.80 bitrate=N/A speed=1.05x    
size=N/A time=00:00:40.18 bitrate=N/A speed=1.04x    
size=N/A time=00:00:40.70 bitrate=N/A speed=1.04x    
size=N/A time=00:00:41.21 bitrate=N/A speed=1.04x    
size=N/A time=00:00:41.72 bitrate=N/A speed=1.04x    
size=N/A time=00:00:42.23 bitrate=N/A speed=1.04x    
size=N/A time=00:00:42.75 bitrate=N/A speed=1.04x    
size=N/A time=00:00:43.26 bitrate=N/A speed=1.04x    
[hls @ 0x8462e0] Opening '/mnt/recordings/2025-07-30_19-33-37_hls/segment_0006.ts' for writing
size=N/A time=00:00:43.76 bitrate=N/A speed=1.04x    
size=N/A time=00:00:44.28 bitrate=N/A speed=1.04x    
size=N/A time=00:00:44.79 bitrate=N/A speed=1.04x    
size=N/A time=00:00:45.30 bitrate=N/A speed=1.04x    
size=N/A time=00:00:45.69 bitrate=N/A speed=1.04x    
size=N/A time=00:00:46.33 bitrate=N/A speed=1.04x    
size=N/A time=00:00:46.71 bitrate=N/A speed=1.04x    
size=N/A time=00:00:47.23 bitrate=N/A speed=1.04x    
size=N/A time=00:00:47.74 bitrate=N/A speed=1.04x    
size=N/A time=00:00:48.25 bitrate=N/A speed=1.04x    
size=N/A time=00:00:48.77 bitrate=N/A speed=1.04x    
size=N/A time=00:00:49.28 bitrate=N/A speed=1.04x    
[hls @ 0x8462e0] Opening '/mnt/recordings/2025-07-30_19-33-37_hls/segment_0007.ts' for writing
size=N/A time=00:00:49.78 bitrate=N/A speed=1.04x    
size=N/A time=00:00:50.29 bitrate=N/A speed=1.04x    
size=N/A time=00:00:50.82 bitrate=N/A speed=1.04x    
size=N/A time=00:00:51.32 bitrate=N/A speed=1.04x    
size=N/A time=00:00:51.70 bitrate=N/A speed=1.03x    
size=N/A time=00:00:52.23 bitrate=N/A speed=1.03x    
size=N/A time=00:00:52.73 bitrate=N/A speed=1.03x    
size=N/A time=00:00:53.24 bitrate=N/A speed=1.03x    
size=N/A time=00:00:53.75 bitrate=N/A speed=1.03x    
size=N/A time=00:00:54.27 bitrate=N/A speed=1.03x    
size=N/A time=00:00:54.78 bitrate=N/A speed=1.03x    
[hls @ 0x8462e0] Opening '/mnt/recordings/2025-07-30_19-33-37_hls/segment_0008.ts' for writing
size=N/A time=00:00:55.29 bitrate=N/A speed=1.03x    
size=N/A time=00:00:55.81 bitrate=N/A speed=1.03x    
size=N/A time=00:00:56.32 bitrate=N/A speed=1.03x    
size=N/A time=00:00:56.70 bitrate=N/A speed=1.03x    
size=N/A time=00:00:57.22 bitrate=N/A speed=1.03x    
size=N/A time=00:00:57.72 bitrate=N/A speed=1.03x    
size=N/A time=00:00:58.24 bitrate=N/A speed=1.03x    
size=N/A time=00:00:58.74 bitrate=N/A speed=1.03x    
size=N/A time=00:00:59.26 bitrate=N/A speed=1.03x    
size=N/A time=00:00:59.77 bitrate=N/A speed=1.03x    
size=N/A time=00:01:00.29 bitrate=N/A speed=1.03x    
size=N/A time=00:01:00.80 bitrate=N/A speed=1.03x    
[hls @ 0x8462e0] Opening '/mnt/recordings/2025-07-30_19-33-37_hls/segment_0009.ts' for writing
size=N/A time=00:01:01.31 bitrate=N/A speed=1.03x    
size=N/A time=00:01:01.69 bitrate=N/A speed=1.03x    
size=N/A time=00:01:02.33 bitrate=N/A speed=1.03x    
size=N/A time=00:01:02.72 bitrate=N/A speed=1.03x    
size=N/A time=00:01:03.23 bitrate=N/A speed=1.03x    
size=N/A time=00:01:03.73 bitrate=N/A speed=1.03x    
size=N/A time=00:01:04.25 bitrate=N/A speed=1.03x    
size=N/A time=00:01:04.76 bitrate=N/A speed=1.03x    
size=N/A time=00:01:05.27 bitrate=N/A speed=1.03x    
size=N/A time=00:01:05.79 bitrate=N/A speed=1.03x    
size=N/A time=00:01:06.30 bitrate=N/A speed=1.03x    
size=N/A time=00:01:06.82 bitrate=N/A speed=1.03x    
[hls @ 0x8462e0] Opening '/mnt/recordings/2025-07-30_19-33-37_hls/segment_0010.ts' for writing
size=N/A time=00:01:07.33 bitrate=N/A speed=1.03x    
size=N/A time=00:01:07.84 bitrate=N/A speed=1.03x    
size=N/A time=00:01:08.22 bitrate=N/A speed=1.02x    
size=N/A time=00:01:08.74 bitrate=N/A speed=1.02x    
size=N/A time=00:01:09.24 bitrate=N/A speed=1.02x    
size=N/A time=00:01:09.76 bitrate=N/A speed=1.02x    
size=N/A time=00:01:10.26 bitrate=N/A speed=1.02x    
size=N/A time=00:01:10.78 bitrate=N/A speed=1.02x    
size=N/A time=00:01:11.29 bitrate=N/A speed=1.02x    
size=N/A time=00:01:11.81 bitrate=N/A speed=1.02x    
size=N/A time=00:01:12.31 bitrate=N/A speed=1.02x    
size=N/A time=00:01:12.83 bitrate=N/A speed=1.02x    
size=N/A time=00:01:13.21 bitrate=N/A speed=1.02x    
[hls @ 0x8462e0] Opening '/mnt/recordings/2025-07-30_19-33-37_hls/segment_0011.ts' for writing
[hls @ 0x8462e0] Opening '/mnt/recordings/2025-07-30_19-33-37_hls/segment_0012.ts' for writing
[out#0/hls @ 0x842660] video:28803KiB audio:291KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:01:14.04 bitrate=N/A speed=1.03x    
Exiting normally, received signal 15.
