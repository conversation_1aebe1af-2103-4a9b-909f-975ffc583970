ffmpeg version 7.0.2 Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 14.2.0 (GCC)
  configuration: --disable-stripping --enable-pic --enable-shared --enable-pthreads --cross-prefix=aarch64-oe-linux- --ld='aarch64-oe-linux-gcc -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --cc='aarch64-oe-linux-gcc -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --cxx='aarch64-oe-linux-g++ -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --arch=aarch64 --target-os=linux --enable-cross-compile --extra-cflags=' -O2 -g -fcanon-prefix-map -fmacro-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fmacro-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/recipe-sysroot= -fmacro-prefix-map=/recipe-sysroot= -fdebug-prefix-map=/recipe-sysroot-native= -fmacro-prefix-map=/recipe-sysroot-native= -pipe -mcpu=cortex-a53+crc -mbranch-protection=standard --sysroot=/recipe-sysroot' --extra-ldflags='-Wl,-O1 -Wl,--hash-style=gnu -Wl,--as-needed -fcanon-prefix-map -fmacro-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/ffmpeg-7.0.2=/usr/src/debug/ffmpeg/7.0.2 -fmacro-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/build=/usr/src/debug/ffmpeg/7.0.2 -fdebug-prefix-map=/recipe-sysroot= -fmacro-prefix-map=/recipe-sysroot= -fdebug-prefix-map=/recipe-sysroot-native= -fmacro-prefix-map=/recipe-sysroot-native= ' --sysroot=/recipe-sysroot --libdir=/usr/lib --shlibdir=/usr/lib --datadir=/usr/share/ffmpeg --disable-mipsdsp --disable-mipsdspr2 --cpu=cortex-a53+crc --pkg-config=pkg-config --enable-shared --enable-demuxer=rtp --enable-demuxer=rtsp --enable-protocol=udp --enable-protocol=rtp --enable-libfdk-aac --enable-nonfree --enable-protocol=http --enable-protocol=https --enable-muxer=hls --enable-demuxer=hls --enable-libmp3lame --enable-alsa --disable-altivec --enable-avcodec --enable-avdevice --enable-avfilter --enable-avformat --enable-bzlib --enable-libfdk-aac --enable-nonfree --enable-gpl --disable-libgsm --disable-indev=jack --enable-libopus --disable-libvorbis --enable-lzma --disable-libmfx --enable-libmp3lame --disable-openssl --enable-postproc --disable-sdl2 --disable-libspeex --disable-libsrt --enable-swresample --enable-swscale --enable-libtheora --disable-libv4l2 --disable-vaapi --disable-vdpau --disable-libvpx --enable-libx264 --disable-libx265 --enable-libxcb --enable-outdev=xv --enable-zlib
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
[h264 @ 0x465ef0] non-existing PPS 0 referenced
    Last message repeated 1 times
[h264 @ 0x465ef0] decode_slice_header error
[h264 @ 0x465ef0] no frame!
Input #0, rtsp, from 'rtsp://127.0.0.1/h264':
  Metadata:
    title           : 
    comment         : h264
  Duration: N/A, start: 0.000000, bitrate: N/A
  Stream #0:0: Video: h264 (Constrained Baseline), yuv420p(progressive), 1280x720, 30 tbr, 90k tbn
  Stream #0:1: Audio: pcm_s16be, 16000 Hz, mono, s16, 256 kb/s
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (pcm_s16be (native) -> aac (libfdk_aac))
Press [q] to stop, [?] for help
Output #0, hls, to '/mnt/recordings/2025-07-30_11-34-09_hls/playlist.m3u8':
  Metadata:
    title           : 
    comment         : h264
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (Constrained Baseline), yuv420p(progressive), 1280x720, q=2-31, 30 tbr, 90k tbn
  Stream #0:1: Audio: aac, 8000 Hz, mono, s16, 32 kb/s
      Metadata:
        encoder         : Lavc61.3.100 libfdk_aac
[hls @ 0x512e50] Stream 0 packet with pts 41977 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 50158 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 51085 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 56023 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 61116 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 67196 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 71990 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 74972 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 77057 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 80137 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 83051 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 86184 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 89104 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 92173 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 95061 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 98080 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 101088 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 104284 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 107100 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 110053 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 113078 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 116199 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 119069 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 122133 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 125094 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 128146 has duration 0. The segment duration may not be precise.
[hls @ 0x512e50] Stream 0 packet with pts 131035 has duration 0. The segment duration may not be precise.
size=N/A time=00:00:01.40 bitrate=N/A speed=2.81x    
size=N/A time=00:00:01.91 bitrate=N/A speed=1.91x    
size=N/A time=00:00:02.42 bitrate=N/A speed=1.62x    
[vost#0:0/copy @ 0x8094c0] Non-monotonic DTS; previous: 255893, current: 255866; changing to 255894. This may result in incorrect timestamps in the output file.
size=N/A time=00:00:02.94 bitrate=N/A speed=1.47x    
size=N/A time=00:00:03.44 bitrate=N/A speed=1.38x    
size=N/A time=00:00:03.96 bitrate=N/A speed=1.32x    
size=N/A time=00:00:04.46 bitrate=N/A speed=1.28x    
size=N/A time=00:00:04.85 bitrate=N/A speed=1.21x    
size=N/A time=00:00:05.36 bitrate=N/A speed=1.19x    
size=N/A time=00:00:05.88 bitrate=N/A speed=1.18x    
size=N/A time=00:00:06.40 bitrate=N/A speed=1.16x    
[hls @ 0x512e50] Opening '/mnt/recordings/2025-07-30_11-34-09_hls/segment_0000.ts' for writing
size=N/A time=00:00:06.90 bitrate=N/A speed=1.15x    
size=N/A time=00:00:07.41 bitrate=N/A speed=1.14x    
size=N/A time=00:00:07.93 bitrate=N/A speed=1.13x    
size=N/A time=00:00:08.43 bitrate=N/A speed=1.12x    
size=N/A time=00:00:08.95 bitrate=N/A speed=1.12x    
size=N/A time=00:00:09.45 bitrate=N/A speed=1.11x    
size=N/A time=00:00:09.84 bitrate=N/A speed=1.09x    
size=N/A time=00:00:10.48 bitrate=N/A speed= 1.1x    
size=N/A time=00:00:10.87 bitrate=N/A speed=1.09x    
[hls @ 0x512e50] Opening '/mnt/recordings/2025-07-30_11-34-09_hls/segment_0001.ts' for writing
[out#0/hls @ 0x492960] video:5077KiB audio:47KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:00:11.70 bitrate=N/A speed=1.11x    
Exiting normally, received signal 15.
