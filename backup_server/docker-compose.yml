version: "3.8"

services:
  backup-server:
    build:
      context: .
      dockerfile: Dockerfile
      network: host # 构建时使用主机网络
    ports:
      - "8080:8080"
    volumes:
      - ./data:/app/data
      - ./config:/app/config
    environment:
      - TZ=Asia/Shanghai
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/storage/stats"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    env_file:
      - .env

volumes:
  backup_data:

networks:
  default:
    name: backup_network
