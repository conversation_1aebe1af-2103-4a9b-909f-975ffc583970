# 视频备份系统配置文件示例
# 复制此文件为 config.yaml 并修改相应配置

# 服务器配置
server:
  host: "0.0.0.0"          # 服务监听地址，0.0.0.0表示监听所有网卡
  port: "8080"             # 服务端口

# 后端服务配置
backend:
  url: "http://your-backend-server"  # 替换为实际的backend_server地址
  auth_token: "FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"

# MinIO配置（将从后端API自动获取，这里作为备用配置）
minio:
  endpoint: "***************:9000"
  access_key: "animsuper"
  secret_key: "4vbtCeEQgcN2uB"
  use_ssl: false
  bucket: "records"

# 存储配置
storage:
  data_path: "./data"      # 本地数据存储路径，可以是绝对路径或相对路径

# 下载配置
download:
  max_concurrent: 5        # 最大并发下载数，根据网络带宽和系统性能调整
  chunk_size: 1048576      # 下载块大小，单位字节 (1MB = 1048576)

# 代理配置（如果需要通过代理访问网络）
proxy:
  http_proxy: "http://127.0.0.1:7897"      # HTTP代理地址
  https_proxy: "http://127.0.0.1:7897"     # HTTPS代理地址  
  all_proxy: "socks5://127.0.0.1:7897"     # SOCKS5代理地址

# 配置说明：
#
# 1. server.host:
#    - "0.0.0.0": 监听所有网卡，可从任何IP访问
#    - "127.0.0.1": 只监听本地回环，只能从本机访问
#    - "*************": 监听指定IP
#
# 2. backend.url:
#    - 必须替换为实际的backend_server地址
#    - 例如: "http://*************:8080" 或 "https://api.example.com"
#
# 3. storage.data_path:
#    - 相对路径: "./data" (相对于程序运行目录)
#    - 绝对路径: "/home/<USER>/backup_data"
#    - 确保目录有足够的磁盘空间
#
# 4. download.max_concurrent:
#    - 建议值: 3-10，根据网络带宽调整
#    - 过高可能导致网络拥塞或服务器限制
#    - 过低会影响下载效率
#
# 5. download.chunk_size:
#    - 1MB (1048576): 适合大多数情况
#    - 2MB (2097152): 适合高速网络
#    - 512KB (524288): 适合较慢网络
#
# 6. proxy配置:
#    - 如果不需要代理，可以注释掉或设置为空字符串
#    - 支持HTTP、HTTPS和SOCKS5代理
#    - 格式: "protocol://host:port"
