# Backup Server Configuration
TZ=Asia/Shanghai
DATA_PATH=./data
MAX_CONCURRENT_DOWNLOADS=5

# Backend Server Connection (required)
BACKEND_URL=http://localhost:5678
BACKEND_USERNAME=your_username
BACKEND_PASSWORD=your_password

# MinIO Configuration (will be fetched from backend)
# These will be automatically configured
MINIO_ENDPOINT=
MINIO_ACCESS_KEY=
MINIO_SECRET_KEY=
MINIO_BUCKET=

# Proxy Configuration (for Docker build)
http_proxy=http://127.0.0.1:7897
https_proxy=http://127.0.0.1:7897
no_proxy=localhost,127.0.0.1
