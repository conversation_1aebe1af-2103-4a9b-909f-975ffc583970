# Backup Server Configuration
TZ=Asia/Shanghai
DATA_PATH=./data
MAX_CONCURRENT_DOWNLOADS=5

# Backend Server Connection (required)
# Production API endpoint
BACKEND_URL=https://api.caby.care
BACKEND_USERNAME=your_username
BACKEND_PASSWORD=your_password

# MinIO Configuration (will be fetched from backend)
# These will be automatically configured
MINIO_ENDPOINT=
MINIO_ACCESS_KEY=
MINIO_SECRET_KEY=
MINIO_BUCKET=

# Proxy Configuration
# For Docker containers: use host.docker.internal
# For local development: use 127.0.0.1
http_proxy=http://host.docker.internal:7897
https_proxy=http://host.docker.internal:7897
no_proxy=localhost,127.0.0.1,host.docker.internal

# Runtime Proxy Configuration (for application)
APP_HTTP_PROXY=http://host.docker.internal:7897
APP_HTTPS_PROXY=http://host.docker.internal:7897
