package models

import "time"

// BackupVideoInfo 备份视频信息
type BackupVideoInfo struct {
	VideoID      string  `json:"video_id"`
	DeviceID     string  `json:"device_id"`
	AnimalID     string  `json:"animal_id"`
	StartTime    int64   `json:"start_time"`
	EndTime      *int64  `json:"end_time"`
	WeightLitter float64 `json:"weight_litter"`
	WeightCat    float64 `json:"weight_cat"`
	WeightWaste  float64 `json:"weight_waste"`
	// MinIO路径信息
	VideoFolder    string `json:"video_folder"`    // 视频文件夹名称，如：2025-01-23_20-46-05_hls
	PlaylistPath   string `json:"playlist_path"`   // playlist.m3u8的完整路径
	WeightDataPath string `json:"weight_data_path"` // 重量数据文件路径
	// 元数据
	BehaviorType  string  `json:"behavior_type"`
	CatConfidence float64 `json:"cat_confidence"`
	CreatedAt     string  `json:"created_at"`
}

// BackupListResponse 备份列表响应
type BackupListResponse struct {
	Videos     []BackupVideoInfo `json:"videos"`
	Total      int64             `json:"total"`
	Page       int               `json:"page"`
	PageSize   int               `json:"page_size"`
	TotalPages int               `json:"total_pages"`
}

// MinIOConfig MinIO配置信息
type MinIOConfig struct {
	Endpoint  string `json:"endpoint"`
	AccessKey string `json:"access_key"`
	SecretKey string `json:"secret_key"`
	UseSSL    bool   `json:"use_ssl"`
	Bucket    string `json:"bucket"`
}

// DownloadTask 下载任务
type DownloadTask struct {
	ID          string                `json:"id"`
	VideoInfo   BackupVideoInfo       `json:"video_info"`
	Status      DownloadStatus        `json:"status"`
	Progress    float64               `json:"progress"`
	Error       string                `json:"error,omitempty"`
	CreatedAt   time.Time             `json:"created_at"`
	StartedAt   *time.Time            `json:"started_at,omitempty"`
	CompletedAt *time.Time            `json:"completed_at,omitempty"`
	Files       []DownloadFileStatus  `json:"files"`
}

// DownloadStatus 下载状态
type DownloadStatus string

const (
	StatusPending     DownloadStatus = "pending"
	StatusRunning     DownloadStatus = "running"
	StatusDownloading DownloadStatus = "downloading"
	StatusCompleted   DownloadStatus = "completed"
	StatusFailed      DownloadStatus = "failed"
	StatusCancelled   DownloadStatus = "cancelled"
	StatusPaused      DownloadStatus = "paused"
)

// DownloadFileStatus 文件下载状态
type DownloadFileStatus struct {
	FileName string         `json:"file_name"`
	Status   DownloadStatus `json:"status"`
	Progress float64        `json:"progress"`
	Size     int64          `json:"size"`
	Error    string         `json:"error,omitempty"`
}

// VideoListRequest 视频列表请求
type VideoListRequest struct {
	StartTime *int64  `form:"start_time"`
	EndTime   *int64  `form:"end_time"`
	DeviceID  *string `form:"device_id"`
	AnimalID  *string `form:"animal_id"`
	Page      int     `form:"page"`
	PageSize  int     `form:"page_size"`
}

// DownloadRequest 下载请求
type DownloadRequest struct {
	VideoIDs []string `json:"video_ids" binding:"required"`
}

// TaskStatusResponse 任务状态响应
type TaskStatusResponse struct {
	Tasks []DownloadTask `json:"tasks"`
	Total int            `json:"total"`
}

// LocalVideoInfo 本地视频信息
type LocalVideoInfo struct {
	VideoID      string    `json:"video_id"`
	DeviceID     string    `json:"device_id"`
	AnimalID     string    `json:"animal_id"`
	StartTime    int64     `json:"start_time"`
	LocalPath    string    `json:"local_path"`
	DownloadedAt time.Time `json:"downloaded_at"`
	FileSize     int64     `json:"file_size"`
	FileCount    int       `json:"file_count"`
}

// StorageStats 存储统计
type StorageStats struct {
	TotalVideos   int   `json:"total_videos"`
	TotalSize     int64 `json:"total_size"`
	TotalFiles    int   `json:"total_files"`
	DeviceCount   int   `json:"device_count"`
	CatCount      int   `json:"cat_count"`
	LastUpdated   time.Time `json:"last_updated"`
}

// CatInfo 猫咪信息
type CatInfo struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	AvatarURL   string `json:"avatar_url,omitempty"`
	VideoCount  int    `json:"video_count"`
	LastVideoAt *int64 `json:"last_video_at,omitempty"`
}

// CatListResponse 猫咪列表响应
type CatListResponse struct {
	Cats  []CatInfo `json:"cats"`
	Total int       `json:"total"`
}

// CatVideoListRequest 按猫咪获取视频列表请求
type CatVideoListRequest struct {
	CatID     string `form:"cat_id" binding:"required"`
	StartTime *int64 `form:"start_time"`
	EndTime   *int64 `form:"end_time"`
	Page      int    `form:"page"`
	PageSize  int    `form:"page_size"`
}

// CatVideoListResponse 猫咪视频列表响应
type CatVideoListResponse struct {
	CatInfo    CatInfo           `json:"cat_info"`
	Videos     []BackupVideoInfo `json:"videos"`
	Total      int64             `json:"total"`
	Page       int               `json:"page"`
	PageSize   int               `json:"page_size"`
	TotalPages int               `json:"total_pages"`
}
