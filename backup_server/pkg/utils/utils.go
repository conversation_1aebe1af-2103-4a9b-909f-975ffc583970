package utils

import (
	"crypto/md5"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
)

// GenerateTaskID 生成任务ID
func GenerateTaskID() string {
	return uuid.New().String()
}

// EnsureDir 确保目录存在
func EnsureDir(dir string) error {
	return os.MkdirAll(dir, 0755)
}

// FileExists 检查文件是否存在
func FileExists(path string) bool {
	_, err := os.Stat(path)
	return !os.IsNotExist(err)
}

// GetFileSize 获取文件大小
func GetFileSize(path string) (int64, error) {
	info, err := os.Stat(path)
	if err != nil {
		return 0, err
	}
	return info.Size(), nil
}

// FormatBytes 格式化字节数
func FormatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// FormatDuration 格式化时间间隔
func FormatDuration(d time.Duration) string {
	if d < time.Minute {
		return fmt.Sprintf("%.1fs", d.Seconds())
	}
	if d < time.Hour {
		return fmt.Sprintf("%.1fm", d.Minutes())
	}
	return fmt.Sprintf("%.1fh", d.Hours())
}

// SanitizeFileName 清理文件名
func SanitizeFileName(name string) string {
	// 替换不安全的字符
	unsafe := []string{"/", "\\", ":", "*", "?", "\"", "<", ">", "|"}
	for _, char := range unsafe {
		name = strings.ReplaceAll(name, char, "_")
	}
	return name
}

// GetVideoLocalPath 获取视频本地存储路径
func GetVideoLocalPath(dataPath, deviceID, videoFolder string) string {
	return filepath.Join(dataPath, "by_device", deviceID, videoFolder)
}

// GetCatLocalPath 获取按猫分类的本地存储路径
func GetCatLocalPath(dataPath, catID, videoFolder string) string {
	return filepath.Join(dataPath, "by_cat", catID, videoFolder)
}

// CalculateMD5 计算文件MD5
func CalculateMD5(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := md5.New()
	buffer := make([]byte, 8192)
	for {
		n, err := file.Read(buffer)
		if n > 0 {
			hash.Write(buffer[:n])
		}
		if err != nil {
			break
		}
	}

	return fmt.Sprintf("%x", hash.Sum(nil)), nil
}

// CreateSymlink 创建符号链接
func CreateSymlink(target, link string) error {
	// 确保目标目录存在
	linkDir := filepath.Dir(link)
	if err := EnsureDir(linkDir); err != nil {
		return err
	}

	// 如果链接已存在，先删除
	if FileExists(link) {
		os.Remove(link)
	}

	return os.Symlink(target, link)
}

// ParseTimestamp 解析时间戳
func ParseTimestamp(timestamp int64) time.Time {
	return time.Unix(timestamp, 0)
}

// FormatTimestamp 格式化时间戳
func FormatTimestamp(timestamp int64) string {
	return ParseTimestamp(timestamp).Format("2006-01-02 15:04:05")
}

// GetRelativePath 获取相对路径
func GetRelativePath(basePath, targetPath string) (string, error) {
	return filepath.Rel(basePath, targetPath)
}

// CountFiles 统计目录中的文件数量
func CountFiles(dir string) (int, error) {
	count := 0
	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			count++
		}
		return nil
	})
	return count, err
}

// GetDirSize 获取目录大小
func GetDirSize(dir string) (int64, error) {
	var size int64
	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			size += info.Size()
		}
		return nil
	})
	return size, err
}
