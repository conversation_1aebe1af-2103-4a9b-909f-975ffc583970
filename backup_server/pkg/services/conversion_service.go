package services

import (
	"backup_server/pkg/models"
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"sync"
	"time"
	"io"
	"bufio"
	"strings"

	"github.com/google/uuid"
)

// ConversionService 转换服务
type ConversionService struct {
	tasks      map[string]*models.ConversionTask
	tasksMutex sync.RWMutex
	queue      chan *models.ConversionTask
	workers    int
	ctx        context.Context
	cancel     context.CancelFunc
	storage    *StorageService
}

// NewConversionService 创建转换服务
func NewConversionService(storage *StorageService) *ConversionService {
	ctx, cancel := context.WithCancel(context.Background())
	
	service := &ConversionService{
		tasks:   make(map[string]*models.ConversionTask),
		queue:   make(chan *models.ConversionTask, 100),
		workers: 2, // 同时进行2个转换任务
		ctx:     ctx,
		cancel:  cancel,
		storage: storage,
	}

	// 启动工作协程
	for i := 0; i < service.workers; i++ {
		go service.worker()
	}

	return service
}

// worker 工作协程
func (s *ConversionService) worker() {
	for {
		select {
		case <-s.ctx.Done():
			return
		case task := <-s.queue:
			if task.Status == models.ConversionStatusPending {
				s.processTask(task)
			}
		}
	}
}

// processTask 处理转换任务
func (s *ConversionService) processTask(task *models.ConversionTask) {
	s.tasksMutex.Lock()
	task.Status = models.ConversionStatusRunning
	now := time.Now()
	task.StartedAt = &now
	s.tasksMutex.Unlock()

	log.Printf("开始转换任务: %s, 视频ID: %s", task.ID, task.VideoID)

	// 检查输入文件是否存在
	if _, err := os.Stat(task.InputPath); os.IsNotExist(err) {
		s.tasksMutex.Lock()
		task.Status = models.ConversionStatusFailed
		task.Error = "输入文件不存在"
		s.tasksMutex.Unlock()
		log.Printf("转换任务失败: %s, 错误: %s", task.ID, task.Error)
		return
	}

	// 创建输出目录
	outputDir := filepath.Dir(task.OutputPath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		s.tasksMutex.Lock()
		task.Status = models.ConversionStatusFailed
		task.Error = fmt.Sprintf("创建输出目录失败: %v", err)
		s.tasksMutex.Unlock()
		log.Printf("转换任务失败: %s, 错误: %s", task.ID, task.Error)
		return
	}

	// 使用纯Go实现HLS到MP4转换
	if err := s.convertHLSToMP4(task); err != nil {
		s.tasksMutex.Lock()
		task.Status = models.ConversionStatusFailed
		task.Error = fmt.Sprintf("转换失败: %v", err)
		s.tasksMutex.Unlock()
		log.Printf("转换任务失败: %s, 错误: %s", task.ID, task.Error)
		return
	}

	// 转换成功
	s.tasksMutex.Lock()
	task.Status = models.ConversionStatusCompleted
	task.Progress = 100.0
	now = time.Now()
	task.CompletedAt = &now
	s.tasksMutex.Unlock()

	// 创建转换完成标志文件
	flagFile := filepath.Join(filepath.Dir(task.InputPath), ".have_transformed")
	if err := os.WriteFile(flagFile, []byte(""), 0644); err != nil {
		log.Printf("创建转换标志文件失败: %v", err)
	}

	log.Printf("转换任务完成: %s, 视频ID: %s", task.ID, task.VideoID)
}

// convertHLSToMP4 将HLS转换为MP4（纯Go实现）
func (s *ConversionService) convertHLSToMP4(task *models.ConversionTask) error {
	// 解析m3u8文件
	segments, err := s.parseM3U8(task.InputPath)
	if err != nil {
		return fmt.Errorf("解析m3u8文件失败: %v", err)
	}

	if len(segments) == 0 {
		return fmt.Errorf("m3u8文件中没有找到视频片段")
	}

	// 创建输出文件
	outputFile, err := os.Create(task.OutputPath)
	if err != nil {
		return fmt.Errorf("创建输出文件失败: %v", err)
	}
	defer outputFile.Close()

	log.Printf("开始合并 %d 个视频片段", len(segments))

	// 逐个合并TS片段
	for i, segment := range segments {
		// 更新进度
		progress := float64(i) / float64(len(segments)) * 100
		s.tasksMutex.Lock()
		task.Progress = progress
		s.tasksMutex.Unlock()

		// 构建片段文件路径
		segmentPath := filepath.Join(filepath.Dir(task.InputPath), segment)

		// 检查片段文件是否存在
		if _, err := os.Stat(segmentPath); os.IsNotExist(err) {
			log.Printf("警告: 片段文件不存在: %s", segmentPath)
			continue
		}

		// 读取并写入片段数据
		if err := s.copySegment(segmentPath, outputFile); err != nil {
			log.Printf("警告: 复制片段失败: %s, 错误: %v", segmentPath, err)
			continue
		}

		// 检查是否被取消
		select {
		case <-s.ctx.Done():
			return fmt.Errorf("转换被取消")
		default:
		}
	}

	log.Printf("视频片段合并完成: %s", task.OutputPath)
	return nil
}

// parseM3U8 解析m3u8文件，提取TS片段列表
func (s *ConversionService) parseM3U8(m3u8Path string) ([]string, error) {
	file, err := os.Open(m3u8Path)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var segments []string
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		// 跳过注释行和空行
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// 检查是否是TS文件
		if strings.HasSuffix(line, ".ts") {
			segments = append(segments, line)
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, err
	}

	return segments, nil
}

// copySegment 复制视频片段到输出文件
func (s *ConversionService) copySegment(segmentPath string, outputFile *os.File) error {
	segmentFile, err := os.Open(segmentPath)
	if err != nil {
		return err
	}
	defer segmentFile.Close()

	// 直接复制文件内容
	_, err = io.Copy(outputFile, segmentFile)
	return err
}

// CreateConversionTask 创建转换任务
func (s *ConversionService) CreateConversionTask(videoID string) (*models.ConversionTask, error) {
	// 获取本地视频信息
	videos, _, err := s.storage.GetLocalVideos(1, 10000)
	if err != nil {
		return nil, fmt.Errorf("获取本地视频失败: %v", err)
	}

	var targetVideo *models.LocalVideoInfo
	for _, video := range videos {
		if video.VideoID == videoID {
			targetVideo = &video
			break
		}
	}

	if targetVideo == nil {
		return nil, fmt.Errorf("视频不存在: %s", videoID)
	}

	// 检查是否已经转换过
	flagFile := filepath.Join(targetVideo.LocalPath, ".have_transformed")
	if _, err := os.Stat(flagFile); err == nil {
		return nil, fmt.Errorf("视频已经转换过: %s", videoID)
	}

	// 构建输入和输出路径
	inputPath := filepath.Join(targetVideo.LocalPath, "playlist.m3u8")
	outputPath := filepath.Join(targetVideo.LocalPath, fmt.Sprintf("%s.mp4", videoID))

	// 创建转换任务
	task := &models.ConversionTask{
		ID:         uuid.New().String(),
		VideoID:    videoID,
		InputPath:  inputPath,
		OutputPath: outputPath,
		Status:     models.ConversionStatusPending,
		Progress:   0,
		CreatedAt:  time.Now(),
	}

	s.tasksMutex.Lock()
	s.tasks[task.ID] = task
	s.tasksMutex.Unlock()

	// 加入队列
	select {
	case s.queue <- task:
	default:
		return nil, fmt.Errorf("转换队列已满")
	}

	return task, nil
}

// GetAllTasks 获取所有转换任务
func (s *ConversionService) GetAllTasks() []*models.ConversionTask {
	s.tasksMutex.RLock()
	defer s.tasksMutex.RUnlock()

	tasks := make([]*models.ConversionTask, 0, len(s.tasks))
	for _, task := range s.tasks {
		tasks = append(tasks, task)
	}

	return tasks
}

// GetTaskStats 获取任务状态统计
func (s *ConversionService) GetTaskStats() (map[string]int, error) {
	s.tasksMutex.RLock()
	defer s.tasksMutex.RUnlock()

	stats := map[string]int{
		"pending":    0,
		"converting": 0,
		"completed":  0,
		"failed":     0,
		"paused":     0,
		"cancelled":  0,
	}

	for _, task := range s.tasks {
		statusStr := string(task.Status)
		if count, exists := stats[statusStr]; exists {
			stats[statusStr] = count + 1
		}
	}

	return stats, nil
}

// GetTasksByStatus 按状态获取任务列表（支持分页）
func (s *ConversionService) GetTasksByStatus(status string, page, pageSize int) ([]*models.ConversionTask, int64, error) {
	s.tasksMutex.RLock()
	defer s.tasksMutex.RUnlock()

	var filteredTasks []*models.ConversionTask

	// 筛选任务
	for _, task := range s.tasks {
		taskStatus := string(task.Status)
		
		// 特殊处理：当筛选"converting"时，包含"converting"状态
		if status == "converting" && taskStatus == "converting" {
			filteredTasks = append(filteredTasks, task)
		} else if status == "" || status == "all" || taskStatus == status {
			filteredTasks = append(filteredTasks, task)
		}
	}

	total := int64(len(filteredTasks))

	// 分页处理
	start := (page - 1) * pageSize
	end := start + pageSize

	if start >= len(filteredTasks) {
		return []*models.ConversionTask{}, total, nil
	}

	if end > len(filteredTasks) {
		end = len(filteredTasks)
	}

	pagedTasks := filteredTasks[start:end]
	return pagedTasks, total, nil
}

// Stop 停止转换服务
func (s *ConversionService) Stop() {
	s.cancel()
}
