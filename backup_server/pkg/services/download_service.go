package services

import (
	"backup_server/pkg/config"
	"backup_server/pkg/models"
	"backup_server/pkg/utils"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

// DownloadService 下载服务
type DownloadService struct {
	config      *config.Config
	minioClient *minio.Client
	tasks       map[string]*models.DownloadTask
	tasksMutex  sync.RWMutex
	queue       chan *models.DownloadTask
	workers     int
	ctx         context.Context
	cancel      context.CancelFunc
}

// NewDownloadService 创建下载服务
func NewDownloadService(cfg *config.Config, minioConfig *models.MinIOConfig) (*DownloadService, error) {
	// 创建MinIO客户端
	minioClient, err := minio.New(minioConfig.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(minioConfig.AccessKey, minioConfig.SecretKey, ""),
		Secure: minioConfig.UseSSL,
	})
	if err != nil {
		return nil, fmt.Errorf("创建MinIO客户端失败: %v", err)
	}

	ctx, cancel := context.WithCancel(context.Background())

	service := &DownloadService{
		config:      cfg,
		minioClient: minioClient,
		tasks:       make(map[string]*models.DownloadTask),
		queue:       make(chan *models.DownloadTask, 100),
		workers:     cfg.Download.MaxConcurrent,
		ctx:         ctx,
		cancel:      cancel,
	}

	// 启动工作协程
	for i := 0; i < service.workers; i++ {
		go service.worker()
	}

	return service, nil
}

// AddDownloadTask 添加下载任务
func (s *DownloadService) AddDownloadTask(videoInfo models.BackupVideoInfo) string {
	taskID := utils.GenerateTaskID()
	
	task := &models.DownloadTask{
		ID:        taskID,
		VideoInfo: videoInfo,
		Status:    models.StatusPending,
		Progress:  0,
		CreatedAt: time.Now(),
		Files:     []models.DownloadFileStatus{},
	}

	s.tasksMutex.Lock()
	s.tasks[taskID] = task
	s.tasksMutex.Unlock()

	// 添加到队列
	select {
	case s.queue <- task:
		log.Printf("任务 %s 已添加到下载队列", taskID)
	default:
		task.Status = models.StatusFailed
		task.Error = "下载队列已满"
		log.Printf("下载队列已满，任务 %s 添加失败", taskID)
	}

	return taskID
}

// GetTask 获取任务状态
func (s *DownloadService) GetTask(taskID string) (*models.DownloadTask, bool) {
	s.tasksMutex.RLock()
	defer s.tasksMutex.RUnlock()
	task, exists := s.tasks[taskID]
	return task, exists
}

// GetAllTasks 获取所有任务
func (s *DownloadService) GetAllTasks() []models.DownloadTask {
	s.tasksMutex.RLock()
	defer s.tasksMutex.RUnlock()
	
	tasks := make([]models.DownloadTask, 0, len(s.tasks))
	for _, task := range s.tasks {
		tasks = append(tasks, *task)
	}
	return tasks
}

// CancelTask 取消任务
func (s *DownloadService) CancelTask(taskID string) error {
	s.tasksMutex.Lock()
	defer s.tasksMutex.Unlock()
	
	task, exists := s.tasks[taskID]
	if !exists {
		return fmt.Errorf("任务不存在")
	}
	
	if task.Status == models.StatusDownloading {
		task.Status = models.StatusCancelled
		return nil
	}
	
	if task.Status == models.StatusPending {
		task.Status = models.StatusCancelled
		return nil
	}
	
	return fmt.Errorf("任务状态不允许取消")
}

// worker 工作协程
func (s *DownloadService) worker() {
	for {
		select {
		case <-s.ctx.Done():
			return
		case task := <-s.queue:
			s.processTask(task)
		}
	}
}

// processTask 处理下载任务
func (s *DownloadService) processTask(task *models.DownloadTask) {
	log.Printf("开始处理下载任务: %s", task.ID)
	
	// 更新任务状态
	task.Status = models.StatusDownloading
	task.Progress = 0
	now := time.Now()
	task.StartedAt = &now

	// 创建本地目录
	localPath := utils.GetVideoLocalPath(s.config.Storage.DataPath, task.VideoInfo.DeviceID, task.VideoInfo.VideoFolder)
	if err := utils.EnsureDir(localPath); err != nil {
		s.failTask(task, fmt.Sprintf("创建本地目录失败: %v", err))
		return
	}

	// 下载文件列表
	files, err := s.getVideoFiles(task.VideoInfo)
	if err != nil {
		s.failTask(task, fmt.Sprintf("获取文件列表失败: %v", err))
		return
	}

	task.Files = make([]models.DownloadFileStatus, len(files))
	for i, file := range files {
		task.Files[i] = models.DownloadFileStatus{
			FileName: file,
			Status:   models.StatusPending,
			Progress: 0,
		}
	}

	// 下载每个文件
	totalFiles := len(files)
	completedFiles := 0

	for i, file := range files {
		if task.Status == models.StatusCancelled {
			break
		}

		task.Files[i].Status = models.StatusDownloading
		
		err := s.downloadFile(task.VideoInfo, file, localPath)
		if err != nil {
			task.Files[i].Status = models.StatusFailed
			task.Files[i].Error = err.Error()
			log.Printf("下载文件失败 %s: %v", file, err)
		} else {
			task.Files[i].Status = models.StatusCompleted
			task.Files[i].Progress = 100
			completedFiles++
		}

		// 更新总进度
		task.Progress = float64(completedFiles) / float64(totalFiles) * 100
	}

	// 检查任务状态
	if task.Status == models.StatusCancelled {
		log.Printf("任务已取消: %s", task.ID)
		return
	}

	// 创建元数据文件
	if err := s.createMetadataFile(task.VideoInfo, localPath); err != nil {
		log.Printf("创建元数据文件失败: %v", err)
	}

	// 创建按猫分类的符号链接
	if err := s.createCatSymlink(task.VideoInfo, localPath); err != nil {
		log.Printf("创建猫分类符号链接失败: %v", err)
	}

	// 完成任务
	if completedFiles == totalFiles {
		task.Status = models.StatusCompleted
		task.Progress = 100
		now := time.Now()
		task.CompletedAt = &now
		log.Printf("任务完成: %s", task.ID)
	} else {
		s.failTask(task, "部分文件下载失败")
	}
}

// failTask 标记任务失败
func (s *DownloadService) failTask(task *models.DownloadTask, errorMsg string) {
	task.Status = models.StatusFailed
	task.Error = errorMsg
	now := time.Now()
	if task.StartedAt == nil {
		task.StartedAt = &now
	}
	task.CompletedAt = &now
	log.Printf("任务失败 %s: %s", task.ID, errorMsg)
}

// getVideoFiles 获取视频文件列表
func (s *DownloadService) getVideoFiles(videoInfo models.BackupVideoInfo) ([]string, error) {
	// 修正VideoFolder时区问题
	correctedVideoFolder := s.correctVideoFolderTimezone(videoInfo)

	// 从MinIO获取文件列表
	devicePath := fmt.Sprintf("device%s", videoInfo.DeviceID)
	prefix := fmt.Sprintf("%s/%s/", devicePath, correctedVideoFolder)

	log.Printf("Searching for video files in MinIO path: %s", prefix)

	objectCh := s.minioClient.ListObjects(s.ctx, "records", minio.ListObjectsOptions{
		Prefix:    prefix,
		Recursive: true,
	})

	var files []string
	for object := range objectCh {
		if object.Err != nil {
			return nil, fmt.Errorf("列出对象失败: %v", object.Err)
		}

		// 提取文件名
		fileName := strings.TrimPrefix(object.Key, prefix)
		if fileName != "" {
			files = append(files, fileName)
		}
	}

	if len(files) == 0 {
		// 如果修正后的路径没有找到文件，尝试原始路径
		log.Printf("No files found in corrected path, trying original path: %s/%s/", devicePath, videoInfo.VideoFolder)

		originalPrefix := fmt.Sprintf("%s/%s/", devicePath, videoInfo.VideoFolder)
		objectCh = s.minioClient.ListObjects(s.ctx, "records", minio.ListObjectsOptions{
			Prefix:    originalPrefix,
			Recursive: true,
		})

		for object := range objectCh {
			if object.Err != nil {
				return nil, fmt.Errorf("列出对象失败: %v", object.Err)
			}

			fileName := strings.TrimPrefix(object.Key, originalPrefix)
			if fileName != "" {
				files = append(files, fileName)
			}
		}
	}

	if len(files) == 0 {
		return nil, fmt.Errorf("未找到视频文件")
	}

	log.Printf("Found %d files in video folder", len(files))
	return files, nil
}

// correctVideoFolderTimezone 修正VideoFolder的时区问题
func (s *DownloadService) correctVideoFolderTimezone(videoInfo models.BackupVideoInfo) string {
	// 如果VideoFolder看起来是未来时间，尝试修正
	// 解析当前的VideoFolder时间
	if len(videoInfo.VideoFolder) < 19 || !strings.HasSuffix(videoInfo.VideoFolder, "_hls") {
		return videoInfo.VideoFolder // 格式不正确，返回原值
	}

	// 提取时间部分：2025-07-31_06-19-06
	timeStr := videoInfo.VideoFolder[:19]

	// 尝试解析时间
	folderTime, err := time.Parse("2006-01-02_15-04-05", timeStr)
	if err != nil {
		log.Printf("Failed to parse video folder time: %s, error: %v", timeStr, err)
		return videoInfo.VideoFolder
	}

	// 检查是否是未来时间（超过当前时间1小时）
	now := time.Now()
	if folderTime.After(now.Add(time.Hour)) {
		log.Printf("Video folder time appears to be in the future: %s, correcting timezone", folderTime.Format(time.RFC3339))

		// 尝试不同的时区修正
		// 常见的时区偏移：UTC+8 (中国), UTC+0 (UTC)
		timezoneOffsets := []time.Duration{
			-8 * time.Hour,  // 如果是UTC+8被当作UTC，减去8小时
			-7 * time.Hour,  // UTC+7
			-6 * time.Hour,  // UTC+6
			8 * time.Hour,   // 如果是UTC被当作UTC+8，加上8小时
		}

		for _, offset := range timezoneOffsets {
			correctedTime := folderTime.Add(offset)

			// 检查修正后的时间是否合理（在过去，但不超过30天前）
			if correctedTime.Before(now) && correctedTime.After(now.Add(-30*24*time.Hour)) {
				correctedFolder := correctedTime.Format("2006-01-02_15-04-05") + "_hls"
				log.Printf("Corrected video folder from %s to %s (offset: %v)", videoInfo.VideoFolder, correctedFolder, offset)
				return correctedFolder
			}
		}

		// 如果所有修正都不合理，尝试使用Unix时间戳直接转换
		// 假设设备在UTC+8时区
		beijingLoc, err := time.LoadLocation("Asia/Shanghai")
		if err == nil {
			timestampTime := time.Unix(videoInfo.StartTime, 0).In(beijingLoc)
			correctedFolder := timestampTime.Format("2006-01-02_15-04-05") + "_hls"
			log.Printf("Using timestamp-based correction: %s (Beijing time)", correctedFolder)
			return correctedFolder
		}
	}

	return videoInfo.VideoFolder
}

// downloadFile 下载单个文件
func (s *DownloadService) downloadFile(videoInfo models.BackupVideoInfo, fileName, localPath string) error {
	// 构建MinIO对象路径
	devicePath := fmt.Sprintf("device%s", videoInfo.DeviceID)
	objectPath := fmt.Sprintf("%s/%s/%s", devicePath, videoInfo.VideoFolder, fileName)
	
	// 获取对象
	object, err := s.minioClient.GetObject(s.ctx, "records", objectPath, minio.GetObjectOptions{})
	if err != nil {
		return fmt.Errorf("获取对象失败: %v", err)
	}
	defer object.Close()

	// 创建本地文件
	localFilePath := filepath.Join(localPath, fileName)
	localFile, err := os.Create(localFilePath)
	if err != nil {
		return fmt.Errorf("创建本地文件失败: %v", err)
	}
	defer localFile.Close()

	// 复制数据
	_, err = io.Copy(localFile, object)
	if err != nil {
		return fmt.Errorf("复制数据失败: %v", err)
	}

	return nil
}

// createMetadataFile 创建元数据文件
func (s *DownloadService) createMetadataFile(videoInfo models.BackupVideoInfo, localPath string) error {
	metadata := map[string]interface{}{
		"video_id":       videoInfo.VideoID,
		"device_id":      videoInfo.DeviceID,
		"animal_id":      videoInfo.AnimalID,
		"start_time":     videoInfo.StartTime,
		"end_time":       videoInfo.EndTime,
		"weight_litter":  videoInfo.WeightLitter,
		"weight_cat":     videoInfo.WeightCat,
		"weight_waste":   videoInfo.WeightWaste,
		"behavior_type":  videoInfo.BehaviorType,
		"cat_confidence": videoInfo.CatConfidence,
		"created_at":     videoInfo.CreatedAt,
		"downloaded_at":  time.Now().Format("2006-01-02 15:04:05"),
	}

	metadataPath := filepath.Join(localPath, "metadata.json")
	file, err := os.Create(metadataPath)
	if err != nil {
		return err
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	return encoder.Encode(metadata)
}

// createCatSymlink 创建按猫分类的符号链接
func (s *DownloadService) createCatSymlink(videoInfo models.BackupVideoInfo, sourcePath string) error {
	if videoInfo.AnimalID == "" {
		return nil // 没有猫ID，跳过
	}

	catPath := utils.GetCatLocalPath(s.config.Storage.DataPath, videoInfo.AnimalID, videoInfo.VideoFolder)
	return utils.CreateSymlink(sourcePath, catPath)
}

// Stop 停止下载服务
func (s *DownloadService) Stop() {
	s.cancel()
}

// GetTaskStats 获取任务状态统计
func (s *DownloadService) GetTaskStats() (map[string]int, error) {
	s.tasksMutex.RLock()
	defer s.tasksMutex.RUnlock()

	stats := map[string]int{
		"pending":     0,
		"running":     0,
		"downloading": 0,
		"completed":   0,
		"failed":      0,
		"paused":      0,
		"cancelled":   0,
	}

	for _, task := range s.tasks {
		statusStr := string(task.Status)
		if count, exists := stats[statusStr]; exists {
			stats[statusStr] = count + 1
		}
	}

	return stats, nil
}

// GetTasksByStatus 按状态获取任务列表（支持分页）
func (s *DownloadService) GetTasksByStatus(status string, page, pageSize int) ([]*models.DownloadTask, int64, error) {
	s.tasksMutex.RLock()
	defer s.tasksMutex.RUnlock()

	var filteredTasks []*models.DownloadTask

	// 筛选任务
	for _, task := range s.tasks {
		taskStatus := string(task.Status)

		// 特殊处理：当筛选"running"时，包含"running"和"downloading"状态
		if status == "running" && (taskStatus == "running" || taskStatus == "downloading") {
			filteredTasks = append(filteredTasks, task)
		} else if status == "" || status == "all" || taskStatus == status {
			filteredTasks = append(filteredTasks, task)
		}
	}

	total := int64(len(filteredTasks))

	// 分页处理
	start := (page - 1) * pageSize
	end := start + pageSize

	if start >= len(filteredTasks) {
		return []*models.DownloadTask{}, total, nil
	}

	if end > len(filteredTasks) {
		end = len(filteredTasks)
	}

	pagedTasks := filteredTasks[start:end]
	return pagedTasks, total, nil
}

// PauseTask 暂停任务
func (s *DownloadService) PauseTask(taskID string) error {
	s.tasksMutex.Lock()
	defer s.tasksMutex.Unlock()

	task, exists := s.tasks[taskID]
	if !exists {
		return fmt.Errorf("任务不存在")
	}

	if task.Status != models.StatusPending && task.Status != models.StatusRunning && task.Status != models.StatusDownloading {
		return fmt.Errorf("只能暂停等待中或进行中的任务")
	}

	task.Status = models.StatusPaused
	return nil
}

// ResumeTask 恢复任务
func (s *DownloadService) ResumeTask(taskID string) error {
	s.tasksMutex.Lock()
	defer s.tasksMutex.Unlock()

	task, exists := s.tasks[taskID]
	if !exists {
		return fmt.Errorf("任务不存在")
	}

	if task.Status != models.StatusPaused {
		return fmt.Errorf("只能恢复已暂停的任务")
	}

	task.Status = models.StatusPending
	// 重新加入队列
	select {
	case s.queue <- task:
	default:
		// 队列满了，稍后会被处理
	}
	return nil
}

// RetryTask 重试任务
func (s *DownloadService) RetryTask(taskID string) error {
	s.tasksMutex.Lock()
	defer s.tasksMutex.Unlock()

	task, exists := s.tasks[taskID]
	if !exists {
		return fmt.Errorf("任务不存在")
	}

	if task.Status != models.StatusFailed {
		return fmt.Errorf("只能重试失败的任务")
	}

	// 重置任务状态
	task.Status = models.StatusPending
	task.Progress = 0
	task.Error = ""

	// 重新加入队列
	select {
	case s.queue <- task:
	default:
		// 队列满了，稍后会被处理
	}
	return nil
}

// ClearCompletedTasks 清理已完成的任务
func (s *DownloadService) ClearCompletedTasks() (int, error) {
	s.tasksMutex.Lock()
	defer s.tasksMutex.Unlock()

	deletedCount := 0
	for taskID, task := range s.tasks {
		if task.Status == models.StatusCompleted {
			delete(s.tasks, taskID)
			deletedCount++
		}
	}

	return deletedCount, nil
}

// RetryFailedTasks 重试所有失败的任务
func (s *DownloadService) RetryFailedTasks() (int, error) {
	s.tasksMutex.Lock()
	defer s.tasksMutex.Unlock()

	retriedCount := 0
	for _, task := range s.tasks {
		if task.Status == models.StatusFailed {
			task.Status = models.StatusPending
			task.Progress = 0
			task.Error = ""

			// 重新加入队列
			select {
			case s.queue <- task:
				retriedCount++
			default:
				// 队列满了，稍后会被处理
				retriedCount++
			}
		}
	}

	return retriedCount, nil
}

// PauseAllTasks 暂停所有正在进行的任务
func (s *DownloadService) PauseAllTasks() (int, error) {
	s.tasksMutex.Lock()
	defer s.tasksMutex.Unlock()

	pausedCount := 0
	for _, task := range s.tasks {
		if task.Status == models.StatusPending || task.Status == models.StatusRunning || task.Status == models.StatusDownloading {
			task.Status = models.StatusPaused
			pausedCount++
		}
	}

	return pausedCount, nil
}

// ResumeAllTasks 恢复所有暂停的任务
func (s *DownloadService) ResumeAllTasks() (int, error) {
	s.tasksMutex.Lock()
	defer s.tasksMutex.Unlock()

	resumedCount := 0
	for _, task := range s.tasks {
		if task.Status == models.StatusPaused {
			task.Status = models.StatusPending

			// 重新加入队列
			select {
			case s.queue <- task:
				resumedCount++
			default:
				// 队列满了，稍后会被处理
				resumedCount++
			}
		}
	}

	return resumedCount, nil
}
