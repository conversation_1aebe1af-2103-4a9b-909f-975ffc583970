package services

import (
	"backup_server/pkg/config"
	"backup_server/pkg/models"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"time"
)

// BackendClient 后端API客户端
type BackendClient struct {
	config     *config.Config
	httpClient *http.Client
}

// NewBackendClient 创建后端API客户端
func NewBackendClient(cfg *config.Config) *BackendClient {
	// 创建HTTP客户端，支持代理
	transport := &http.Transport{}
	
	// 设置代理
	if cfg.Proxy.HTTPProxy != "" {
		proxyURL, err := url.Parse(cfg.Proxy.HTTPProxy)
		if err == nil {
			transport.Proxy = http.ProxyURL(proxyURL)
		}
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   30 * time.Second,
	}

	return &BackendClient{
		config:     cfg,
		httpClient: client,
	}
}

// GetVideoList 获取视频列表
func (c *BackendClient) GetVideoList(req *models.VideoListRequest) (*models.BackupListResponse, error) {
	// 构建URL
	baseURL := fmt.Sprintf("%s/api/backup/videos", c.config.Backend.URL)
	u, err := url.Parse(baseURL)
	if err != nil {
		return nil, fmt.Errorf("解析URL失败: %v", err)
	}

	// 添加查询参数
	params := url.Values{}
	if req.StartTime != nil {
		params.Add("start_time", strconv.FormatInt(*req.StartTime, 10))
	}
	if req.EndTime != nil {
		params.Add("end_time", strconv.FormatInt(*req.EndTime, 10))
	}
	if req.DeviceID != nil {
		params.Add("device_id", *req.DeviceID)
	}
	if req.AnimalID != nil {
		params.Add("animal_id", *req.AnimalID)
	}
	if req.Page > 0 {
		params.Add("page", strconv.Itoa(req.Page))
	}
	if req.PageSize > 0 {
		params.Add("page_size", strconv.Itoa(req.PageSize))
	}
	u.RawQuery = params.Encode()

	// 创建请求
	httpReq, err := http.NewRequest("GET", u.String(), nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 添加认证头
	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", c.config.Backend.AuthToken))
	httpReq.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var response models.BackupListResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &response, nil
}

// GetMinIOConfig 获取MinIO配置
func (c *BackendClient) GetMinIOConfig() (*models.MinIOConfig, error) {
	// 构建URL
	url := fmt.Sprintf("%s/api/backup/minio-config", c.config.Backend.URL)

	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 添加认证头
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", c.config.Backend.AuthToken))
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var config models.MinIOConfig
	if err := json.NewDecoder(resp.Body).Decode(&config); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &config, nil
}

// GetCatList 获取猫咪列表
func (c *BackendClient) GetCatList() (*models.CatListResponse, error) {
	// 构建URL - 使用backup API路径
	url := fmt.Sprintf("%s/api/backup/cats", c.config.Backend.URL)

	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 添加认证头
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", c.config.Backend.AuthToken))
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var response models.CatListResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &response, nil
}

// GetCatVideoList 获取猫咪的视频列表
func (c *BackendClient) GetCatVideoList(req *models.CatVideoListRequest) (*models.CatVideoListResponse, error) {
	// 构建URL
	baseURL := fmt.Sprintf("%s/api/backup/cats/%s/videos", c.config.Backend.URL, req.CatID)
	u, err := url.Parse(baseURL)
	if err != nil {
		return nil, fmt.Errorf("解析URL失败: %v", err)
	}

	// 添加查询参数
	params := url.Values{}
	if req.StartTime != nil {
		params.Add("start_time", strconv.FormatInt(*req.StartTime, 10))
	}
	if req.EndTime != nil {
		params.Add("end_time", strconv.FormatInt(*req.EndTime, 10))
	}
	if req.Page > 0 {
		params.Add("page", strconv.Itoa(req.Page))
	}
	if req.PageSize > 0 {
		params.Add("page_size", strconv.Itoa(req.PageSize))
	}
	u.RawQuery = params.Encode()

	// 创建请求
	httpReq, err := http.NewRequest("GET", u.String(), nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 添加认证头
	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", c.config.Backend.AuthToken))
	httpReq.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var response models.CatVideoListResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &response, nil
}

// TestConnection 测试连接
func (c *BackendClient) TestConnection() error {
	url := fmt.Sprintf("%s/api/health", c.config.Backend.URL)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("连接失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("健康检查失败，状态码: %d", resp.StatusCode)
	}

	return nil
}
