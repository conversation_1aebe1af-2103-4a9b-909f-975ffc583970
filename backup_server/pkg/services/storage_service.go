package services

import (
	"backup_server/pkg/config"
	"backup_server/pkg/models"
	"backup_server/pkg/utils"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// StorageService 存储服务
type StorageService struct {
	config *config.Config
}

// NewStorageService 创建存储服务
func NewStorageService(cfg *config.Config) *StorageService {
	return &StorageService{
		config: cfg,
	}
}

// GetLocalVideos 获取本地视频列表
func (s *StorageService) GetLocalVideos(page, pageSize int) ([]models.LocalVideoInfo, int, error) {
	var videos []models.LocalVideoInfo
	
	deviceDir := filepath.Join(s.config.Storage.DataPath, "by_device")
	if !utils.FileExists(deviceDir) {
		return videos, 0, nil
	}

	// 遍历设备目录
	err := filepath.Walk(deviceDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 查找metadata.json文件
		if info.Name() == "metadata.json" {
			video, err := s.parseVideoMetadata(path)
			if err != nil {
				return nil // 跳过错误的元数据文件
			}
			videos = append(videos, *video)
		}
		return nil
	})

	if err != nil {
		return nil, 0, fmt.Errorf("遍历本地视频失败: %v", err)
	}

	// 分页
	total := len(videos)
	start := (page - 1) * pageSize
	end := start + pageSize

	if start >= total {
		return []models.LocalVideoInfo{}, total, nil
	}
	if end > total {
		end = total
	}

	return videos[start:end], total, nil
}

// parseVideoMetadata 解析视频元数据
func (s *StorageService) parseVideoMetadata(metadataPath string) (*models.LocalVideoInfo, error) {
	file, err := os.Open(metadataPath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var metadata map[string]interface{}
	if err := json.NewDecoder(file).Decode(&metadata); err != nil {
		return nil, err
	}

	// 获取视频目录路径
	videoDir := filepath.Dir(metadataPath)
	
	// 计算目录大小和文件数量
	dirSize, _ := utils.GetDirSize(videoDir)
	fileCount, _ := utils.CountFiles(videoDir)

	// 解析下载时间
	downloadedAt := time.Now()
	if downloadedAtStr, ok := metadata["downloaded_at"].(string); ok {
		if t, err := time.Parse("2006-01-02 15:04:05", downloadedAtStr); err == nil {
			downloadedAt = t
		}
	}

	video := &models.LocalVideoInfo{
		VideoID:      getString(metadata, "video_id"),
		DeviceID:     getString(metadata, "device_id"),
		AnimalID:     getString(metadata, "animal_id"),
		StartTime:    getInt64(metadata, "start_time"),
		LocalPath:    videoDir,
		DownloadedAt: downloadedAt,
		FileSize:     dirSize,
		FileCount:    fileCount,
	}

	return video, nil
}

// GetStorageStats 获取存储统计
func (s *StorageService) GetStorageStats() (*models.StorageStats, error) {
	stats := &models.StorageStats{
		LastUpdated: time.Now(),
	}

	dataPath := s.config.Storage.DataPath
	if !utils.FileExists(dataPath) {
		return stats, nil
	}

	// 统计设备数量
	deviceDir := filepath.Join(dataPath, "by_device")
	if utils.FileExists(deviceDir) {
		devices, err := os.ReadDir(deviceDir)
		if err == nil {
			stats.DeviceCount = len(devices)
		}
	}

	// 统计猫数量
	catDir := filepath.Join(dataPath, "by_cat")
	if utils.FileExists(catDir) {
		cats, err := os.ReadDir(catDir)
		if err == nil {
			stats.CatCount = len(cats)
		}
	}

	// 统计视频和文件
	err := filepath.Walk(dataPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.Name() == "metadata.json" {
			stats.TotalVideos++
		} else if !info.IsDir() && !strings.HasSuffix(info.Name(), ".json") {
			stats.TotalFiles++
			stats.TotalSize += info.Size()
		}
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("统计存储信息失败: %v", err)
	}

	return stats, nil
}

// DeleteVideo 删除本地视频
func (s *StorageService) DeleteVideo(videoID string) error {
	// 查找视频路径
	videoPath, err := s.findVideoPath(videoID)
	if err != nil {
		return err
	}

	// 删除设备目录下的视频
	if err := os.RemoveAll(videoPath); err != nil {
		return fmt.Errorf("删除视频文件失败: %v", err)
	}

	// 删除猫分类下的符号链接
	if err := s.deleteCatSymlink(videoID); err != nil {
		// 符号链接删除失败不影响主要操作
		fmt.Printf("删除猫分类符号链接失败: %v\n", err)
	}

	return nil
}

// findVideoPath 查找视频路径
func (s *StorageService) findVideoPath(videoID string) (string, error) {
	deviceDir := filepath.Join(s.config.Storage.DataPath, "by_device")
	
	var foundPath string
	err := filepath.Walk(deviceDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.Name() == "metadata.json" {
			metadata, err := s.parseVideoMetadata(path)
			if err != nil {
				return nil
			}
			if metadata.VideoID == videoID {
				foundPath = filepath.Dir(path)
				return filepath.SkipDir
			}
		}
		return nil
	})

	if err != nil {
		return "", err
	}
	if foundPath == "" {
		return "", fmt.Errorf("视频不存在")
	}

	return foundPath, nil
}

// deleteCatSymlink 删除猫分类符号链接
func (s *StorageService) deleteCatSymlink(videoID string) error {
	catDir := filepath.Join(s.config.Storage.DataPath, "by_cat")
	if !utils.FileExists(catDir) {
		return nil
	}

	return filepath.Walk(catDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 检查是否是符号链接
		if info.Mode()&os.ModeSymlink != 0 {
			// 检查符号链接指向的目录是否包含对应的videoID
			target, err := os.Readlink(path)
			if err != nil {
				return nil
			}

			metadataPath := filepath.Join(target, "metadata.json")
			if utils.FileExists(metadataPath) {
				metadata, err := s.parseVideoMetadata(metadataPath)
				if err != nil {
					return nil
				}
				if metadata.VideoID == videoID {
					return os.Remove(path)
				}
			}
		}
		return nil
	})
}

// CleanupEmptyDirs 清理空目录
func (s *StorageService) CleanupEmptyDirs() error {
	dataPath := s.config.Storage.DataPath
	
	return filepath.Walk(dataPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() && path != dataPath {
			// 检查目录是否为空
			entries, err := os.ReadDir(path)
			if err != nil {
				return nil
			}
			if len(entries) == 0 {
				os.Remove(path)
			}
		}
		return nil
	})
}

// 辅助函数
func getString(m map[string]interface{}, key string) string {
	if v, ok := m[key].(string); ok {
		return v
	}
	return ""
}

func getInt64(m map[string]interface{}, key string) int64 {
	if v, ok := m[key].(float64); ok {
		return int64(v)
	}
	return 0
}

// GetLocalVideosByCat 获取指定猫咪的本地视频列表
func (s *StorageService) GetLocalVideosByCat(catID string, page, pageSize int) ([]models.LocalVideoInfo, int64, error) {
	return s.GetLocalVideosByCatWithDateFilter(catID, page, pageSize, nil, nil)
}

// GetLocalVideosByCatWithDateFilter 获取指定猫咪的本地视频列表（支持日期筛选）
func (s *StorageService) GetLocalVideosByCatWithDateFilter(catID string, page, pageSize int, startTime, endTime *int64) ([]models.LocalVideoInfo, int64, error) {
	var allVideos []models.LocalVideoInfo

	// 首先获取所有本地视频
	videos, _, err := s.GetLocalVideos(1, 10000) // 获取所有视频
	if err != nil {
		return nil, 0, err
	}

	// 筛选出指定猫咪的视频
	for _, video := range videos {
		if video.AnimalID == catID {
			// 应用日期筛选
			if startTime != nil && video.StartTime < *startTime {
				continue
			}
			if endTime != nil && video.StartTime > *endTime {
				continue
			}
			allVideos = append(allVideos, video)
		}
	}

	total := int64(len(allVideos))

	// 分页处理
	start := (page - 1) * pageSize
	end := start + pageSize

	if start >= len(allVideos) {
		return []models.LocalVideoInfo{}, total, nil
	}

	if end > len(allVideos) {
		end = len(allVideos)
	}

	pagedVideos := allVideos[start:end]
	return pagedVideos, total, nil
}

// GetAllLocalVideoIds 获取所有本地视频ID列表
func (s *StorageService) GetAllLocalVideoIds() ([]string, error) {
	var videoIds []string

	// 获取所有本地视频
	videos, _, err := s.GetLocalVideos(1, 10000) // 获取所有视频
	if err != nil {
		return nil, err
	}

	// 提取视频ID
	for _, video := range videos {
		videoIds = append(videoIds, video.VideoID)
	}

	return videoIds, nil
}
