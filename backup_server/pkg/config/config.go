package config

import (
	"fmt"
	"os"

	"gopkg.in/yaml.v3"
)

// Config 应用配置
type Config struct {
	Server struct {
		Port string `yaml:"port"`
		Host string `yaml:"host"`
	} `yaml:"server"`

	Backend struct {
		URL       string `yaml:"url"`
		AuthToken string `yaml:"auth_token"`
	} `yaml:"backend"`

	MinIO struct {
		Endpoint  string `yaml:"endpoint"`
		AccessKey string `yaml:"access_key"`
		SecretKey string `yaml:"secret_key"`
		UseSSL    bool   `yaml:"use_ssl"`
		Bucket    string `yaml:"bucket"`
	} `yaml:"minio"`

	Storage struct {
		DataPath string `yaml:"data_path"`
	} `yaml:"storage"`

	Download struct {
		MaxConcurrent int `yaml:"max_concurrent"`
		ChunkSize     int `yaml:"chunk_size"`
	} `yaml:"download"`

	Proxy struct {
		HTTPProxy  string `yaml:"http_proxy"`
		HTTPSProxy string `yaml:"https_proxy"`
		AllProxy   string `yaml:"all_proxy"`
	} `yaml:"proxy"`
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	if configPath == "" {
		configPath = "config.yaml"
	}

	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 设置默认值
	if config.Server.Port == "" {
		config.Server.Port = "8080"
	}
	if config.Server.Host == "" {
		config.Server.Host = "0.0.0.0"
	}
	if config.Storage.DataPath == "" {
		config.Storage.DataPath = "./data"
	}
	if config.Download.MaxConcurrent == 0 {
		config.Download.MaxConcurrent = 5
	}
	if config.Download.ChunkSize == 0 {
		config.Download.ChunkSize = 1024 * 1024 // 1MB
	}

	return &config, nil
}
