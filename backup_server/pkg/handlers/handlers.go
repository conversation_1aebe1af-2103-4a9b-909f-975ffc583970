package handlers

import (
	"backup_server/pkg/models"
	"backup_server/pkg/services"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// Handler HTTP处理器
type Handler struct {
	backendClient   *services.BackendClient
	downloadService *services.DownloadService
	storageService  *services.StorageService
}

// NewHandler 创建处理器
func NewHandler(backendClient *services.BackendClient, downloadService *services.DownloadService, storageService *services.StorageService) *Handler {
	return &Handler{
		backendClient:   backendClient,
		downloadService: downloadService,
		storageService:  storageService,
	}
}

// GetVideoList 获取云端视频列表
func (h *Handler) GetVideoList(c *gin.Context) {
	var req models.VideoListRequest
	if err := c.Should<PERSON>ind<PERSON>uery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	response, err := h.backendClient.GetVideoList(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// DownloadVideos 下载视频
func (h *Handler) DownloadVideos(c *gin.Context) {
	var req models.DownloadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if len(req.VideoIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "视频ID列表不能为空"})
		return
	}

	// 立即返回响应，避免前端等待
	c.JSON(http.StatusOK, gin.H{
		"message":     "正在创建下载任务...",
		"video_count": len(req.VideoIDs),
		"status":      "processing",
	})

	// 在后台异步处理视频信息获取和任务创建
	go h.processDownloadTasks(req.VideoIDs)
}

// processDownloadTasks 异步处理下载任务创建
func (h *Handler) processDownloadTasks(videoIDs []string) {
	for _, videoID := range videoIDs {
		// 获取视频详细信息
		videoInfo, err := h.getVideoInfo(videoID)
		if err != nil {
			// 记录错误日志，但不中断其他视频的处理
			fmt.Printf("获取视频信息失败 %s: %v\n", videoID, err)
			continue
		}

		// 添加下载任务
		h.downloadService.AddDownloadTask(*videoInfo)
	}
}

// getVideoInfo 获取单个视频的详细信息
func (h *Handler) getVideoInfo(videoID string) (*models.BackupVideoInfo, error) {
	// 尝试从多个页面中查找视频信息
	for page := 1; page <= 10; page++ { // 最多查找10页
		videoListReq := &models.VideoListRequest{
			Page:     page,
			PageSize: 100,
		}

		response, err := h.backendClient.GetVideoList(videoListReq)
		if err != nil {
			return nil, err
		}

		// 查找匹配的视频
		for _, video := range response.Videos {
			if video.VideoID == videoID {
				return &video, nil
			}
		}

		// 如果当前页没有更多数据，停止查找
		if len(response.Videos) < videoListReq.PageSize {
			break
		}
	}

	return nil, fmt.Errorf("视频不存在: %s", videoID)
}

// GetTaskStatus 获取任务状态
func (h *Handler) GetTaskStatus(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "任务ID不能为空"})
		return
	}

	task, exists := h.downloadService.GetTask(taskID)
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "任务不存在"})
		return
	}

	c.JSON(http.StatusOK, task)
}

// GetAllTasks 获取所有任务
func (h *Handler) GetAllTasks(c *gin.Context) {
	tasks := h.downloadService.GetAllTasks()
	c.JSON(http.StatusOK, models.TaskStatusResponse{
		Tasks: tasks,
		Total: len(tasks),
	})
}

// CancelTask 取消任务
func (h *Handler) CancelTask(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "任务ID不能为空"})
		return
	}

	err := h.downloadService.CancelTask(taskID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "任务已取消"})
}

// GetLocalVideos 获取本地视频列表
func (h *Handler) GetLocalVideos(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	videos, total, err := h.storageService.GetLocalVideos(page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"videos":      videos,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": (total + pageSize - 1) / pageSize,
	})
}

// GetStorageStats 获取存储统计
func (h *Handler) GetStorageStats(c *gin.Context) {
	stats, err := h.storageService.GetStorageStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// DeleteVideo 删除本地视频
func (h *Handler) DeleteVideo(c *gin.Context) {
	videoID := c.Param("id")
	if videoID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "视频ID不能为空"})
		return
	}

	err := h.storageService.DeleteVideo(videoID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "视频已删除"})
}

// CleanupStorage 清理存储
func (h *Handler) CleanupStorage(c *gin.Context) {
	err := h.storageService.CleanupEmptyDirs()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "存储清理完成"})
}

// TestConnection 测试后端连接
func (h *Handler) TestConnection(c *gin.Context) {
	err := h.backendClient.TestConnection()
	if err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"status": "error",
			"error":  err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status": "ok",
		"message": "后端连接正常",
	})
}

// GetCatList 获取猫咪列表
func (h *Handler) GetCatList(c *gin.Context) {
	response, err := h.backendClient.GetCatList()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetCatVideoList 获取猫咪的视频列表
func (h *Handler) GetCatVideoList(c *gin.Context) {
	var req models.CatVideoListRequest
	req.CatID = c.Param("cat_id")

	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	response, err := h.backendClient.GetCatVideoList(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetLocalCatVideoList 获取本地猫咪的视频列表
func (h *Handler) GetLocalCatVideoList(c *gin.Context) {
	catID := c.Param("cat_id")
	if catID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "cat_id is required"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	// 获取日期筛选参数
	var startTime, endTime *int64
	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if st, err := strconv.ParseInt(startTimeStr, 10, 64); err == nil {
			startTime = &st
		}
	}
	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if et, err := strconv.ParseInt(endTimeStr, 10, 64); err == nil {
			endTime = &et
		}
	}

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	videos, total, err := h.storageService.GetLocalVideosByCatWithDateFilter(catID, page, pageSize, startTime, endTime)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	if pageSize <= 0 {
		totalPages = 1
	}

	c.JSON(http.StatusOK, gin.H{
		"videos":      videos,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": totalPages,
	})
}

// Health 健康检查
func (h *Handler) Health(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status": "ok",
		"service": "backup_server",
	})
}
