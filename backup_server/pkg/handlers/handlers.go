package handlers

import (
	"backup_server/pkg/models"
	"backup_server/pkg/services"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// Handler HTTP处理器
type Handler struct {
	backendClient     *services.BackendClient
	downloadService   *services.DownloadService
	storageService    *services.StorageService
	conversionService *services.ConversionService
}

// NewHandler 创建处理器
func NewHandler(backendClient *services.BackendClient, downloadService *services.DownloadService, storageService *services.StorageService, conversionService *services.ConversionService) *Handler {
	return &Handler{
		backendClient:     backendClient,
		downloadService:   downloadService,
		storageService:    storageService,
		conversionService: conversionService,
	}
}

// GetVideoList 获取云端视频列表
func (h *Handler) GetVideoList(c *gin.Context) {
	var req models.VideoListRequest
	if err := c.Should<PERSON>ind<PERSON>(&req); err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	response, err := h.backendClient.GetVideoList(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// DownloadVideos 下载视频
func (h *Handler) DownloadVideos(c *gin.Context) {
	var req models.DownloadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if len(req.VideoIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "视频ID列表不能为空"})
		return
	}

	// 立即返回响应，避免前端等待
	c.JSON(http.StatusOK, gin.H{
		"message":     "正在创建下载任务...",
		"video_count": len(req.VideoIDs),
		"status":      "processing",
	})

	// 在后台异步处理视频信息获取和任务创建
	go h.processDownloadTasks(req.VideoIDs)
}

// processDownloadTasks 异步处理下载任务创建
func (h *Handler) processDownloadTasks(videoIDs []string) {
	for _, videoID := range videoIDs {
		// 获取视频详细信息
		videoInfo, err := h.getVideoInfo(videoID)
		if err != nil {
			// 记录错误日志，但不中断其他视频的处理
			fmt.Printf("获取视频信息失败 %s: %v\n", videoID, err)
			continue
		}

		// 添加下载任务
		h.downloadService.AddDownloadTask(*videoInfo)
	}
}

// getVideoInfo 获取单个视频的详细信息
func (h *Handler) getVideoInfo(videoID string) (*models.BackupVideoInfo, error) {
	// 尝试从多个页面中查找视频信息
	for page := 1; page <= 10; page++ { // 最多查找10页
		videoListReq := &models.VideoListRequest{
			Page:     page,
			PageSize: 100,
		}

		response, err := h.backendClient.GetVideoList(videoListReq)
		if err != nil {
			return nil, err
		}

		// 查找匹配的视频
		for _, video := range response.Videos {
			if video.VideoID == videoID {
				return &video, nil
			}
		}

		// 如果当前页没有更多数据，停止查找
		if len(response.Videos) < videoListReq.PageSize {
			break
		}
	}

	return nil, fmt.Errorf("视频不存在: %s", videoID)
}

// GetTaskStatus 获取任务状态
func (h *Handler) GetTaskStatus(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "任务ID不能为空"})
		return
	}

	task, exists := h.downloadService.GetTask(taskID)
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "任务不存在"})
		return
	}

	c.JSON(http.StatusOK, task)
}

// GetAllTasks 获取所有任务（支持状态筛选和分页）
func (h *Handler) GetAllTasks(c *gin.Context) {
	status := c.Query("status")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	tasks, total, err := h.downloadService.GetTasksByStatus(status, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	if pageSize <= 0 {
		totalPages = 1
	}

	c.JSON(http.StatusOK, gin.H{
		"tasks":       tasks,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": totalPages,
	})
}

// CancelTask 取消任务
func (h *Handler) CancelTask(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "任务ID不能为空"})
		return
	}

	err := h.downloadService.CancelTask(taskID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "任务已取消"})
}

// GetLocalVideos 获取本地视频列表
func (h *Handler) GetLocalVideos(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	videos, total, err := h.storageService.GetLocalVideos(page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"videos":      videos,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": (total + pageSize - 1) / pageSize,
	})
}

// GetStorageStats 获取存储统计
func (h *Handler) GetStorageStats(c *gin.Context) {
	stats, err := h.storageService.GetStorageStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// DeleteVideo 删除本地视频
func (h *Handler) DeleteVideo(c *gin.Context) {
	videoID := c.Param("id")
	if videoID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "视频ID不能为空"})
		return
	}

	err := h.storageService.DeleteVideo(videoID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "视频已删除"})
}

// CleanupStorage 清理存储
func (h *Handler) CleanupStorage(c *gin.Context) {
	err := h.storageService.CleanupEmptyDirs()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "存储清理完成"})
}

// TestConnection 测试后端连接
func (h *Handler) TestConnection(c *gin.Context) {
	err := h.backendClient.TestConnection()
	if err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"status": "error",
			"error":  err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status": "ok",
		"message": "后端连接正常",
	})
}

// GetCatList 获取猫咪列表
func (h *Handler) GetCatList(c *gin.Context) {
	response, err := h.backendClient.GetCatList()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetCatVideoList 获取猫咪的视频列表
func (h *Handler) GetCatVideoList(c *gin.Context) {
	var req models.CatVideoListRequest
	req.CatID = c.Param("cat_id")

	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	response, err := h.backendClient.GetCatVideoList(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetLocalVideoIds 获取所有本地视频ID列表
func (h *Handler) GetLocalVideoIds(c *gin.Context) {
	videoIds, err := h.storageService.GetAllLocalVideoIds()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"video_ids": videoIds,
		"total":     len(videoIds),
	})
}

// GetTaskStats 获取任务状态统计
func (h *Handler) GetTaskStats(c *gin.Context) {
	stats, err := h.downloadService.GetTaskStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status_counts": stats,
	})
}

// PauseTask 暂停任务
func (h *Handler) PauseTask(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "任务ID不能为空"})
		return
	}

	err := h.downloadService.PauseTask(taskID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "任务已暂停"})
}

// ResumeTask 恢复任务
func (h *Handler) ResumeTask(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "任务ID不能为空"})
		return
	}

	err := h.downloadService.ResumeTask(taskID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "任务已恢复"})
}

// RetryTask 重试任务
func (h *Handler) RetryTask(c *gin.Context) {
	taskID := c.Param("id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "任务ID不能为空"})
		return
	}

	err := h.downloadService.RetryTask(taskID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "任务已重试"})
}

// ClearCompletedTasks 清理已完成的任务
func (h *Handler) ClearCompletedTasks(c *gin.Context) {
	deletedCount, err := h.downloadService.ClearCompletedTasks()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "已完成任务清理成功",
		"deleted_count": deletedCount,
	})
}

// RetryFailedTasks 重试失败的任务
func (h *Handler) RetryFailedTasks(c *gin.Context) {
	retriedCount, err := h.downloadService.RetryFailedTasks()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "失败任务重试成功",
		"retried_count": retriedCount,
	})
}

// PauseAllTasks 暂停所有任务
func (h *Handler) PauseAllTasks(c *gin.Context) {
	pausedCount, err := h.downloadService.PauseAllTasks()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":      "任务暂停成功",
		"paused_count": pausedCount,
	})
}

// ResumeAllTasks 恢复所有任务
func (h *Handler) ResumeAllTasks(c *gin.Context) {
	resumedCount, err := h.downloadService.ResumeAllTasks()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "任务恢复成功",
		"resumed_count": resumedCount,
	})
}

// ==================== 转换任务相关API ====================

// ConvertVideos 转换视频
func (h *Handler) ConvertVideos(c *gin.Context) {
	var req models.ConvertVideoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if len(req.VideoIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "视频ID列表不能为空"})
		return
	}

	taskCount := 0
	var errors []string

	for _, videoID := range req.VideoIDs {
		_, err := h.conversionService.CreateConversionTask(videoID)
		if err != nil {
			errors = append(errors, fmt.Sprintf("视频 %s: %v", videoID, err))
		} else {
			taskCount++
		}
	}

	response := gin.H{
		"task_count": taskCount,
		"message":    fmt.Sprintf("成功创建 %d 个转换任务", taskCount),
	}

	if len(errors) > 0 {
		response["errors"] = errors
	}

	c.JSON(http.StatusOK, response)
}

// ConvertAllVideos 转换所有未转换的视频
func (h *Handler) ConvertAllVideos(c *gin.Context) {
	var req models.ConvertAllVideoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if req.CatID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "猫咪ID不能为空"})
		return
	}

	// 获取该猫咪的所有本地视频
	videos, _, err := h.storageService.GetLocalVideosByCat(req.CatID, 1, 10000)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	taskCount := 0
	var errors []string

	for _, video := range videos {
		if !video.IsConverted {
			_, err := h.conversionService.CreateConversionTask(video.VideoID)
			if err != nil {
				errors = append(errors, fmt.Sprintf("视频 %s: %v", video.VideoID, err))
			} else {
				taskCount++
			}
		}
	}

	response := gin.H{
		"task_count": taskCount,
		"message":    fmt.Sprintf("成功创建 %d 个转换任务", taskCount),
	}

	if len(errors) > 0 {
		response["errors"] = errors
	}

	c.JSON(http.StatusOK, response)
}

// GetConversionStats 获取转换任务状态统计
func (h *Handler) GetConversionStats(c *gin.Context) {
	stats, err := h.conversionService.GetTaskStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status_counts": stats,
	})
}

// GetAllConversions 获取所有转换任务（支持状态筛选和分页）
func (h *Handler) GetAllConversions(c *gin.Context) {
	status := c.Query("status")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	tasks, total, err := h.conversionService.GetTasksByStatus(status, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	if pageSize <= 0 {
		totalPages = 1
	}

	c.JSON(http.StatusOK, gin.H{
		"tasks":       tasks,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": totalPages,
	})
}

// GetLocalCatVideoList 获取本地猫咪的视频列表
func (h *Handler) GetLocalCatVideoList(c *gin.Context) {
	catID := c.Param("cat_id")
	if catID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "cat_id is required"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	// 获取日期筛选参数
	var startTime, endTime *int64
	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if st, err := strconv.ParseInt(startTimeStr, 10, 64); err == nil {
			startTime = &st
		}
	}
	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if et, err := strconv.ParseInt(endTimeStr, 10, 64); err == nil {
			endTime = &et
		}
	}

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	videos, total, err := h.storageService.GetLocalVideosByCatWithDateFilter(catID, page, pageSize, startTime, endTime)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	if pageSize <= 0 {
		totalPages = 1
	}

	c.JSON(http.StatusOK, gin.H{
		"videos":      videos,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": totalPages,
	})
}

// Health 健康检查
func (h *Handler) Health(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status": "ok",
		"service": "backup_server",
	})
}
