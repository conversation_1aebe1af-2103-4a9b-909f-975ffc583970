# 视频备份系统部署指南

## 系统要求

- Go 1.21 或更高版本
- 网络连接到backend_server和MinIO
- 足够的磁盘空间存储视频文件

## 快速开始

### 1. 获取代码

```bash
# 代码已在 backup_server 目录中
cd backup_server
```

### 2. 配置环境

编辑 `config.yaml` 文件，修改以下配置：

```yaml
backend:
  url: "http://your-backend-server-address"  # 替换为实际的backend_server地址
```

### 3. 设置代理（如果需要）

```bash
export https_proxy=http://127.0.0.1:7897
export http_proxy=http://127.0.0.1:7897
export all_proxy=socks5://127.0.0.1:7897
```

### 4. 编译和运行

```bash
# 安装依赖
go mod tidy

# 编译
go build -o backup_server

# 运行
./backup_server
```

### 5. 访问Web界面

打开浏览器访问：`http://localhost:8080`

## 详细配置说明

### config.yaml 配置项

```yaml
server:
  host: "0.0.0.0"        # 服务监听地址
  port: "8080"           # 服务端口

backend:
  url: "http://backend-server"  # backend_server地址
  auth_token: "FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"

storage:
  data_path: "./data"    # 本地数据存储路径

download:
  max_concurrent: 5      # 最大并发下载数
  chunk_size: 1048576    # 下载块大小(字节)

proxy:
  http_proxy: "http://127.0.0.1:7897"
  https_proxy: "http://127.0.0.1:7897"
  all_proxy: "socks5://127.0.0.1:7897"
```

## 使用流程

### 1. 查看云端视频

1. 打开Web界面
2. 在"云端视频"标签页查看可下载的视频列表
3. 支持分页浏览，每页显示20个视频

### 2. 选择和下载视频

1. 点击视频卡片选择视频（支持多选）
2. 使用"全选"或"清空选择"按钮批量操作
3. 点击"下载选中"开始下载任务

### 3. 监控下载进度

1. 切换到"下载任务"标签页
2. 实时查看下载进度和状态
3. 可以取消正在进行的任务

### 4. 管理本地视频

1. 在"本地视频"标签页查看已下载的视频
2. 查看视频详细信息和存储统计
3. 删除不需要的视频文件

## 数据组织结构

### 按设备分类存储

```
data/by_device/
└── device202502270220f7cbb4421000/
    └── 2025-01-23_20-46-05_hls/
        ├── playlist.m3u8              # HLS播放列表
        ├── segment_0001.ts            # 视频片段1
        ├── segment_0002.ts            # 视频片段2
        ├── ...
        ├── cat_weight_1737712707.json # 重量数据
        └── metadata.json              # 视频元数据
```

### 按猫分类存储（符号链接）

```
data/by_cat/
└── cat_12345/
    └── 2025-01-23_20-46-05_hls/      # 符号链接到设备目录
```

### 元数据文件格式

`metadata.json` 包含完整的视频信息：

```json
{
  "video_id": "video_123",
  "device_id": "device202502270220f7cbb4421000",
  "animal_id": "cat_456",
  "start_time": 1737712707,
  "end_time": 1737712800,
  "weight_litter": 2.5,
  "weight_cat": 4.2,
  "weight_waste": 0.3,
  "behavior_type": "normal_poop",
  "cat_confidence": 0.95,
  "created_at": "2025-01-23 20:46:05",
  "downloaded_at": "2025-01-24 10:30:15"
}
```

## API接口文档

### 获取云端视频列表

```bash
GET /api/videos/remote?page=1&page_size=20&device_id=xxx&animal_id=xxx
```

### 批量下载视频

```bash
POST /api/videos/download
Content-Type: application/json

{
  "video_ids": ["video_123", "video_456"]
}
```

### 获取下载任务状态

```bash
GET /api/tasks                # 获取所有任务
GET /api/tasks/{task_id}      # 获取特定任务
DELETE /api/tasks/{task_id}   # 取消任务
```

### 本地视频管理

```bash
GET /api/videos/local         # 获取本地视频列表
DELETE /api/videos/{video_id} # 删除本地视频
```

### 存储管理

```bash
GET /api/storage/stats        # 获取存储统计
POST /api/storage/cleanup     # 清理空目录
```

## 故障排除

### 1. 服务启动失败

**问题**: 端口被占用
```bash
# 检查端口占用
lsof -i :8080

# 修改配置文件中的端口
```

**问题**: 配置文件错误
```bash
# 检查配置文件格式
cat config.yaml
```

### 2. 连接后端失败

**问题**: 网络连接问题
```bash
# 测试连接
curl http://your-backend-server/api/health
```

**问题**: 认证失败
- 检查auth_token是否正确
- 确认token权限是否足够

### 3. 下载失败

**问题**: MinIO连接失败
- 检查网络连接
- 确认MinIO配置正确

**问题**: 磁盘空间不足
```bash
# 检查磁盘空间
df -h
```

**问题**: 权限问题
```bash
# 检查目录权限
ls -la data/
```

### 4. 代理问题

**问题**: 代理连接失败
```bash
# 测试代理
curl --proxy http://127.0.0.1:7897 http://www.google.com
```

**问题**: 代理配置错误
- 检查代理地址和端口
- 确认代理协议类型

## 性能优化

### 1. 调整并发数

根据网络带宽和系统性能调整 `max_concurrent` 参数：

```yaml
download:
  max_concurrent: 10  # 增加并发数
```

### 2. 调整块大小

根据网络状况调整 `chunk_size`：

```yaml
download:
  chunk_size: 2097152  # 2MB，适合高速网络
```

### 3. 存储优化

- 使用SSD存储提高I/O性能
- 定期清理不需要的视频文件
- 监控磁盘使用情况

## 监控和日志

### 查看运行日志

```bash
# 运行时查看日志
./backup_server 2>&1 | tee logs/backup_server.log
```

### 监控系统资源

```bash
# 监控CPU和内存使用
top -p $(pgrep backup_server)

# 监控磁盘I/O
iotop
```

### 健康检查

```bash
# 检查服务状态
curl http://localhost:8080/api/health

# 检查后端连接
curl http://localhost:8080/api/test
```

## 备份和恢复

### 备份数据

```bash
# 备份配置和数据
tar -czf backup_$(date +%Y%m%d).tar.gz config.yaml data/
```

### 恢复数据

```bash
# 恢复数据
tar -xzf backup_20250124.tar.gz
```

## 安全注意事项

1. **认证Token**: 妥善保管factory token，不要泄露
2. **网络安全**: 在生产环境中使用HTTPS
3. **访问控制**: 限制Web界面的访问权限
4. **数据加密**: 考虑对敏感数据进行加密存储

## 联系支持

如遇到问题，请提供以下信息：
- 错误日志
- 配置文件（隐藏敏感信息）
- 系统环境信息
- 复现步骤
