# Stage 1: Build the Go application
FROM golang:1.21-alpine AS builder

WORKDIR /app

# Copy Go module files and download dependencies
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN go build -o backup-server .

# Stage 2: Runtime container
FROM ubuntu:22.04

# Set non-interactive installation mode to avoid timezone configuration prompts
ENV DEBIAN_FRONTEND=noninteractive

# Install timezone data and CA certificates properly 
RUN apt-get update && apt-get install -y --no-install-recommends tzdata ca-certificates curl \
    && rm -rf /var/lib/apt/lists/*

# Set environment variables
ENV TZ=UTC

# Configure timezone
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

WORKDIR /app
COPY --from=builder /app/backup-server /app/

# Copy web assets
COPY web/ /app/web/

# Create data directory
RUN mkdir -p /app/data

# Expose backup server port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/api/storage/stats || exit 1

CMD ["./backup-server"]
