# Simple runtime container using pre-built binary
FROM alpine:latest

# Accept build arguments for target platform info (for logging)
ARG TARGETPLATFORM
ARG TARGETOS
ARG TARGETARCH

# Log build information
RUN echo "Building container for platform: ${TARGETPLATFORM:-unknown} (OS: ${TARGETOS:-linux}, ARCH: ${TARGETARCH:-unknown})"

WORKDIR /app

# Copy the pre-built binary (built by the deployment script)
COPY backup-server /app/

# Copy configuration file
COPY config.yaml /app/

# Copy web assets
COPY web/ /app/web/

# Create data directory
RUN mkdir -p /app/data

# Set environment variables
ENV TZ=UTC

# Expose backup server port
EXPOSE 8080

# Health check (using basic shell commands available in Alpine)
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD nc -z localhost 8080 || exit 1

CMD ["./backup-server"]
