#!/bin/bash

# Script to check network connectivity for backup server

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Network Connectivity Check ===${NC}"

# Check if running in Docker
if [ -f /.dockerenv ]; then
    echo -e "${YELLOW}Running inside Docker container${NC}"
    BACKEND_HOST="host.docker.internal"
    PROXY_HOST="host.docker.internal"
else
    echo -e "${YELLOW}Running on host system${NC}"
    BACKEND_HOST="127.0.0.1"
    PROXY_HOST="127.0.0.1"
fi

# Backend server check
echo -e "\n${BLUE}=== Backend Server Check ===${NC}"

# Check production API
PRODUCTION_API="https://api.caby.care"
echo -e "${YELLOW}Testing production API: ${PRODUCTION_API}${NC}"

if command -v curl &> /dev/null; then
    if curl -s --connect-timeout 10 "${PRODUCTION_API}/api/backup/cats" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Production API is reachable${NC}"
    else
        echo -e "${RED}✗ Production API is not reachable${NC}"
        echo -e "  URL: ${PRODUCTION_API}/api/backup/cats"
        echo -e "  Check your internet connection and API status"
    fi

    # Test with verbose output for debugging
    echo -e "${YELLOW}Testing with verbose output:${NC}"
    curl -v --connect-timeout 10 "${PRODUCTION_API}/api/backup/cats" 2>&1 | head -10
else
    echo -e "${YELLOW}⚠ curl not available, skipping HTTP test${NC}"
fi

# Legacy local backend check (for development)
if [ ! -f /.dockerenv ]; then
    BACKEND_URL="http://${BACKEND_HOST}:5678"
    echo -e "\n${YELLOW}Testing local backend (development): ${BACKEND_URL}${NC}"

    if curl -s --connect-timeout 5 "${BACKEND_URL}/api/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Local backend server is reachable${NC}"
    else
        echo -e "${YELLOW}⚠ Local backend server is not reachable (expected in production)${NC}"
    fi
fi

# Test basic connectivity
if command -v nc &> /dev/null; then
    if nc -z ${BACKEND_HOST} 5678 2>/dev/null; then
        echo -e "${GREEN}✓ Port 5678 is open on ${BACKEND_HOST}${NC}"
    else
        echo -e "${RED}✗ Port 5678 is not accessible on ${BACKEND_HOST}${NC}"
    fi
else
    echo -e "${YELLOW}⚠ netcat not available, skipping port test${NC}"
fi

# Proxy server check
echo -e "\n${BLUE}=== Proxy Server Check ===${NC}"
PROXY_URL="http://${PROXY_HOST}:7897"
echo -e "${YELLOW}Testing proxy server: ${PROXY_URL}${NC}"

if command -v curl &> /dev/null; then
    if curl -s --connect-timeout 5 --proxy "${PROXY_URL}" "http://www.google.com" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Proxy server is working${NC}"
    else
        echo -e "${RED}✗ Proxy server is not working${NC}"
        echo -e "  Proxy: ${PROXY_URL}"
        echo -e "  This is expected if proxy is not running"
    fi
fi

# Test proxy port
if command -v nc &> /dev/null; then
    if nc -z ${PROXY_HOST} 7897 2>/dev/null; then
        echo -e "${GREEN}✓ Port 7897 is open on ${PROXY_HOST}${NC}"
    else
        echo -e "${RED}✗ Port 7897 is not accessible on ${PROXY_HOST}${NC}"
    fi
fi

# DNS resolution check
echo -e "\n${BLUE}=== DNS Resolution Check ===${NC}"

if [ -f /.dockerenv ]; then
    # Inside Docker
    if nslookup host.docker.internal > /dev/null 2>&1; then
        echo -e "${GREEN}✓ host.docker.internal resolves correctly${NC}"
        HOST_IP=$(nslookup host.docker.internal | grep -A1 "Name:" | tail -1 | awk '{print $2}')
        echo -e "${YELLOW}  Resolved to: ${HOST_IP}${NC}"
    else
        echo -e "${RED}✗ host.docker.internal does not resolve${NC}"
        echo -e "  This may indicate Docker networking issues"
    fi
else
    # On host
    echo -e "${GREEN}✓ Running on host, no special DNS needed${NC}"
fi

# Configuration recommendations
echo -e "\n${BLUE}=== Configuration Recommendations ===${NC}"

echo -e "${YELLOW}For production deployment:${NC}"
echo -e "  Backend URL: https://api.caby.care"
echo -e "  This is the recommended configuration"
echo -e "  No proxy needed for HTTPS API access"

if [ ! -f /.dockerenv ]; then
    echo -e "\n${YELLOW}For local development (optional):${NC}"
    echo -e "  Backend URL: http://127.0.0.1:5678"
    echo -e "  Proxy URL: http://127.0.0.1:7897"
    echo -e "  Use localhost addresses in config.yaml"
fi

# Show current configuration
echo -e "\n${BLUE}=== Current Configuration ===${NC}"
if [ -f config.yaml ]; then
    echo -e "${YELLOW}Backend URL in config.yaml:${NC}"
    grep -A1 "backend:" config.yaml | grep "url:" | sed 's/^/  /'
    
    echo -e "${YELLOW}Proxy configuration in config.yaml:${NC}"
    grep -A3 "proxy:" config.yaml | grep -E "(http_proxy|https_proxy):" | sed 's/^/  /'
else
    echo -e "${RED}config.yaml not found${NC}"
fi

echo -e "\n${GREEN}Network check complete!${NC}"
