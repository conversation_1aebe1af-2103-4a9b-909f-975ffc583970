#!/bin/bash

# Script to debug MinIO storage structure for backup_server

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== MinIO Storage Structure Debug ===${NC}"

# Check if mc (MinIO Client) is available
if ! command -v mc &> /dev/null; then
    echo -e "${RED}MinIO Client (mc) is not installed.${NC}"
    echo "Please install mc first: https://docs.min.io/docs/minio-client-quickstart-guide.html"
    exit 1
fi

# MinIO configuration from backup_server config
MINIO_ENDPOINT="***************:9000"
MINIO_ACCESS_KEY="animsuper"
MINIO_SECRET_KEY="4vbtCeEQgcN2uB"
BUCKET="records"

echo -e "${YELLOW}MinIO Configuration:${NC}"
echo -e "  Endpoint: $MINIO_ENDPOINT"
echo -e "  Bucket: $BUCKET"
echo -e "  Access Key: $MINIO_ACCESS_KEY"

# Configure mc alias
echo -e "\n${YELLOW}Configuring MinIO client...${NC}"
mc alias set backup-minio http://$MINIO_ENDPOINT $MINIO_ACCESS_KEY $MINIO_SECRET_KEY

if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to configure MinIO client.${NC}"
    exit 1
fi

# Test connection
echo -e "\n${YELLOW}Testing MinIO connection...${NC}"
mc ls backup-minio > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to connect to MinIO server.${NC}"
    exit 1
fi
echo -e "${GREEN}✓ MinIO connection successful.${NC}"

# List buckets
echo -e "\n${YELLOW}Available buckets:${NC}"
mc ls backup-minio

# Check if records bucket exists
echo -e "\n${YELLOW}Checking records bucket...${NC}"
mc ls backup-minio/$BUCKET > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo -e "${RED}Records bucket does not exist or is not accessible.${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Records bucket is accessible.${NC}"

# List device directories
echo -e "\n${YELLOW}Device directories in records bucket:${NC}"
mc ls backup-minio/$BUCKET/ | grep "device"

# Get a sample device directory
SAMPLE_DEVICE=$(mc ls backup-minio/$BUCKET/ | grep "device" | head -1 | awk '{print $5}' | sed 's/\///g')

if [ -z "$SAMPLE_DEVICE" ]; then
    echo -e "${RED}No device directories found in records bucket.${NC}"
    exit 1
fi

echo -e "\n${YELLOW}Sample device: $SAMPLE_DEVICE${NC}"

# List video folders in sample device
echo -e "\n${YELLOW}Video folders in $SAMPLE_DEVICE:${NC}"
mc ls backup-minio/$BUCKET/$SAMPLE_DEVICE/ | head -10

# Get a sample video folder
SAMPLE_FOLDER=$(mc ls backup-minio/$BUCKET/$SAMPLE_DEVICE/ | grep "_hls" | head -1 | awk '{print $5}' | sed 's/\///g')

if [ -z "$SAMPLE_FOLDER" ]; then
    echo -e "${RED}No HLS video folders found in $SAMPLE_DEVICE.${NC}"
    echo -e "${YELLOW}Available folders:${NC}"
    mc ls backup-minio/$BUCKET/$SAMPLE_DEVICE/ | head -5
else
    echo -e "\n${YELLOW}Sample video folder: $SAMPLE_FOLDER${NC}"
    
    # List files in sample video folder
    echo -e "\n${YELLOW}Files in $SAMPLE_DEVICE/$SAMPLE_FOLDER:${NC}"
    mc ls backup-minio/$BUCKET/$SAMPLE_DEVICE/$SAMPLE_FOLDER/
fi

# Show path structure analysis
echo -e "\n${BLUE}=== Path Structure Analysis ===${NC}"
echo -e "${YELLOW}Expected path format:${NC}"
echo -e "  records/device{deviceID}/{videoFolder}/"
echo -e "  Example: records/device123/2025-01-23_20-46-05_hls/"

echo -e "\n${YELLOW}Actual path format found:${NC}"
if [ ! -z "$SAMPLE_DEVICE" ] && [ ! -z "$SAMPLE_FOLDER" ]; then
    echo -e "  $BUCKET/$SAMPLE_DEVICE/$SAMPLE_FOLDER/"
else
    echo -e "  Unable to determine - no video folders found"
fi

# Check for common video file extensions
if [ ! -z "$SAMPLE_DEVICE" ] && [ ! -z "$SAMPLE_FOLDER" ]; then
    echo -e "\n${YELLOW}Checking for video files...${NC}"
    
    # Check for .ts files (HLS segments)
    TS_COUNT=$(mc ls backup-minio/$BUCKET/$SAMPLE_DEVICE/$SAMPLE_FOLDER/ | grep "\.ts$" | wc -l)
    echo -e "  .ts files: $TS_COUNT"
    
    # Check for .m3u8 files (HLS playlists)
    M3U8_COUNT=$(mc ls backup-minio/$BUCKET/$SAMPLE_DEVICE/$SAMPLE_FOLDER/ | grep "\.m3u8$" | wc -l)
    echo -e "  .m3u8 files: $M3U8_COUNT"
    
    # Check for other files
    OTHER_COUNT=$(mc ls backup-minio/$BUCKET/$SAMPLE_DEVICE/$SAMPLE_FOLDER/ | grep -v "\.ts$" | grep -v "\.m3u8$" | wc -l)
    echo -e "  Other files: $OTHER_COUNT"
    
    if [ $TS_COUNT -eq 0 ] && [ $M3U8_COUNT -eq 0 ]; then
        echo -e "${RED}⚠ No video files found in sample folder!${NC}"
    else
        echo -e "${GREEN}✓ Video files found.${NC}"
    fi
fi

# Provide debugging recommendations
echo -e "\n${BLUE}=== Debugging Recommendations ===${NC}"
echo -e "${YELLOW}1. Check video folder naming:${NC}"
echo -e "   - Should be in format: YYYY-MM-DD_HH-MM-SS_hls"
echo -e "   - Example: 2025-01-23_20-46-05_hls"

echo -e "\n${YELLOW}2. Verify MinIO path construction:${NC}"
echo -e "   - Device path: device{deviceID}"
echo -e "   - Full path: records/device{deviceID}/{videoFolder}/"

echo -e "\n${YELLOW}3. Check timestamp conversion:${NC}"
echo -e "   - Unix timestamp should be converted to local time"
echo -e "   - Format: time.Unix(startTime, 0).Format(\"2006-01-02_15-04-05\") + \"_hls\""

echo -e "\n${YELLOW}4. Manual verification commands:${NC}"
if [ ! -z "$SAMPLE_DEVICE" ] && [ ! -z "$SAMPLE_FOLDER" ]; then
    echo -e "   mc ls backup-minio/$BUCKET/$SAMPLE_DEVICE/$SAMPLE_FOLDER/"
    echo -e "   mc cat backup-minio/$BUCKET/$SAMPLE_DEVICE/$SAMPLE_FOLDER/playlist.m3u8"
fi

echo -e "\n${GREEN}MinIO debug completed!${NC}"
