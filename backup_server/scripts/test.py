#!/usr/bin/env python3
"""
Backup Server API Test Script

This script tests the main functionality of the backup server API.
"""

import requests
import json
import sys
import time
from typing import Dict, Any, Optional

# Configuration
BASE_URL = "http://localhost:8080"
TIMEOUT = 10

# Colors for output
class Colors:
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    RED = '\033[0;31m'
    BLUE = '\033[0;34m'
    NC = '\033[0m'  # No Color

def print_colored(message: str, color: str = Colors.NC):
    """Print colored message"""
    print(f"{color}{message}{Colors.NC}")

def make_request(method: str, endpoint: str, data: Optional[Dict[Any, Any]] = None) -> Optional[Dict[Any, Any]]:
    """Make HTTP request to the backup server"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, timeout=TIMEOUT)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, timeout=TIMEOUT)
        else:
            print_colored(f"Unsupported method: {method}", Colors.RED)
            return None
        
        if response.status_code == 200:
            return response.json()
        else:
            print_colored(f"Request failed: {response.status_code} - {response.text}", Colors.RED)
            return None
            
    except requests.exceptions.ConnectionError:
        print_colored(f"Connection failed to {url}", Colors.RED)
        return None
    except requests.exceptions.Timeout:
        print_colored(f"Request timeout to {url}", Colors.RED)
        return None
    except Exception as e:
        print_colored(f"Request error: {str(e)}", Colors.RED)
        return None

def test_health_check():
    """Test server health"""
    print_colored("Testing server health...", Colors.BLUE)
    
    # Test root endpoint
    response = make_request("GET", "/")
    if response is None:
        print_colored("❌ Server is not responding", Colors.RED)
        return False
    
    print_colored("✅ Server is responding", Colors.GREEN)
    return True

def test_storage_stats():
    """Test storage statistics API"""
    print_colored("Testing storage statistics...", Colors.BLUE)
    
    response = make_request("GET", "/api/storage/stats")
    if response is None:
        print_colored("❌ Storage stats API failed", Colors.RED)
        return False
    
    # Check expected fields
    expected_fields = ["total_videos", "total_size", "device_count", "cat_count"]
    for field in expected_fields:
        if field not in response:
            print_colored(f"❌ Missing field in storage stats: {field}", Colors.RED)
            return False
    
    print_colored(f"✅ Storage stats: {response['total_videos']} videos, {response['cat_count']} cats", Colors.GREEN)
    return True

def test_cats_api():
    """Test cats API"""
    print_colored("Testing cats API...", Colors.BLUE)
    
    response = make_request("GET", "/api/cats")
    if response is None:
        print_colored("❌ Cats API failed", Colors.RED)
        return False
    
    if "cats" not in response:
        print_colored("❌ Missing 'cats' field in response", Colors.RED)
        return False
    
    cats_count = len(response["cats"])
    print_colored(f"✅ Found {cats_count} cats", Colors.GREEN)
    
    # Test individual cat if available
    if cats_count > 0:
        cat_id = response["cats"][0]["id"]
        cat_response = make_request("GET", f"/api/videos/local/cat/{cat_id}?page=1&page_size=5")
        if cat_response is not None:
            print_colored(f"✅ Cat videos API working for cat {cat_id}", Colors.GREEN)
        else:
            print_colored(f"❌ Cat videos API failed for cat {cat_id}", Colors.RED)
            return False
    
    return True

def test_tasks_api():
    """Test tasks API"""
    print_colored("Testing tasks API...", Colors.BLUE)
    
    # Test task stats
    response = make_request("GET", "/api/tasks/stats")
    if response is None:
        print_colored("❌ Task stats API failed", Colors.RED)
        return False
    
    if "status_counts" not in response:
        print_colored("❌ Missing 'status_counts' field in task stats", Colors.RED)
        return False
    
    print_colored("✅ Task stats API working", Colors.GREEN)
    
    # Test task list
    response = make_request("GET", "/api/tasks?page=1&page_size=10")
    if response is None:
        print_colored("❌ Task list API failed", Colors.RED)
        return False
    
    if "tasks" not in response:
        print_colored("❌ Missing 'tasks' field in task list", Colors.RED)
        return False
    
    print_colored(f"✅ Task list API working, found {len(response['tasks'])} tasks", Colors.GREEN)
    return True

def test_conversions_api():
    """Test conversions API"""
    print_colored("Testing conversions API...", Colors.BLUE)
    
    # Test conversion stats
    response = make_request("GET", "/api/conversions/stats")
    if response is None:
        print_colored("❌ Conversion stats API failed", Colors.RED)
        return False
    
    if "status_counts" not in response:
        print_colored("❌ Missing 'status_counts' field in conversion stats", Colors.RED)
        return False
    
    print_colored("✅ Conversion stats API working", Colors.GREEN)
    
    # Test conversion list
    response = make_request("GET", "/api/conversions?page=1&page_size=10")
    if response is None:
        print_colored("❌ Conversion list API failed", Colors.RED)
        return False
    
    if "tasks" not in response:
        print_colored("❌ Missing 'tasks' field in conversion list", Colors.RED)
        return False
    
    print_colored(f"✅ Conversion list API working, found {len(response['tasks'])} tasks", Colors.GREEN)
    return True

def main():
    """Main test function"""
    print_colored("🧪 Backup Server API Test Suite", Colors.BLUE)
    print_colored("=" * 40, Colors.BLUE)
    
    tests = [
        ("Health Check", test_health_check),
        ("Storage Stats", test_storage_stats),
        ("Cats API", test_cats_api),
        ("Tasks API", test_tasks_api),
        ("Conversions API", test_conversions_api),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print_colored(f"\n📋 Running: {test_name}", Colors.YELLOW)
        if test_func():
            passed += 1
        time.sleep(0.5)  # Small delay between tests
    
    print_colored("\n" + "=" * 40, Colors.BLUE)
    if passed == total:
        print_colored(f"🎉 All tests passed! ({passed}/{total})", Colors.GREEN)
        sys.exit(0)
    else:
        print_colored(f"❌ Some tests failed. ({passed}/{total} passed)", Colors.RED)
        sys.exit(1)

if __name__ == "__main__":
    main()
