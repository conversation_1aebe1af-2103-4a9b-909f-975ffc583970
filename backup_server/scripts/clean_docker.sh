#!/bin/bash

# Script to clean up Docker resources for backup_server

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Directory where docker-compose.yml is located
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_DIR" || { echo -e "${RED}Failed to change directory to $PROJECT_DIR${NC}"; exit 1; }

# Default options
AGGRESSIVE_CLEAN=false
KEEP_VOLUMES=false

# Parse command line arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --aggressive) AGGRESSIVE_CLEAN=true ;;
        --keep-volumes) KEEP_VOLUMES=true ;;
        -h|--help)
            echo "Usage: ./clean_docker.sh [options]"
            echo "Options:"
            echo "  --aggressive     Perform aggressive cleanup (removes all unused Docker resources)"
            echo "  --keep-volumes   Keep Docker volumes (default: remove volumes)"
            echo "  -h, --help       Show this help message"
            exit 0
            ;;
        *) echo "Unknown option: $1"; exit 1 ;;
    esac
    shift
done

echo -e "${BLUE}=== Docker Cleanup for Backup Server ===${NC}"

# Step 1: Stop and remove containers
echo -e "${YELLOW}Step 1: Stopping and removing containers...${NC}"
if [ "$KEEP_VOLUMES" = true ]; then
    docker-compose down --remove-orphans
else
    docker-compose down --volumes --remove-orphans
fi

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Successfully stopped and removed containers.${NC}"
else
    echo -e "${RED}✗ Failed to stop containers.${NC}"
    exit 1
fi

# Step 2: Remove project-specific images
echo -e "${YELLOW}Step 2: Removing backup-server images...${NC}"

# Remove backup-server images
backup_images=$(docker images backup-server -q)
if [ ! -z "$backup_images" ]; then
    docker rmi -f $backup_images
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Successfully removed backup-server images.${NC}"
    else
        echo -e "${YELLOW}⚠ Warning: Some backup-server images could not be removed.${NC}"
    fi
else
    echo -e "${GREEN}✓ No backup-server images found.${NC}"
fi

# Remove docker-compose generated images
compose_images=$(docker images backup_server-backup-server -q)
if [ ! -z "$compose_images" ]; then
    docker rmi -f $compose_images
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Successfully removed docker-compose images.${NC}"
    else
        echo -e "${YELLOW}⚠ Warning: Some docker-compose images could not be removed.${NC}"
    fi
else
    echo -e "${GREEN}✓ No docker-compose images found.${NC}"
fi

# Step 3: Clean up dangling resources
echo -e "${YELLOW}Step 3: Cleaning up dangling resources...${NC}"

# Clean up dangling images
echo -e "${BLUE}  - Cleaning up dangling images...${NC}"
dangling_images=$(docker images -f "dangling=true" -q)
if [ ! -z "$dangling_images" ]; then
    docker image prune -f
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}    ✓ Successfully cleaned up dangling images.${NC}"
    else
        echo -e "${YELLOW}    ⚠ Warning: Failed to clean up some dangling images.${NC}"
    fi
else
    echo -e "${GREEN}    ✓ No dangling images found.${NC}"
fi

# Clean up unused build cache
echo -e "${BLUE}  - Cleaning up build cache...${NC}"
docker builder prune -f > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo -e "${GREEN}    ✓ Successfully cleaned up build cache.${NC}"
else
    echo -e "${YELLOW}    ⚠ Warning: Failed to clean up build cache.${NC}"
fi

# Step 4: Aggressive cleanup (if requested)
if [ "$AGGRESSIVE_CLEAN" = true ]; then
    echo -e "${YELLOW}Step 4: Performing aggressive cleanup...${NC}"
    
    # Remove all unused images
    echo -e "${BLUE}  - Removing all unused images...${NC}"
    docker image prune -a -f > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}    ✓ Successfully removed unused images.${NC}"
    else
        echo -e "${YELLOW}    ⚠ Warning: Failed to remove some unused images.${NC}"
    fi
    
    # Remove unused networks
    echo -e "${BLUE}  - Removing unused networks...${NC}"
    docker network prune -f > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}    ✓ Successfully removed unused networks.${NC}"
    else
        echo -e "${YELLOW}    ⚠ Warning: Failed to remove some networks.${NC}"
    fi
    
    # Remove unused volumes (if not keeping them)
    if [ "$KEEP_VOLUMES" = false ]; then
        echo -e "${BLUE}  - Removing unused volumes...${NC}"
        docker volume prune -f > /dev/null 2>&1
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}    ✓ Successfully removed unused volumes.${NC}"
        else
            echo -e "${YELLOW}    ⚠ Warning: Failed to remove some volumes.${NC}"
        fi
    fi
    
    # System-wide cleanup
    echo -e "${BLUE}  - Performing system-wide cleanup...${NC}"
    docker system prune -f > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}    ✓ Successfully performed system cleanup.${NC}"
    else
        echo -e "${YELLOW}    ⚠ Warning: System cleanup had some issues.${NC}"
    fi
else
    echo -e "${YELLOW}Step 4: Skipping aggressive cleanup (use --aggressive for full cleanup).${NC}"
fi

# Step 5: Show disk space saved
echo -e "${YELLOW}Step 5: Cleanup summary...${NC}"

# Show remaining Docker resources
echo -e "${BLUE}Remaining Docker resources:${NC}"
echo -e "${BLUE}Images:${NC}"
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | head -10

echo -e "\n${BLUE}Containers:${NC}"
docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Size}}"

echo -e "\n${BLUE}Volumes:${NC}"
docker volume ls --format "table {{.Driver}}\t{{.Name}}"

echo -e "\n${GREEN}Docker cleanup completed successfully!${NC}"

# Provide next steps
echo -e "\n${BLUE}Next steps:${NC}"
echo -e "  - Run: ${GREEN}./scripts/quick_deploy.sh${NC} to deploy with fresh images"
echo -e "  - Run: ${GREEN}./scripts/quick_deploy.sh --no-clean${NC} for faster deployment"
echo -e "  - Run: ${GREEN}./scripts/clean_docker.sh --aggressive${NC} for complete cleanup"
