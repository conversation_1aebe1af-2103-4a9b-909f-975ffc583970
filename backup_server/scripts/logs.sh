#!/bin/bash

# Script to view backup server logs

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Directory where docker-compose.yml is located
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_DIR" || { echo -e "${RED}Failed to change directory to $PROJECT_DIR${NC}"; exit 1; }

# Default options
FOLLOW=false
TAIL_LINES=100

# Parse command line arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        -f|--follow) FOLLOW=true ;;
        -n|--lines)
            if [[ -n $2 && $2 =~ ^[0-9]+$ ]]; then
                TAIL_LINES=$2
                shift
            else
                echo -e "${RED}Error: --lines requires a number${NC}"
                exit 1
            fi
            ;;
        -h|--help)
            echo "Usage: ./logs.sh [options]"
            echo "Options:"
            echo "  -f, --follow    Follow log output"
            echo "  -n, --lines N   Show last N lines (default: 100)"
            echo "  -h, --help      Show this help message"
            echo ""
            echo "Examples:"
            echo "  ./logs.sh                    # Show last 100 lines"
            echo "  ./logs.sh -f                # Follow logs"
            echo "  ./logs.sh -n 50             # Show last 50 lines"
            echo "  ./logs.sh -f -n 200         # Follow logs, starting with last 200 lines"
            exit 0
            ;;
        *) echo "Unknown option: $1"; exit 1 ;;
    esac
    shift
done

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Error: docker-compose is not installed.${NC}"
    exit 1
fi

# Check if backup server is running
container_id=$(docker-compose ps -q backup-server 2>/dev/null)
if [ -z "$container_id" ]; then
    echo -e "${RED}Error: backup-server container is not running.${NC}"
    echo -e "${YELLOW}Run './scripts/quick_deploy.sh' to start the server first.${NC}"
    exit 1
fi

echo -e "${GREEN}Backup Server Logs${NC}"
echo -e "${BLUE}Container ID: ${container_id}${NC}"
echo -e "${BLUE}Lines: ${TAIL_LINES}${NC}"
if [ "$FOLLOW" = true ]; then
    echo -e "${BLUE}Mode: Following (Press Ctrl+C to exit)${NC}"
else
    echo -e "${BLUE}Mode: Static${NC}"
fi
echo -e "${YELLOW}===========================================${NC}"

# Show logs
if [ "$FOLLOW" = true ]; then
    docker-compose logs -f --tail="$TAIL_LINES" backup-server
else
    docker-compose logs --tail="$TAIL_LINES" backup-server
fi
