#!/bin/bash

# Script to detect system architecture and Docker platform compatibility

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Architecture Detection Report ===${NC}"

# Detect host architecture
HOST_ARCH=$(uname -m)
echo -e "${YELLOW}Host Architecture:${NC} $HOST_ARCH"

# Map to Docker platform
case $HOST_ARCH in
    x86_64)
        DOCKER_PLATFORM="linux/amd64"
        GO_ARCH="amd64"
        ARCH_NAME="x86_64 (Intel/AMD 64-bit)"
        ;;
    aarch64|arm64)
        DOCKER_PLATFORM="linux/arm64"
        GO_ARCH="arm64"
        ARCH_NAME="ARM64 (Apple Silicon/ARM 64-bit)"
        ;;
    i386|i686)
        DOCKER_PLATFORM="linux/386"
        GO_ARCH="386"
        ARCH_NAME="x86 (32-bit) - Limited Support"
        ;;
    armv7l)
        DOCKER_PLATFORM="linux/arm/v7"
        GO_ARCH="arm"
        ARCH_NAME="ARM v7 (32-bit ARM)"
        ;;
    *)
        DOCKER_PLATFORM="unknown"
        GO_ARCH="unknown"
        ARCH_NAME="Unknown/Unsupported"
        ;;
esac

echo -e "${YELLOW}Architecture Name:${NC} $ARCH_NAME"
echo -e "${YELLOW}Docker Platform:${NC} $DOCKER_PLATFORM"
echo -e "${YELLOW}Go Architecture:${NC} $GO_ARCH"

# Check Docker availability
echo -e "\n${BLUE}=== Docker Environment Check ===${NC}"

if command -v docker &> /dev/null; then
    echo -e "${GREEN}✓ Docker is installed${NC}"
    
    # Check Docker version
    DOCKER_VERSION=$(docker --version)
    echo -e "${YELLOW}Docker Version:${NC} $DOCKER_VERSION"
    
    # Check if Docker daemon is running
    if docker info &> /dev/null; then
        echo -e "${GREEN}✓ Docker daemon is running${NC}"
        
        # Check Docker platform
        DOCKER_ARCH=$(docker info --format '{{.Architecture}}')
        echo -e "${YELLOW}Docker Architecture:${NC} $DOCKER_ARCH"
        
        # Check buildx availability
        if docker buildx version &> /dev/null; then
            echo -e "${GREEN}✓ Docker Buildx is available${NC}"
            
            # List available platforms
            echo -e "${YELLOW}Available Buildx Platforms:${NC}"
            docker buildx ls | grep -E "linux/(amd64|arm64|arm)" || echo "  No multi-platform builders found"
        else
            echo -e "${YELLOW}⚠ Docker Buildx is not available${NC}"
        fi
    else
        echo -e "${RED}✗ Docker daemon is not running${NC}"
    fi
else
    echo -e "${RED}✗ Docker is not installed${NC}"
fi

# Check docker-compose availability
if command -v docker-compose &> /dev/null; then
    echo -e "${GREEN}✓ Docker Compose is installed${NC}"
    COMPOSE_VERSION=$(docker-compose --version)
    echo -e "${YELLOW}Compose Version:${NC} $COMPOSE_VERSION"
else
    echo -e "${RED}✗ Docker Compose is not installed${NC}"
fi

# Check Go availability (for development)
echo -e "\n${BLUE}=== Go Environment Check ===${NC}"

if command -v go &> /dev/null; then
    echo -e "${GREEN}✓ Go is installed${NC}"
    GO_VERSION=$(go version)
    echo -e "${YELLOW}Go Version:${NC} $GO_VERSION"
    
    # Check Go architecture
    GO_HOST_ARCH=$(go env GOARCH)
    GO_HOST_OS=$(go env GOOS)
    echo -e "${YELLOW}Go Host OS/Arch:${NC} $GO_HOST_OS/$GO_HOST_ARCH"
else
    echo -e "${YELLOW}⚠ Go is not installed (not required for Docker deployment)${NC}"
fi

# Recommendations
echo -e "\n${BLUE}=== Recommendations ===${NC}"

if [ "$DOCKER_PLATFORM" = "unknown" ]; then
    echo -e "${RED}⚠ Unsupported architecture detected${NC}"
    echo -e "  Your architecture ($HOST_ARCH) may not be fully supported."
    echo -e "  Consider using a supported platform: x86_64 or arm64"
elif [ "$DOCKER_PLATFORM" = "linux/386" ] || [ "$DOCKER_PLATFORM" = "linux/arm/v7" ]; then
    echo -e "${YELLOW}⚠ Limited support architecture${NC}"
    echo -e "  Your architecture has limited Docker image support."
    echo -e "  Consider upgrading to 64-bit architecture if possible."
else
    echo -e "${GREEN}✓ Your architecture is fully supported${NC}"
    echo -e "  Platform: $DOCKER_PLATFORM"
    echo -e "  You can use: ./scripts/quick_deploy.sh"
fi

# Platform-specific notes
echo -e "\n${BLUE}=== Platform-Specific Notes ===${NC}"

case $HOST_ARCH in
    x86_64)
        echo -e "${GREEN}Intel/AMD 64-bit:${NC}"
        echo -e "  • Excellent Docker support"
        echo -e "  • All features available"
        echo -e "  • Best performance for most workloads"
        ;;
    aarch64|arm64)
        echo -e "${GREEN}ARM64 (Apple Silicon):${NC}"
        echo -e "  • Excellent Docker support"
        echo -e "  • All features available"
        echo -e "  • Great performance and energy efficiency"
        echo -e "  • Native support on Apple M1/M2/M3 Macs"
        ;;
    *)
        echo -e "${YELLOW}Other architectures:${NC}"
        echo -e "  • May require additional configuration"
        echo -e "  • Some Docker images may not be available"
        echo -e "  • Consider building from source"
        ;;
esac

echo -e "\n${GREEN}Architecture detection complete!${NC}"
