#!/bin/bash

# Script to quickly deploy the backup server using Docker Compose

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Directory where docker-compose.yml is located
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_DIR" || { echo -e "${RED}Failed to change directory to $PROJECT_DIR${NC}"; exit 1; }

# Check for docker-compose
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Error: docker-compose is not installed.${NC}"
    echo "Please install docker-compose first."
    exit 1
fi

# Check for .env file
if [ ! -f "$PROJECT_DIR/.env" ]; then
    echo -e "${YELLOW}Warning: .env file not found.${NC}"
    echo -e "${BLUE}Creating a default .env file...${NC}"
    cat > .env << EOF
# Backup Server Configuration
TZ=Asia/Shanghai
DATA_PATH=./data
MAX_CONCURRENT_DOWNLOADS=5

# Backend Server Connection (required)
BACKEND_URL=http://localhost:5678
BACKEND_USERNAME=your_username
BACKEND_PASSWORD=your_password

# MinIO Configuration (will be fetched from backend)
# These will be automatically configured
MINIO_ENDPOINT=
MINIO_ACCESS_KEY=
MINIO_SECRET_KEY=
MINIO_BUCKET=
EOF
    echo -e "${GREEN}Created default .env file. Please edit it with your configuration.${NC}"
    echo -e "${YELLOW}You need to configure BACKEND_URL, BACKEND_USERNAME, and BACKEND_PASSWORD${NC}"
    read -p "Do you want to continue with the default configuration? [y/N] " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Please edit the .env file and run the script again."
        exit 1
    fi
fi

# Default options
SKIP_BUILD=false
FOLLOW_LOGS=false

# Parse command line arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --skip-build) SKIP_BUILD=true ;;
        --logs) FOLLOW_LOGS=true ;;
        -h|--help)
            echo "Usage: ./quick_deploy.sh [options]"
            echo "Options:"
            echo "  --skip-build    Skip the build step"
            echo "  --logs          Follow logs after deployment"
            echo "  -h, --help      Show this help message"
            exit 0
            ;;
        *) echo "Unknown option: $1"; exit 1 ;;
    esac
    shift
done

# Detect host architecture
HOST_ARCH=$(uname -m)
case $HOST_ARCH in
    x86_64)
        DOCKER_PLATFORM="linux/amd64"
        GO_ARCH="amd64"
        ;;
    aarch64|arm64)
        DOCKER_PLATFORM="linux/arm64"
        GO_ARCH="arm64"
        ;;
    *)
        echo -e "${RED}Unsupported architecture: $HOST_ARCH${NC}"
        echo "Supported architectures: x86_64, aarch64/arm64"
        exit 1
        ;;
esac

echo -e "${GREEN}Starting deployment of the backup server...${NC}"
echo -e "${BLUE}Detected architecture: $HOST_ARCH${NC}"
echo -e "${BLUE}Docker platform: $DOCKER_PLATFORM${NC}"
echo -e "${BLUE}Go architecture: $GO_ARCH${NC}"

# Step 1: Create data directory if it doesn't exist
echo -e "${YELLOW}Creating data directory...${NC}"
mkdir -p data
if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to create data directory.${NC}"
    exit 1
fi
echo -e "${GREEN}Data directory ready.${NC}"

# Step 2: Bring down any running containers
echo -e "${YELLOW}Stopping any running containers...${NC}"
docker-compose down
if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to stop containers.${NC}"
    exit 1
fi
echo -e "${GREEN}Successfully stopped containers.${NC}"

# Step 3: Build containers (unless skipped)
if [ "$SKIP_BUILD" = false ]; then
    echo -e "${YELLOW}Building Go binary for $DOCKER_PLATFORM (GOOS=linux GOARCH=$GO_ARCH)...${NC}"

    # Build Go binary for target architecture
    if command -v go &> /dev/null; then
        GOOS=linux GOARCH=$GO_ARCH go build -ldflags="-w -s" -o backup-server .
        if [ $? -ne 0 ]; then
            echo -e "${RED}Failed to build Go binary.${NC}"
            exit 1
        fi
        echo -e "${GREEN}Successfully built Go binary for $GO_ARCH.${NC}"
    else
        echo -e "${RED}Go is not installed. Please install Go to build the binary.${NC}"
        exit 1
    fi

    echo -e "${YELLOW}Building Docker container for $DOCKER_PLATFORM...${NC}"

    # Use docker buildx for multi-platform builds if available
    if command -v docker &> /dev/null; then
        if docker buildx version &> /dev/null; then
            echo -e "${BLUE}Using Docker Buildx for multi-platform build...${NC}"
            docker buildx build --platform $DOCKER_PLATFORM -t backup-server --load \
                --build-arg TARGETPLATFORM=$DOCKER_PLATFORM \
                --build-arg TARGETOS=linux \
                --build-arg TARGETARCH=$GO_ARCH .
        else
            echo -e "${BLUE}Using standard Docker build...${NC}"
            DOCKER_DEFAULT_PLATFORM=$DOCKER_PLATFORM docker build -t backup-server \
                --build-arg TARGETPLATFORM=$DOCKER_PLATFORM \
                --build-arg TARGETOS=linux \
                --build-arg TARGETARCH=$GO_ARCH .
        fi
    else
        echo -e "${RED}Docker is not available.${NC}"
        exit 1
    fi

    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to build container.${NC}"
        exit 1
    fi
    echo -e "${GREEN}Successfully built container for $DOCKER_PLATFORM.${NC}"
    
    # Clean up dangling images to save disk space
    echo -e "${YELLOW}Cleaning up dangling images (<none>:<none>)...${NC}"
    dangling_images=$(docker images -f "dangling=true" -q)
    if [ ! -z "$dangling_images" ]; then
        docker image prune -f
        if [ $? -ne 0 ]; then
            echo -e "${YELLOW}Warning: Failed to clean up some dangling images.${NC}"
        else
            echo -e "${GREEN}Successfully cleaned up dangling images.${NC}"
        fi
    else
        echo -e "${GREEN}No dangling images found.${NC}"
    fi
else
    echo -e "${YELLOW}Skipping build step as requested.${NC}"
fi

# Step 4: Start containers in detached mode
echo -e "${YELLOW}Starting backup server container...${NC}"
docker-compose up -d
if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to start container.${NC}"
    exit 1
fi

echo -e "${GREEN}Deployment completed successfully!${NC}"
echo -e "Services:"
echo -e "  - Backup server: ${BLUE}http://localhost:8080${NC}"
echo -e "  - Web interface: ${BLUE}http://localhost:8080${NC}"

# Show container status
echo -e "${YELLOW}Container status:${NC}"
docker-compose ps

# Display container hash for log inspection
echo -e "\n${YELLOW}Container hash for log inspection:${NC}"
container_id=$(docker-compose ps -q backup-server)
if [ ! -z "$container_id" ]; then
    echo -e "backup-server: ${container_id}"
    echo -e "  - Check logs with: ${GREEN}docker logs ${container_id}${NC}"
    echo -e "  - Follow logs with: ${GREEN}docker logs -f ${container_id}${NC}"
else
    echo -e "backup-server: ${RED}Not running${NC}"
fi

echo -e "\n${GREEN}To check logs, run: docker-compose logs -f${NC}"

# Follow logs if requested
if [ "$FOLLOW_LOGS" = true ]; then
    echo -e "\n${YELLOW}Following logs (Press Ctrl+C to exit)...${NC}"
    docker-compose logs -f
fi
