#!/bin/bash

# Script to quickly deploy the backup server using Docker Compose

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Directory where docker-compose.yml is located
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_DIR" || { echo -e "${RED}Failed to change directory to $PROJECT_DIR${NC}"; exit 1; }

# Check for docker-compose
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Error: docker-compose is not installed.${NC}"
    echo "Please install docker-compose first."
    exit 1
fi

# Check for .env file
if [ ! -f "$PROJECT_DIR/.env" ]; then
    echo -e "${YELLOW}Warning: .env file not found.${NC}"
    echo -e "${BLUE}Creating a default .env file...${NC}"
    cat > .env << EOF
# Backup Server Configuration
TZ=Asia/Shanghai
DATA_PATH=./data
MAX_CONCURRENT_DOWNLOADS=5

# Backend Server Connection (required)
BACKEND_URL=http://localhost:5678
BACKEND_USERNAME=your_username
BACKEND_PASSWORD=your_password

# MinIO Configuration (will be fetched from backend)
# These will be automatically configured
MINIO_ENDPOINT=
MINIO_ACCESS_KEY=
MINIO_SECRET_KEY=
MINIO_BUCKET=
EOF
    echo -e "${GREEN}Created default .env file. Please edit it with your configuration.${NC}"
    echo -e "${YELLOW}You need to configure BACKEND_URL, BACKEND_USERNAME, and BACKEND_PASSWORD${NC}"
    read -p "Do you want to continue with the default configuration? [y/N] " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Please edit the .env file and run the script again."
        exit 1
    fi
fi

# Default options
SKIP_BUILD=false
FOLLOW_LOGS=false
FORCE_CLEAN=true  # Always clean by default for fresh builds

# Parse command line arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --skip-build) SKIP_BUILD=true ;;
        --logs) FOLLOW_LOGS=true ;;
        --no-clean) FORCE_CLEAN=false ;;
        --force-clean) FORCE_CLEAN=true ;;
        -h|--help)
            echo "Usage: ./quick_deploy.sh [options]"
            echo "Options:"
            echo "  --skip-build    Skip the build step"
            echo "  --logs          Follow logs after deployment"
            echo "  --no-clean      Skip Docker cleanup (faster but uses more disk)"
            echo "  --force-clean   Force complete cleanup (default behavior)"
            echo "  -h, --help      Show this help message"
            exit 0
            ;;
        *) echo "Unknown option: $1"; exit 1 ;;
    esac
    shift
done

# Detect host architecture
HOST_ARCH=$(uname -m)
case $HOST_ARCH in
    x86_64)
        DOCKER_PLATFORM="linux/amd64"
        GO_ARCH="amd64"
        ;;
    aarch64|arm64)
        DOCKER_PLATFORM="linux/arm64"
        GO_ARCH="arm64"
        ;;
    *)
        echo -e "${RED}Unsupported architecture: $HOST_ARCH${NC}"
        echo "Supported architectures: x86_64, aarch64/arm64"
        exit 1
        ;;
esac

echo -e "${GREEN}Starting deployment of the backup server...${NC}"
echo -e "${BLUE}Detected architecture: $HOST_ARCH${NC}"
echo -e "${BLUE}Docker platform: $DOCKER_PLATFORM${NC}"
echo -e "${BLUE}Go architecture: $GO_ARCH${NC}"

# Step 1: Create data directory if it doesn't exist
echo -e "${YELLOW}Creating data directory...${NC}"
mkdir -p data
if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to create data directory.${NC}"
    exit 1
fi
echo -e "${GREEN}Data directory ready.${NC}"

# Step 2: Stop containers and optionally clean up
echo -e "${YELLOW}Stopping and removing any running containers...${NC}"
docker-compose down --volumes --remove-orphans
if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to stop containers.${NC}"
    exit 1
fi
echo -e "${GREEN}Successfully stopped and removed containers.${NC}"

# Conditional cleanup based on FORCE_CLEAN option
if [ "$FORCE_CLEAN" = true ]; then
    echo -e "${YELLOW}Performing complete cleanup for fresh build...${NC}"

    # Remove existing backup-server images to ensure fresh build
    echo -e "${BLUE}  - Removing existing backup-server images...${NC}"
    existing_images=$(docker images backup-server -q)
    if [ ! -z "$existing_images" ]; then
        docker rmi -f $existing_images
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}    ✓ Successfully removed existing backup-server images.${NC}"
        else
            echo -e "${YELLOW}    ⚠ Warning: Some images could not be removed (may be in use).${NC}"
        fi
    else
        echo -e "${GREEN}    ✓ No existing backup-server images found.${NC}"
    fi

    # Remove backup_server-backup-server images (docker-compose naming)
    echo -e "${BLUE}  - Removing docker-compose generated images...${NC}"
    compose_images=$(docker images backup_server-backup-server -q)
    if [ ! -z "$compose_images" ]; then
        docker rmi -f $compose_images
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}    ✓ Successfully removed docker-compose images.${NC}"
        else
            echo -e "${YELLOW}    ⚠ Warning: Some docker-compose images could not be removed.${NC}"
        fi
    else
        echo -e "${GREEN}    ✓ No docker-compose images found.${NC}"
    fi

    echo -e "${GREEN}Pre-build cleanup completed.${NC}"
else
    echo -e "${YELLOW}Skipping image cleanup (--no-clean specified).${NC}"
fi

# Step 3: Build containers (unless skipped)
if [ "$SKIP_BUILD" = false ]; then
    echo -e "${YELLOW}Building Go binary for $DOCKER_PLATFORM (GOOS=linux GOARCH=$GO_ARCH)...${NC}"

    # Check if Go is installed
    if ! command -v go &> /dev/null; then
        echo -e "${RED}Go is not installed. Please install Go to build the binary.${NC}"
        echo -e "${YELLOW}Installation instructions:${NC}"
        echo -e "  - Visit: https://golang.org/dl/"
        echo -e "  - Or use package manager:"
        echo -e "    macOS: brew install go"
        echo -e "    Ubuntu: sudo apt install golang-go"
        echo -e "    CentOS: sudo yum install golang"
        exit 1
    fi

    # Show Go version for debugging
    GO_VERSION=$(go version)
    echo -e "${BLUE}Using Go: $GO_VERSION${NC}"

    # Build Go binary for target architecture
    echo -e "${BLUE}Building with: GOOS=linux GOARCH=$GO_ARCH${NC}"
    GOOS=linux GOARCH=$GO_ARCH go build -ldflags="-w -s" -o backup-server .

    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to build Go binary.${NC}"
        echo -e "${YELLOW}Troubleshooting:${NC}"
        echo -e "  1. Check if all dependencies are available: go mod tidy"
        echo -e "  2. Verify Go modules: go mod download"
        echo -e "  3. Check for compilation errors above"
        exit 1
    fi

    # Verify the binary was created and is executable
    if [ ! -f "backup-server" ]; then
        echo -e "${RED}Binary file 'backup-server' was not created.${NC}"
        exit 1
    fi

    # Check binary architecture
    if command -v file &> /dev/null; then
        BINARY_INFO=$(file backup-server)
        echo -e "${GREEN}Successfully built Go binary: $BINARY_INFO${NC}"
    else
        echo -e "${GREEN}Successfully built Go binary for $GO_ARCH.${NC}"
    fi

    # Make sure binary is executable
    chmod +x backup-server

    echo -e "${YELLOW}Building Docker containers for $DOCKER_PLATFORM...${NC}"

    # Use docker-compose build
    if command -v docker-compose &> /dev/null; then
        echo -e "${BLUE}Using docker-compose build...${NC}"
        DOCKER_DEFAULT_PLATFORM=$DOCKER_PLATFORM docker-compose build \
            --build-arg TARGETPLATFORM=$DOCKER_PLATFORM \
            --build-arg TARGETOS=linux \
            --build-arg TARGETARCH=$GO_ARCH
    else
        echo -e "${RED}docker-compose is not available.${NC}"
        exit 1
    fi

    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to build containers.${NC}"
        echo -e "${YELLOW}Troubleshooting:${NC}"
        echo -e "  1. Check Docker daemon is running: docker info"
        echo -e "  2. Check Dockerfile syntax"
        echo -e "  3. Check available disk space"
        echo -e "  4. Verify backup-server binary exists and is executable"
        exit 1
    fi
    echo -e "${GREEN}Successfully built containers for $DOCKER_PLATFORM.${NC}"

    # Conditional post-build cleanup
    if [ "$FORCE_CLEAN" = true ]; then
        echo -e "${YELLOW}Performing comprehensive Docker cleanup...${NC}"

        # Clean up dangling images
        echo -e "${BLUE}  - Cleaning up dangling images...${NC}"
        dangling_images=$(docker images -f "dangling=true" -q)
        if [ ! -z "$dangling_images" ]; then
            docker image prune -f
            if [ $? -eq 0 ]; then
                echo -e "${GREEN}    ✓ Successfully cleaned up dangling images.${NC}"
            else
                echo -e "${YELLOW}    ⚠ Warning: Failed to clean up some dangling images.${NC}"
            fi
        else
            echo -e "${GREEN}    ✓ No dangling images found.${NC}"
        fi

        # Clean up unused build cache
        echo -e "${BLUE}  - Cleaning up build cache...${NC}"
        docker builder prune -f > /dev/null 2>&1
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}    ✓ Successfully cleaned up build cache.${NC}"
        else
            echo -e "${YELLOW}    ⚠ Warning: Failed to clean up build cache.${NC}"
        fi

        # Clean up unused networks (but keep the ones we need)
        echo -e "${BLUE}  - Cleaning up unused networks...${NC}"
        docker network prune -f > /dev/null 2>&1
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}    ✓ Successfully cleaned up unused networks.${NC}"
        else
            echo -e "${YELLOW}    ⚠ Warning: Failed to clean up networks.${NC}"
        fi

        echo -e "${GREEN}Post-build cleanup completed.${NC}"
    else
        echo -e "${YELLOW}Skipping post-build cleanup (--no-clean specified).${NC}"
    fi
else
    echo -e "${YELLOW}Skipping build step as requested.${NC}"
fi

# Step 4: Pre-deployment verification
echo -e "${YELLOW}Verifying deployment prerequisites...${NC}"

# Check if backup-server binary exists (should be created by build step)
if [ ! -f "backup-server" ]; then
    echo -e "${RED}Error: backup-server binary not found!${NC}"
    echo -e "${YELLOW}This usually means:${NC}"
    echo -e "  1. Go build failed (check error messages above)"
    echo -e "  2. You're running with --skip-build but no binary exists"
    echo -e "  3. Binary was built for wrong architecture"
    echo -e "\n${YELLOW}Solutions:${NC}"
    echo -e "  - Run without --skip-build to build the binary"
    echo -e "  - Check Go installation: go version"
    echo -e "  - Manually build: GOOS=linux GOARCH=$GO_ARCH go build -o backup-server ."
    exit 1
fi

# Check binary architecture and permissions
if command -v file &> /dev/null; then
    BINARY_INFO=$(file backup-server)
    echo -e "${GREEN}✓ Binary found: $BINARY_INFO${NC}"
else
    echo -e "${GREEN}✓ Binary file exists: backup-server${NC}"
fi

# Check if binary is executable
if [ ! -x "backup-server" ]; then
    echo -e "${YELLOW}Making binary executable...${NC}"
    chmod +x backup-server
fi

# Check essential files for Docker build
REQUIRED_FILES=("config.yaml" "docker-compose.yml" "Dockerfile" "go.mod")
for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo -e "${RED}Error: Required file '$file' not found!${NC}"
        exit 1
    else
        echo -e "${GREEN}✓ Found: $file${NC}"
    fi
done

echo -e "${GREEN}All prerequisites verified.${NC}"

# Step 5: Start containers in detached mode
echo -e "${YELLOW}Starting backup server container...${NC}"
docker-compose up -d
if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to start container.${NC}"
    echo -e "${YELLOW}Troubleshooting:${NC}"
    echo -e "  1. Check Docker daemon is running: docker info"
    echo -e "  2. Check docker-compose.yml syntax"
    echo -e "  3. Check port 8080 is not in use: lsof -i :8080"
    echo -e "  4. Check container logs: docker-compose logs"
    exit 1
fi

echo -e "${GREEN}Deployment completed successfully!${NC}"
echo -e "Services:"
echo -e "  - Backup server: ${BLUE}http://localhost:8080${NC}"
echo -e "  - Web interface: ${BLUE}http://localhost:8080${NC}"

# Show container status
echo -e "${YELLOW}Container status:${NC}"
docker-compose ps

# Display container hash for log inspection
echo -e "\n${YELLOW}Container hash for log inspection:${NC}"
container_id=$(docker-compose ps -q backup-server)
if [ ! -z "$container_id" ]; then
    echo -e "backup-server: ${container_id}"
    echo -e "  - Check logs with: ${GREEN}docker logs ${container_id}${NC}"
    echo -e "  - Follow logs with: ${GREEN}docker logs -f ${container_id}${NC}"
else
    echo -e "backup-server: ${RED}Not running${NC}"
fi

echo -e "\n${GREEN}To check logs, run: docker-compose logs -f${NC}"

# Follow logs if requested
if [ "$FOLLOW_LOGS" = true ]; then
    echo -e "\n${YELLOW}Following logs (Press Ctrl+C to exit)...${NC}"
    docker-compose logs -f
fi
