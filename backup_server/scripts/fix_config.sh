#!/bin/bash

# Script to fix configuration issues in backup_server

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Backup Server Configuration Fix ===${NC}"

# Check if config.yaml exists
if [ ! -f "config.yaml" ]; then
    echo -e "${RED}Error: config.yaml not found in current directory${NC}"
    exit 1
fi

echo -e "${YELLOW}Current backend URL in config.yaml:${NC}"
grep -A1 "backend:" config.yaml | grep "url:" | sed 's/^/  /'

# Fix backend URL
echo -e "${YELLOW}Updating backend URL to https://api.caby.care...${NC}"
sed -i.bak 's|url: "http://127.0.0.1:5678".*|url: "https://api.caby.care"  # 生产环境API地址|g' config.yaml
sed -i.bak 's|url: "http://host.docker.internal:5678".*|url: "https://api.caby.care"  # 生产环境API地址|g' config.yaml

# Fix proxy configuration
echo -e "${YELLOW}Disabling proxy configuration...${NC}"
sed -i.bak 's|http_proxy: "http://.*"|http_proxy: ""|g' config.yaml
sed -i.bak 's|https_proxy: "http://.*"|https_proxy: ""|g' config.yaml
sed -i.bak 's|all_proxy: "socks5://.*"|all_proxy: ""|g' config.yaml

echo -e "${YELLOW}Updated backend URL in config.yaml:${NC}"
grep -A1 "backend:" config.yaml | grep "url:" | sed 's/^/  /'

echo -e "${YELLOW}Updated proxy configuration:${NC}"
grep -A3 "proxy:" config.yaml | grep -E "(http_proxy|https_proxy|all_proxy):" | sed 's/^/  /'

# Verify the changes
echo -e "\n${BLUE}=== Configuration Verification ===${NC}"

# Check backend URL
BACKEND_URL=$(grep -A1 "backend:" config.yaml | grep "url:" | sed 's/.*url: "\([^"]*\)".*/\1/')
if [ "$BACKEND_URL" = "https://api.caby.care" ]; then
    echo -e "${GREEN}✓ Backend URL is correctly set to: $BACKEND_URL${NC}"
else
    echo -e "${RED}✗ Backend URL is incorrect: $BACKEND_URL${NC}"
fi

# Check proxy configuration
HTTP_PROXY=$(grep "http_proxy:" config.yaml | sed 's/.*http_proxy: "\([^"]*\)".*/\1/')
if [ -z "$HTTP_PROXY" ]; then
    echo -e "${GREEN}✓ HTTP proxy is disabled${NC}"
else
    echo -e "${YELLOW}⚠ HTTP proxy is still set: $HTTP_PROXY${NC}"
fi

echo -e "\n${GREEN}Configuration fix completed!${NC}"
echo -e "${YELLOW}Next steps:${NC}"
echo -e "  1. Run: ./scripts/quick_deploy.sh"
echo -e "  2. Test: python3 scripts/test.py"
echo -e "  3. Open: http://localhost:8080"
