#!/usr/bin/env python3

"""
Test script to debug video download issues in backup_server
"""

import requests
import json
import sys
import time

# Configuration
BASE_URL = "http://localhost:8080"
API_BASE = f"{BASE_URL}/api"

def test_api_call(endpoint, method="GET", data=None):
    """Test API call and return response"""
    url = f"{API_BASE}{endpoint}"
    print(f"🔍 Testing: {method} {url}")
    
    try:
        if method == "GET":
            response = requests.get(url, timeout=10)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=10)
        else:
            print(f"❌ Unsupported method: {method}")
            return None
            
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"   ✅ Success")
                return result
            except:
                print(f"   ✅ Success (non-JSON response)")
                return response.text
        else:
            print(f"   ❌ Error: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Request failed: {e}")
        return None

def main():
    print("🧪 Backup Server Download Debug Test")
    print("=" * 50)
    
    # Test 1: Server health
    print("\n📋 Test 1: Server Health Check")
    health = test_api_call("/storage/stats")
    if not health:
        print("❌ Server is not responding. Please check if backup_server is running.")
        sys.exit(1)
    
    # Test 2: Get cats list
    print("\n📋 Test 2: Get Cats List")
    cats = test_api_call("/cats")
    if not cats or not cats.get('cats'):
        print("❌ No cats found. Cannot proceed with download test.")
        sys.exit(1)
    
    print(f"   Found {len(cats['cats'])} cats")
    
    # Test 3: Get videos for first cat
    first_cat = cats['cats'][0]
    cat_id = first_cat['id']
    print(f"\n📋 Test 3: Get Videos for Cat {cat_id}")
    
    videos = test_api_call(f"/cats/{cat_id}/videos?page=1&page_size=5")
    if not videos or not videos.get('videos'):
        print("❌ No videos found for this cat.")
        sys.exit(1)
    
    print(f"   Found {len(videos['videos'])} videos")
    
    # Test 4: Analyze video structure
    print("\n📋 Test 4: Analyze Video Structure")
    sample_video = videos['videos'][0]
    
    print("   Sample video info:")
    for key, value in sample_video.items():
        print(f"     {key}: {value}")
    
    # Test 5: Attempt download
    print(f"\n📋 Test 5: Attempt Download")
    video_id = sample_video['video_id']
    
    download_data = {
        "video_ids": [video_id]
    }
    
    download_result = test_api_call("/videos/download", "POST", download_data)
    if not download_result:
        print("❌ Download request failed")
        return
    
    print(f"   Download request submitted: {download_result}")
    
    # Test 6: Monitor download progress
    print(f"\n📋 Test 6: Monitor Download Progress")
    
    for i in range(10):  # Check for 10 seconds
        time.sleep(1)
        tasks = test_api_call("/tasks?status=all&page=1&page_size=10")
        
        if tasks and tasks.get('tasks'):
            latest_task = tasks['tasks'][0]  # Most recent task
            status = latest_task.get('status', 'unknown')
            progress = latest_task.get('progress', 0)
            error = latest_task.get('error', '')
            
            print(f"   Task {latest_task.get('id', 'unknown')}: {status} ({progress}%)")
            
            if error:
                print(f"   ❌ Error: {error}")
                
                # Analyze the error
                if "未找到视频文件" in error:
                    print("\n🔍 Error Analysis: '未找到视频文件'")
                    print("   This error occurs in getVideoFiles() function")
                    print("   Possible causes:")
                    print("   1. MinIO path construction is incorrect")
                    print("   2. Video folder name format mismatch")
                    print("   3. Files don't exist in MinIO at expected path")
                    
                    # Show expected path
                    device_id = sample_video.get('device_id', 'unknown')
                    video_folder = sample_video.get('video_folder', 'unknown')
                    expected_path = f"records/device{device_id}/{video_folder}/"
                    
                    print(f"\n   Expected MinIO path: {expected_path}")
                    print(f"   Device ID: {device_id}")
                    print(f"   Video Folder: {video_folder}")
                    
                    # Check if video_folder format is correct
                    if "_hls" not in video_folder:
                        print("   ⚠️  Video folder doesn't contain '_hls' suffix")
                    
                    if len(video_folder.split("_")) != 3:  # YYYY-MM-DD_HH-MM-SS_hls
                        print("   ⚠️  Video folder format might be incorrect")
                        print("   Expected format: YYYY-MM-DD_HH-MM-SS_hls")
                
                break
            
            if status in ['completed', 'failed', 'cancelled']:
                break
    
    print("\n📋 Test 7: Final Task Status")
    final_tasks = test_api_call("/tasks?status=all&page=1&page_size=5")
    if final_tasks and final_tasks.get('tasks'):
        for task in final_tasks['tasks'][:3]:  # Show last 3 tasks
            print(f"   Task {task.get('id', 'unknown')}: {task.get('status', 'unknown')}")
            if task.get('error'):
                print(f"     Error: {task.get('error')}")
    
    print("\n🎯 Debug Summary:")
    print("   1. Check MinIO connectivity and credentials")
    print("   2. Verify video folder naming convention")
    print("   3. Ensure files exist in MinIO at expected paths")
    print("   4. Check timezone handling in video folder generation")
    
    print(f"\n   MinIO Debug Command:")
    print(f"   docker exec -it backup_server-backup-server-1 /app/scripts/debug_minio.sh")

if __name__ == "__main__":
    main()
