#!/bin/bash

# 视频备份系统测试脚本

set -e

echo "=== 视频备份系统测试 ==="

# 配置
BACKUP_SERVER_URL="http://localhost:8080"
BACKEND_SERVER_URL="http://your-backend-server"  # 需要替换为实际地址
AUTH_TOKEN="FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local url=$1
    local method=${2:-GET}
    local data=${3:-}
    local expected_status=${4:-200}
    
    echo -n "测试 $method $url ... "
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" "$url")
    else
        response=$(curl -s -w "%{http_code}" -X "$method" "$url")
    fi
    
    status_code="${response: -3}"
    body="${response%???}"
    
    if [ "$status_code" -eq "$expected_status" ]; then
        echo -e "${GREEN}✓${NC}"
        return 0
    else
        echo -e "${RED}✗ (状态码: $status_code)${NC}"
        echo "响应: $body"
        return 1
    fi
}

# 检查服务是否运行
check_service() {
    echo "1. 检查backup_server服务状态..."
    
    if ! pgrep -f "backup_server" > /dev/null; then
        echo -e "${RED}错误: backup_server服务未运行${NC}"
        echo "请先启动服务: ./backup_server"
        exit 1
    fi
    
    echo -e "${GREEN}✓ backup_server服务正在运行${NC}"
}

# 测试健康检查
test_health() {
    echo "2. 测试健康检查..."
    test_api "$BACKUP_SERVER_URL/api/health"
}

# 测试后端连接
test_backend_connection() {
    echo "3. 测试后端连接..."
    test_api "$BACKUP_SERVER_URL/api/test"
}

# 测试获取云端视频列表
test_remote_videos() {
    echo "4. 测试获取云端视频列表..."
    test_api "$BACKUP_SERVER_URL/api/videos/remote?page=1&page_size=5"
}

# 测试获取本地视频列表
test_local_videos() {
    echo "5. 测试获取本地视频列表..."
    test_api "$BACKUP_SERVER_URL/api/videos/local?page=1&page_size=5"
}

# 测试获取任务列表
test_tasks() {
    echo "6. 测试获取任务列表..."
    test_api "$BACKUP_SERVER_URL/api/tasks"
}

# 测试存储统计
test_storage_stats() {
    echo "7. 测试存储统计..."
    test_api "$BACKUP_SERVER_URL/api/storage/stats"
}

# 测试Web界面
test_web_interface() {
    echo "8. 测试Web界面..."
    test_api "$BACKUP_SERVER_URL/"
}

# 测试下载功能（模拟）
test_download() {
    echo "9. 测试下载功能（模拟）..."
    
    # 首先获取一个视频ID用于测试
    echo "获取视频列表..."
    response=$(curl -s "$BACKUP_SERVER_URL/api/videos/remote?page=1&page_size=1")
    
    # 检查是否有视频
    if echo "$response" | grep -q '"videos":\[\]'; then
        echo -e "${YELLOW}⚠ 没有可用的视频进行下载测试${NC}"
        return 0
    fi
    
    # 提取第一个视频ID（简单的JSON解析）
    video_id=$(echo "$response" | grep -o '"video_id":"[^"]*"' | head -1 | cut -d'"' -f4)
    
    if [ -n "$video_id" ]; then
        echo "测试下载视频: $video_id"
        download_data="{\"video_ids\":[\"$video_id\"]}"
        test_api "$BACKUP_SERVER_URL/api/videos/download" "POST" "$download_data"
    else
        echo -e "${YELLOW}⚠ 无法提取视频ID进行下载测试${NC}"
    fi
}

# 检查配置文件
check_config() {
    echo "0. 检查配置文件..."
    
    if [ ! -f "config.yaml" ]; then
        echo -e "${RED}错误: 配置文件 config.yaml 不存在${NC}"
        exit 1
    fi
    
    # 检查关键配置项
    if ! grep -q "backend:" config.yaml; then
        echo -e "${RED}错误: 配置文件缺少 backend 配置${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✓ 配置文件检查通过${NC}"
}

# 生成测试报告
generate_report() {
    echo ""
    echo "=== 测试报告 ==="
    echo "测试时间: $(date)"
    echo "backup_server URL: $BACKUP_SERVER_URL"
    echo ""
    
    if [ $failed_tests -eq 0 ]; then
        echo -e "${GREEN}✓ 所有测试通过 ($total_tests/$total_tests)${NC}"
        echo ""
        echo "系统状态良好，可以正常使用！"
        echo ""
        echo "下一步操作："
        echo "1. 打开浏览器访问: $BACKUP_SERVER_URL"
        echo "2. 在Web界面中选择和下载视频"
        echo "3. 监控下载任务进度"
    else
        echo -e "${RED}✗ 测试失败 ($((total_tests - failed_tests))/$total_tests 通过)${NC}"
        echo ""
        echo "请检查以下问题："
        echo "1. 确认backend_server服务正常运行"
        echo "2. 检查网络连接和代理设置"
        echo "3. 验证配置文件中的URL和token"
        echo "4. 查看服务日志了解详细错误信息"
    fi
}

# 主测试流程
main() {
    echo "开始系统测试..."
    echo ""
    
    total_tests=0
    failed_tests=0
    
    # 运行测试
    tests=(
        "check_config"
        "check_service"
        "test_health"
        "test_backend_connection"
        "test_remote_videos"
        "test_local_videos"
        "test_tasks"
        "test_storage_stats"
        "test_web_interface"
        "test_download"
    )
    
    for test in "${tests[@]}"; do
        total_tests=$((total_tests + 1))
        if ! $test; then
            failed_tests=$((failed_tests + 1))
        fi
        echo ""
    done
    
    generate_report
    
    # 返回适当的退出码
    if [ $failed_tests -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "视频备份系统测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -u, --url URL  指定backup_server URL (默认: $BACKUP_SERVER_URL)"
    echo ""
    echo "示例:"
    echo "  $0                           # 使用默认配置运行测试"
    echo "  $0 -u http://localhost:9090  # 指定自定义URL"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -u|--url)
            BACKUP_SERVER_URL="$2"
            shift 2
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 运行主程序
main
