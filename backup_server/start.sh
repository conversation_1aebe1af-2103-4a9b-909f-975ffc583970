#!/bin/bash

# 视频备份系统启动脚本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BINARY_NAME="backup_server"
CONFIG_FILE="config.yaml"
LOG_DIR="logs"
PID_FILE="$LOG_DIR/backup_server.pid"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 显示横幅
show_banner() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "           视频备份系统 (Backup Server)"
    echo "=================================================="
    echo -e "${NC}"
}

# 检查依赖
check_dependencies() {
    echo -e "${YELLOW}检查系统依赖...${NC}"
    
    # 检查Go环境
    if ! command -v go &> /dev/null; then
        echo -e "${RED}错误: 未找到Go环境，请先安装Go 1.21+${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✓ Go环境检查通过${NC}"
    
    # 检查配置文件
    if [ ! -f "$CONFIG_FILE" ]; then
        echo -e "${RED}错误: 配置文件 $CONFIG_FILE 不存在${NC}"
        echo "请复制并编辑配置文件："
        echo "cp config.yaml.example config.yaml"
        exit 1
    fi
    
    echo -e "${GREEN}✓ 配置文件检查通过${NC}"
}

# 编译程序
build_binary() {
    echo -e "${YELLOW}编译程序...${NC}"
    
    if [ ! -f "$BINARY_NAME" ] || [ "main.go" -nt "$BINARY_NAME" ]; then
        echo "正在编译..."
        go mod tidy
        go build -ldflags "-s -w" -o "$BINARY_NAME"
        echo -e "${GREEN}✓ 编译完成${NC}"
    else
        echo -e "${GREEN}✓ 程序已是最新版本${NC}"
    fi
}

# 设置代理环境变量
setup_proxy() {
    echo -e "${YELLOW}设置代理环境...${NC}"
    
    # 从配置文件读取代理设置
    if grep -q "http_proxy:" "$CONFIG_FILE"; then
        HTTP_PROXY=$(grep "http_proxy:" "$CONFIG_FILE" | sed 's/.*http_proxy: *"\([^"]*\)".*/\1/')
        if [ -n "$HTTP_PROXY" ] && [ "$HTTP_PROXY" != "null" ]; then
            export http_proxy="$HTTP_PROXY"
            echo "设置 http_proxy: $HTTP_PROXY"
        fi
    fi
    
    if grep -q "https_proxy:" "$CONFIG_FILE"; then
        HTTPS_PROXY=$(grep "https_proxy:" "$CONFIG_FILE" | sed 's/.*https_proxy: *"\([^"]*\)".*/\1/')
        if [ -n "$HTTPS_PROXY" ] && [ "$HTTPS_PROXY" != "null" ]; then
            export https_proxy="$HTTPS_PROXY"
            echo "设置 https_proxy: $HTTPS_PROXY"
        fi
    fi
    
    if grep -q "all_proxy:" "$CONFIG_FILE"; then
        ALL_PROXY=$(grep "all_proxy:" "$CONFIG_FILE" | sed 's/.*all_proxy: *"\([^"]*\)".*/\1/')
        if [ -n "$ALL_PROXY" ] && [ "$ALL_PROXY" != "null" ]; then
            export all_proxy="$ALL_PROXY"
            echo "设置 all_proxy: $ALL_PROXY"
        fi
    fi
    
    echo -e "${GREEN}✓ 代理环境设置完成${NC}"
}

# 检查端口占用
check_port() {
    local port=$(grep "port:" "$CONFIG_FILE" | sed 's/.*port: *"\([^"]*\)".*/\1/')
    if [ -z "$port" ]; then
        port="8080"  # 默认端口
    fi
    
    echo -e "${YELLOW}检查端口 $port...${NC}"
    
    if lsof -i ":$port" &> /dev/null; then
        echo -e "${RED}错误: 端口 $port 已被占用${NC}"
        echo "请检查是否有其他服务在使用该端口，或修改配置文件中的端口设置"
        lsof -i ":$port"
        exit 1
    fi
    
    echo -e "${GREEN}✓ 端口 $port 可用${NC}"
}

# 启动服务
start_service() {
    echo -e "${YELLOW}启动服务...${NC}"
    
    # 检查是否已经在运行
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo -e "${YELLOW}服务已在运行 (PID: $pid)${NC}"
            return 0
        else
            rm -f "$PID_FILE"
        fi
    fi
    
    # 启动服务
    echo "启动 backup_server..."
    nohup "./$BINARY_NAME" > "$LOG_DIR/backup_server.log" 2>&1 &
    local pid=$!
    echo $pid > "$PID_FILE"
    
    # 等待服务启动
    sleep 2
    
    # 检查服务是否成功启动
    if ps -p "$pid" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 服务启动成功 (PID: $pid)${NC}"
        
        # 获取服务地址
        local host=$(grep "host:" "$CONFIG_FILE" | sed 's/.*host: *"\([^"]*\)".*/\1/')
        local port=$(grep "port:" "$CONFIG_FILE" | sed 's/.*port: *"\([^"]*\)".*/\1/')
        if [ -z "$host" ]; then host="localhost"; fi
        if [ -z "$port" ]; then port="8080"; fi
        
        echo ""
        echo -e "${GREEN}🎉 视频备份系统启动成功！${NC}"
        echo ""
        echo "服务信息："
        echo "  - Web界面: http://$host:$port"
        echo "  - API地址: http://$host:$port/api"
        echo "  - 进程ID: $pid"
        echo "  - 日志文件: $LOG_DIR/backup_server.log"
        echo ""
        echo "下一步操作："
        echo "  1. 打开浏览器访问 Web 界面"
        echo "  2. 运行测试脚本: ./test_system.sh"
        echo "  3. 查看日志: tail -f $LOG_DIR/backup_server.log"
        echo "  4. 停止服务: ./stop.sh"
        
    else
        echo -e "${RED}✗ 服务启动失败${NC}"
        echo "请查看日志文件了解详细错误信息："
        echo "tail -f $LOG_DIR/backup_server.log"
        rm -f "$PID_FILE"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "视频备份系统启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -f, --force    强制重新编译"
    echo "  -d, --dev      开发模式（显示详细日志）"
    echo ""
    echo "示例:"
    echo "  $0             # 正常启动"
    echo "  $0 -f          # 强制重新编译后启动"
    echo "  $0 -d          # 开发模式启动"
}

# 开发模式启动
dev_mode() {
    echo -e "${YELLOW}开发模式启动...${NC}"
    
    # 设置环境变量
    export GIN_MODE=debug
    
    # 直接运行，不后台化
    echo "启动服务（开发模式）..."
    "./$BINARY_NAME"
}

# 主函数
main() {
    local force_build=false
    local dev_mode=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--force)
                force_build=true
                shift
                ;;
            -d|--dev)
                dev_mode=true
                shift
                ;;
            *)
                echo "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    show_banner
    
    # 执行启动流程
    check_dependencies
    
    if [ "$force_build" = true ]; then
        rm -f "$BINARY_NAME"
    fi
    
    build_binary
    setup_proxy
    
    if [ "$dev_mode" = true ]; then
        dev_mode
    else
        check_port
        start_service
    fi
}

# 运行主函数
main "$@"
