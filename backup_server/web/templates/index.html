<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .stat-card .value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .tabs {
            display: flex;
            background: white;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .tab.active {
            border-bottom-color: #3498db;
            background: #f8f9fa;
        }
        
        .tab-content {
            background: white;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            min-height: 400px;
        }
        
        .tab-pane {
            display: none;
            padding: 20px;
        }
        
        .tab-pane.active {
            display: block;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
        }
        
        .video-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s;
        }

        .video-card.local {
            font-size: 12px;
            padding: 12px;
        }

        .video-card.local .video-info {
            line-height: 1.4;
        }

        .video-card.local .video-info strong {
            font-size: 11px;
            color: #495057;
        }

        .cat-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .cat-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
        }

        .cat-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .cat-card.selected {
            border-color: #3498db;
            background: #e3f2fd;
        }

        .cat-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 10px;
            background: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #666;
        }

        .cat-name {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .cat-stats {
            font-size: 12px;
            color: #7f8c8d;
        }

        .cat-info-header {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .cat-info-header h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }

        /* 左右布局样式 */
        .split-layout {
            display: flex;
            gap: 20px;
            height: 600px;
        }

        .left-panel {
            width: 300px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background: white;
            overflow-y: auto;
        }

        .right-panel {
            flex: 1;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background: white;
            display: flex;
            flex-direction: column;
        }

        .cat-list-sidebar {
            max-height: 100%;
            overflow-y: auto;
        }

        .cat-item {
            padding: 10px;
            margin-bottom: 8px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            background: #f8f9fa;
        }

        .cat-item:hover {
            background: #e3f2fd;
            border-color: #3498db;
        }

        .cat-item.active {
            background: #3498db;
            color: white;
            border-color: #2980b9;
        }

        .cat-item-name {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .cat-item-stats {
            font-size: 12px;
            color: #666;
        }

        .cat-item.active .cat-item-stats {
            color: #e3f2fd;
        }

        .video-list {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 15px;
        }

        .video-item {
            padding: 8px 12px;
            margin-bottom: 4px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            background: #f8f9fa;
            font-family: monospace;
            font-size: 13px;
        }

        .video-item:hover {
            background: #e3f2fd;
            border-color: #3498db;
        }

        .video-item.selected {
            background: #3498db;
            color: white;
            border-color: #2980b9;
        }

        .video-item.local-exists {
            background: #f8f9fa;
            color: #6c757d;
            border-color: #dee2e6;
            opacity: 0.7;
        }

        .video-item.local-exists.selected {
            background: #6c757d;
            color: white;
            border-color: #495057;
        }

        /* 任务状态侧边栏样式 */
        .task-status-sidebar {
            max-height: 600px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }

        .task-status-item {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .task-status-item:hover {
            background: #f8f9fa;
        }

        .task-status-item.active {
            background: #3498db;
            color: white;
        }

        .task-status-item.active:hover {
            background: #2980b9;
        }

        .task-status-name {
            font-weight: 500;
        }

        .task-status-count {
            background: rgba(0,0,0,0.1);
            color: inherit;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            min-width: 20px;
            text-align: center;
        }

        .task-status-item.active .task-status-count {
            background: rgba(255,255,255,0.2);
        }

        /* 任务详情列表样式 */
        .task-detail-list {
            max-height: 600px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }

        .task-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s;
        }

        .task-item:hover {
            background: #f8f9fa;
        }

        .task-item:last-child {
            border-bottom: none;
        }

        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .task-id {
            font-family: monospace;
            font-size: 14px;
            color: #666;
        }

        .task-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .task-status.pending {
            background: #ffeaa7;
            color: #d63031;
        }

        .task-status.running {
            background: #74b9ff;
            color: white;
        }

        .task-status.completed {
            background: #00b894;
            color: white;
        }

        .task-status.failed {
            background: #e17055;
            color: white;
        }

        .task-status.paused {
            background: #a29bfe;
            color: white;
        }

        .task-status.converting {
            background: #fd79a8;
            color: white;
        }

        .task-status.converted {
            background: #00cec9;
            color: white;
        }

        /* 转换状态标识 */
        .video-conversion-status {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 500;
            margin-left: 8px;
        }

        .video-conversion-status.converted {
            background: #00cec9;
            color: white;
        }

        .video-conversion-status.not-converted {
            background: #ddd;
            color: #666;
        }

        .video-conversion-status.converting {
            background: #fd79a8;
            color: white;
        }

        .task-info {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }

        .task-progress {
            width: 100%;
            height: 6px;
            background: #ecf0f1;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .task-progress-bar {
            height: 100%;
            background: #3498db;
            transition: width 0.3s ease;
        }

        .task-progress-bar.completed {
            background: #00b894;
        }

        .task-progress-bar.failed {
            background: #e17055;
        }

        .task-actions {
            display: flex;
            gap: 8px;
        }

        .task-actions .btn {
            padding: 4px 8px;
            font-size: 12px;
        }

        /* 主要操作按钮区域样式 */
        .primary-action-bar {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary-large {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            font-weight: 600;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
            text-transform: none;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }

        .btn-primary-large:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
            background: linear-gradient(135deg, #ff5252 0%, #d63031 100%);
        }

        .btn-primary-large:active {
            transform: translateY(0);
            box-shadow: 0 2px 10px rgba(255, 107, 107, 0.4);
        }

        .btn-primary-large:disabled {
            background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            opacity: 0.6;
        }

        .btn-primary-large:disabled:hover {
            transform: none;
            box-shadow: none;
        }

        /* 按钮图标样式 */
        .btn-primary-large .icon-download {
            display: inline-block;
            width: 20px;
            height: 20px;
            margin-right: 8px;
            vertical-align: middle;
        }

        .btn-primary-large .icon-download::before {
            content: "⬇";
            font-size: 16px;
            font-weight: bold;
        }

        /* 按钮动画效果 */
        .btn-primary-large::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary-large:hover::before {
            left: 100%;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .btn-primary-large {
                padding: 12px 20px;
                font-size: 16px;
            }

            .primary-action-bar {
                padding: 10px;
                margin-bottom: 15px;
            }
        }

        .panel-header {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }

        .panel-header h4 {
            margin: 0;
            color: #2c3e50;
            font-size: 16px;
        }

        .video-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .video-controls .btn {
            font-size: 12px;
            padding: 6px 12px;
        }
        
        .video-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .video-card.selected {
            border-color: #3498db;
            background: #e3f2fd;
        }
        
        .video-info {
            margin-bottom: 10px;
        }
        
        .video-info strong {
            color: #2c3e50;
        }
        
        .task-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .task-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .task-info {
            flex: 1;
        }
        
        .task-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-pending { background: #f39c12; color: white; }
        .status-downloading { background: #3498db; color: white; }
        .status-completed { background: #27ae60; color: white; }
        .status-failed { background: #e74c3c; color: white; }
        .status-cancelled { background: #95a5a6; color: white; }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #ecf0f1;
            border-radius: 3px;
            margin: 5px 0;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: #3498db;
            transition: width 0.3s;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{.title}}</h1>
            <p>管理和下载猫砂盆视频数据</p>
        </div>
        
        <div class="stats" id="stats">
            <div class="stat-card">
                <h3>存储大小</h3>
                <div class="value" id="storage-size">-</div>
            </div>
            <div class="stat-card">
                <h3>设备数量</h3>
                <div class="value" id="device-count">-</div>
            </div>
            <div class="stat-card">
                <h3>猫咪数量</h3>
                <div class="value" id="cat-count">-</div>
            </div>
        </div>
        
        <div class="tabs">
            <div class="tab active" onclick="switchTab('remote')">云端视频</div>
            <div class="tab" onclick="switchTab('local')">本地视频</div>
            <div class="tab" onclick="switchTab('cats')">按猫展示</div>
            <div class="tab" onclick="switchTab('tasks')">下载任务</div>
            <div class="tab" onclick="switchTab('conversions')">转换任务</div>
        </div>
        
        <div class="tab-content">
            <!-- 云端视频 -->
            <div class="tab-pane active" id="remote-pane">
                <div class="controls">
                    <button class="btn btn-primary" onclick="loadRemoteVideos()">刷新列表</button>
                    <button class="btn btn-success" onclick="downloadSelected()" id="download-btn" disabled>下载选中</button>
                    <button class="btn btn-primary" onclick="selectAll()">全选</button>
                    <button class="btn btn-primary" onclick="clearSelection()">清空选择</button>
                </div>
                <div id="remote-videos" class="video-grid"></div>
                <div class="pagination" id="remote-pagination"></div>
            </div>
            
            <!-- 本地视频 -->
            <div class="tab-pane" id="local-pane">
                <div class="controls" style="margin-bottom: 15px;">
                    <button class="btn btn-primary" onclick="loadLocalCats()">刷新猫咪列表</button>
                    <button class="btn btn-danger" onclick="cleanupStorage()">清理存储</button>
                </div>

                <div class="split-layout">
                    <!-- 左侧猫咪列表 -->
                    <div class="left-panel">
                        <div class="panel-header">
                            <h4>选择猫咪</h4>
                        </div>
                        <div id="local-cat-list" class="cat-list-sidebar">
                            <div class="loading">加载中...</div>
                        </div>
                    </div>

                    <!-- 右侧视频列表 -->
                    <div class="right-panel">
                        <div class="panel-header">
                            <h4 id="local-video-title">请选择猫咪</h4>
                        </div>

                        <div class="video-controls">
                            <!-- 转换状态筛选 -->
                            <select id="conversion-status-filter" onchange="filterByConversionStatus()">
                                <option value="all">所有视频</option>
                                <option value="converted">已转换</option>
                                <option value="not_converted">未转换</option>
                            </select>
                            <button class="btn btn-success" onclick="convertSelectedVideos()" id="convert-videos-btn" disabled>转换选中视频</button>
                            <button class="btn btn-warning" onclick="convertAllVideos()" id="convert-all-btn">转换所有未转换视频</button>

                            <!-- 日期搜索 -->
                            <input type="date" id="local-start-date" placeholder="开始日期">
                            <input type="date" id="local-end-date" placeholder="结束日期">
                            <button class="btn btn-primary" onclick="filterLocalVideosByDate()">按日期搜索</button>
                            <button class="btn btn-primary" onclick="clearLocalDateFilter()">清除搜索</button>
                        </div>

                        <div id="local-video-list" class="video-list">
                            <div class="loading">请先选择猫咪</div>
                        </div>

                        <div class="pagination" id="local-video-pagination"></div>
                    </div>
                </div>
            </div>

            <!-- 按猫展示 -->
            <div class="tab-pane" id="cats-pane">
                <div class="controls" style="margin-bottom: 15px;">
                    <button class="btn btn-primary" onclick="loadCatList()">刷新猫咪列表</button>
                </div>

                <div class="split-layout">
                    <!-- 左侧猫咪列表 -->
                    <div class="left-panel">
                        <div class="panel-header">
                            <h4>选择猫咪</h4>
                        </div>
                        <div id="cat-list-sidebar" class="cat-list-sidebar">
                            <div class="loading">加载中...</div>
                        </div>
                    </div>

                    <!-- 右侧视频列表 -->
                    <div class="right-panel">
                        <div class="panel-header">
                            <h4 id="cat-video-title">请选择猫咪</h4>
                        </div>

                        <!-- 主要操作按钮 -->
                        <div class="primary-action-bar">
                            <button class="btn btn-primary-large" onclick="downloadAllNewCatVideos()" id="download-all-new-btn" disabled>
                                <i class="icon-download"></i>
                                下载该猫所有新视频
                            </button>
                        </div>

                        <!-- 其他控制按钮 -->
                        <div class="video-controls">
                            <button class="btn btn-success" onclick="downloadSelectedCatVideos()" id="download-cat-videos-btn" disabled>下载选中视频</button>
                            <button class="btn btn-primary" onclick="selectAllCatVideos()">全选</button>
                            <button class="btn btn-primary" onclick="clearCatVideoSelection()">清空选择</button>

                            <!-- 本地视频过滤 -->
                            <label style="margin-left: 15px;">
                                <input type="checkbox" id="show-local-videos" onchange="toggleLocalVideoFilter()">
                                显示本地已有视频
                            </label>

                            <!-- 日期筛选 -->
                            <input type="date" id="cat-start-date" placeholder="开始日期">
                            <input type="date" id="cat-end-date" placeholder="结束日期">
                            <button class="btn btn-primary" onclick="filterCatVideosByDate()">按日期筛选</button>
                            <button class="btn btn-primary" onclick="clearCatDateFilter()">清除筛选</button>
                        </div>

                        <div id="cat-video-list" class="video-list">
                            <div class="loading">请先选择猫咪</div>
                        </div>

                        <div class="pagination" id="cat-video-pagination"></div>
                    </div>
                </div>
            </div>
            
            <!-- 下载任务 -->
            <div class="tab-pane" id="tasks-pane">
                <div class="controls" style="margin-bottom: 15px;">
                    <button class="btn btn-primary" onclick="loadTasks()">刷新任务</button>
                    <button class="btn btn-danger" onclick="clearCompletedTasks()">清理已完成</button>
                    <button class="btn btn-warning" onclick="retryFailedTasks()">重试失败任务</button>
                    <button class="btn btn-info" onclick="pauseAllTasks()">暂停所有任务</button>
                    <button class="btn btn-success" onclick="resumeAllTasks()">恢复所有任务</button>
                </div>

                <div class="split-layout">
                    <!-- 左侧任务状态分类 -->
                    <div class="left-panel">
                        <div class="panel-header">
                            <h4>任务状态</h4>
                        </div>
                        <div id="task-status-list" class="task-status-sidebar">
                            <div class="loading">加载中...</div>
                        </div>
                    </div>

                    <!-- 右侧任务详情 -->
                    <div class="right-panel">
                        <div class="panel-header">
                            <h4 id="task-detail-title">所有任务</h4>
                        </div>

                        <div id="task-list" class="task-detail-list">
                            <div class="loading">请选择任务状态</div>
                        </div>

                        <div class="pagination" id="task-pagination"></div>
                    </div>
                </div>
            </div>

            <!-- 转换任务 -->
            <div class="tab-pane" id="conversions-pane">
                <div class="controls" style="margin-bottom: 15px;">
                    <button class="btn btn-primary" onclick="loadConversionTasks()">刷新任务</button>
                    <button class="btn btn-danger" onclick="clearCompletedConversions()">清理已完成</button>
                    <button class="btn btn-warning" onclick="retryFailedConversions()">重试失败任务</button>
                    <button class="btn btn-info" onclick="pauseAllConversions()">暂停所有任务</button>
                    <button class="btn btn-success" onclick="resumeAllConversions()">恢复所有任务</button>
                </div>

                <div class="split-layout">
                    <!-- 左侧转换任务状态分类 -->
                    <div class="left-panel">
                        <div class="panel-header">
                            <h4>转换状态</h4>
                        </div>
                        <div id="conversion-status-list" class="task-status-sidebar">
                            <div class="loading">加载中...</div>
                        </div>
                    </div>

                    <!-- 右侧转换任务详情 -->
                    <div class="right-panel">
                        <div class="panel-header">
                            <h4 id="conversion-detail-title">所有转换任务</h4>
                        </div>

                        <div id="conversion-task-list" class="task-detail-list">
                            <div class="loading">请选择转换状态</div>
                        </div>

                        <div class="pagination" id="conversion-pagination"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let selectedVideos = new Set(); // 云端视频选中状态
        let currentPage = 1;
        let pageSize = 20;

        // 本地视频相关变量
        let currentLocalCat = null;
        let localVideoPage = 1;
        let localVideoPageSize = 20;
        let selectedLocalVideos = new Set(); // 本地视频选中状态（用于转换）
        let currentConversionFilter = 'all'; // 转换状态筛选

        // 按猫展示相关变量
        let selectedCatVideos = new Set(); // 猫咪视频选中状态
        let currentCat = null;
        let currentCatName = null; // 当前猫咪名称
        let catVideosPage = 1;
        let catVideosPageSize = 20;
        let showLocalVideos = false; // 是否显示本地已有视频，默认不显示
        let localVideoIds = new Set(); // 本地视频ID集合，用于过滤
        let allCatVideos = []; // 当前猫咪的所有视频（用于批量下载）

        // 任务管理相关变量
        let currentTaskStatus = 'all'; // 当前选中的任务状态
        let taskPage = 1;
        let taskPageSize = 20;
        let taskStatusCounts = {}; // 各状态任务数量统计

        // 转换任务相关变量
        let currentConversionStatus = 'all'; // 当前选中的转换任务状态
        let conversionPage = 1;
        let conversionPageSize = 20;
        let conversionStatusCounts = {}; // 各转换状态任务数量统计

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadRemoteVideos();

            // 定时刷新任务状态
            setInterval(loadTasks, 5000);
        });

        // 获取标签显示名称
        function getTabDisplayName(tabName) {
            const tabNames = {
                'remote': '云端视频',
                'local': '本地视频',
                'cats': '按猫展示',
                'tasks': '下载任务',
                'conversions': '转换任务'
            };
            return tabNames[tabName] || tabName;
        }

        // 切换标签页
        function switchTab(tabName) {
            // 更新标签样式
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));

            // 找到对应的标签并激活
            document.querySelectorAll('.tab').forEach(tab => {
                if (tab.textContent.includes(getTabDisplayName(tabName))) {
                    tab.classList.add('active');
                }
            });
            document.getElementById(tabName + '-pane').classList.add('active');

            // 加载对应数据
            if (tabName === 'remote') {
                loadRemoteVideos();
            } else if (tabName === 'local') {
                loadLocalCats();
            } else if (tabName === 'cats') {
                loadCatList();
            } else if (tabName === 'tasks') {
                loadTasks();
            } else if (tabName === 'conversions') {
                loadConversionTasks();
            }
        }

        // 加载统计信息
        async function loadStats() {
            try {
                const response = await fetch('/api/storage/stats');
                const stats = await response.json();

                document.getElementById('storage-size').textContent = formatBytes(stats.total_size || 0);
                document.getElementById('device-count').textContent = stats.device_count || 0;
                document.getElementById('cat-count').textContent = stats.cat_count || 0;
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }

        // 加载云端视频列表
        async function loadRemoteVideos(page = 1) {
            const container = document.getElementById('remote-videos');
            container.innerHTML = '<div class="loading">加载中...</div>';

            try {
                const response = await fetch(`/api/videos/remote?page=${page}&page_size=${pageSize}`);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '加载失败');
                }

                renderVideoGrid(data.videos, container, 'remote');
                renderPagination(data.page, data.total_pages, 'remote-pagination', (page) => loadRemoteVideos(page));

                currentPage = page;
            } catch (error) {
                container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 加载本地视频列表
        async function loadLocalVideos(page = 1) {
            const container = document.getElementById('local-videos');
            container.innerHTML = '<div class="loading">加载中...</div>';

            try {
                const response = await fetch(`/api/videos/local?page=${page}&page_size=${localPageSize}`);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '加载失败');
                }

                renderLocalVideoGrid(data.videos, container);
                renderPagination(data.page, data.total_pages, 'local-pagination', (page) => loadLocalVideos(page));
            } catch (error) {
                container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 渲染视频网格
        function renderVideoGrid(videos, container, type) {
            if (videos.length === 0) {
                container.innerHTML = '<div class="loading">暂无数据</div>';
                return;
            }

            container.innerHTML = videos.map(video => `
                <div class="video-card ${selectedVideos.has(video.video_id) ? 'selected' : ''}"
                     onclick="toggleVideoSelection('${video.video_id}')">
                    <div class="video-info">
                        <strong>视频ID:</strong> ${video.video_id}<br>
                        <strong>设备ID:</strong> ${video.device_id}<br>
                        <strong>猫ID:</strong> ${video.animal_id || '未知'}<br>
                        <strong>开始时间:</strong> ${formatTimestamp(video.start_time)}<br>
                        <strong>行为类型:</strong> ${video.behavior_type}<br>
                        <strong>置信度:</strong> ${(video.cat_confidence * 100).toFixed(1)}%
                    </div>
                </div>
            `).join('');

            updateDownloadButton();
        }

        // 渲染本地视频网格
        function renderLocalVideoGrid(videos, container) {
            if (videos.length === 0) {
                container.innerHTML = '<div class="loading">暂无数据</div>';
                return;
            }

            container.innerHTML = videos.map(video => `
                <div class="video-card local">
                    <div class="video-info">
                        <strong>视频ID:</strong> ${video.video_id}<br>
                        <strong>设备ID:</strong> ${video.device_id}<br>
                        <strong>猫ID:</strong> ${video.animal_id || '未知'}<br>
                        <strong>开始时间:</strong> ${formatTimestamp(video.start_time)}<br>
                        <strong>文件大小:</strong> ${formatBytes(video.file_size)}<br>
                        <strong>文件数量:</strong> ${video.file_count}<br>
                        <strong>下载时间:</strong> ${new Date(video.downloaded_at).toLocaleString()}
                    </div>
                    <button class="btn btn-danger" onclick="deleteVideo('${video.video_id}')" style="font-size: 11px; padding: 6px 12px; margin-top: 8px;">删除</button>
                </div>
            `).join('');
        }

        // 渲染分页
        function renderPagination(currentPage, totalPages, containerId, loadFunction) {
            const container = document.getElementById(containerId);
            if (totalPages <= 1) {
                container.innerHTML = '';
                return;
            }

            // 清除之前的事件监听器
            container.innerHTML = '';

            // 上一页
            if (currentPage > 1) {
                const prevBtn = document.createElement('button');
                prevBtn.className = 'btn btn-primary';
                prevBtn.textContent = '上一页';
                prevBtn.onclick = () => loadFunction(currentPage - 1);
                container.appendChild(prevBtn);
            }

            // 页码
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                const pageBtn = document.createElement('button');
                pageBtn.className = i === currentPage ? 'btn btn-success' : 'btn btn-primary';
                pageBtn.textContent = i;
                pageBtn.onclick = () => loadFunction(i);
                container.appendChild(pageBtn);
            }

            // 下一页
            if (currentPage < totalPages) {
                const nextBtn = document.createElement('button');
                nextBtn.className = 'btn btn-primary';
                nextBtn.textContent = '下一页';
                nextBtn.onclick = () => loadFunction(currentPage + 1);
                container.appendChild(nextBtn);
            }
        }

        // 切换视频选择状态
        function toggleVideoSelection(videoId) {
            if (selectedVideos.has(videoId)) {
                selectedVideos.delete(videoId);
            } else {
                selectedVideos.add(videoId);
            }

            // 更新UI - 通过videoId查找对应的卡片
            const cards = document.querySelectorAll('#remote-videos .video-card');
            cards.forEach(card => {
                const cardVideoId = card.textContent.match(/视频ID:\s*([^\s]+)/);
                if (cardVideoId && cardVideoId[1] === videoId) {
                    card.classList.toggle('selected');
                }
            });
            updateDownloadButton();
        }

        // 全选
        function selectAll() {
            document.querySelectorAll('#remote-videos .video-card').forEach(card => {
                const videoId = card.textContent.match(/视频ID:\s*([^\s]+)/)[1];
                selectedVideos.add(videoId);
                card.classList.add('selected');
            });
            updateDownloadButton();
        }

        // 清空选择
        function clearSelection() {
            selectedVideos.clear();
            document.querySelectorAll('.video-card').forEach(card => {
                card.classList.remove('selected');
            });
            updateDownloadButton();
        }

        // 更新下载按钮状态
        function updateDownloadButton() {
            const btn = document.getElementById('download-btn');
            btn.disabled = selectedVideos.size === 0;
            btn.textContent = `下载选中 (${selectedVideos.size})`;
        }

        // 下载选中的视频
        async function downloadSelected() {
            if (selectedVideos.size === 0) return;

            // 立即显示反馈，禁用按钮
            const btn = document.getElementById('download-btn');
            const originalText = btn.textContent;
            btn.disabled = true;
            btn.textContent = '正在创建下载任务...';

            try {
                const response = await fetch('/api/videos/download', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        video_ids: Array.from(selectedVideos)
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '下载失败');
                }

                // 显示成功消息
                alert(`正在为 ${data.video_count} 个视频创建下载任务，请稍后在"下载任务"标签页查看进度`);
                clearSelection();
                switchTab('tasks');
            } catch (error) {
                alert(`下载失败: ${error.message}`);
            } finally {
                // 恢复按钮状态
                btn.disabled = false;
                btn.textContent = originalText;
            }
        }

        // 加载任务状态统计
        async function loadTasks() {
            const statusContainer = document.getElementById('task-status-list');
            statusContainer.innerHTML = '<div class="loading">加载中...</div>';

            try {
                const response = await fetch('/api/tasks/stats');
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '加载失败');
                }

                taskStatusCounts = data.status_counts || {};
                renderTaskStatusList(data.status_counts, statusContainer);

                // 默认选择"所有任务"
                selectTaskStatus('all');
            } catch (error) {
                statusContainer.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 渲染任务状态列表
        function renderTaskStatusList(statusCounts, container) {
            // 合并running和downloading状态
            const runningCount = (statusCounts.running || 0) + (statusCounts.downloading || 0);

            const statusList = [
                { key: 'all', name: '所有任务', count: Object.values(statusCounts).reduce((a, b) => a + b, 0) },
                { key: 'pending', name: '等待中', count: statusCounts.pending || 0 },
                { key: 'running', name: '进行中', count: runningCount },
                { key: 'completed', name: '已完成', count: statusCounts.completed || 0 },
                { key: 'failed', name: '失败', count: statusCounts.failed || 0 },
                { key: 'paused', name: '已暂停', count: statusCounts.paused || 0 },
                { key: 'cancelled', name: '已取消', count: statusCounts.cancelled || 0 }
            ];

            container.innerHTML = statusList.map(status => `
                <div class="task-status-item ${currentTaskStatus === status.key ? 'active' : ''}"
                     onclick="selectTaskStatus('${status.key}')">
                    <div class="task-status-name">${status.name}</div>
                    <div class="task-status-count">${status.count}</div>
                </div>
            `).join('');
        }

        // 选择任务状态
        function selectTaskStatus(status) {
            currentTaskStatus = status;
            taskPage = 1;

            // 更新状态选中状态
            document.querySelectorAll('#task-status-list .task-status-item').forEach(item => {
                item.classList.remove('active');
            });

            // 找到并激活当前选中的状态
            const statusItems = document.querySelectorAll('#task-status-list .task-status-item');
            statusItems.forEach(item => {
                if (item.onclick.toString().includes(`'${status}'`)) {
                    item.classList.add('active');
                }
            });

            // 更新标题
            const statusNames = {
                'all': '所有任务',
                'pending': '等待中的任务',
                'running': '进行中的任务',
                'completed': '已完成的任务',
                'failed': '失败的任务',
                'paused': '已暂停的任务',
                'cancelled': '已取消的任务'
            };
            document.getElementById('task-detail-title').textContent = statusNames[status] || '任务列表';

            // 加载任务列表
            loadTasksByStatus(status, 1);
        }

        // 按状态加载任务列表
        async function loadTasksByStatus(status, page = 1) {
            const container = document.getElementById('task-list');
            container.innerHTML = '<div class="loading">加载中...</div>';

            try {
                let url = `/api/tasks?page=${page}&page_size=${taskPageSize}`;
                if (status !== 'all') {
                    url += `&status=${status}`;
                }

                const response = await fetch(url);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '加载失败');
                }

                renderTaskDetailList(data.tasks, container);
                renderPagination(data.page, data.total_pages, 'task-pagination', (page) => loadTasksByStatus(status, page));

                taskPage = page;
            } catch (error) {
                container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 渲染任务详情列表
        function renderTaskDetailList(tasks, container) {
            if (tasks.length === 0) {
                container.innerHTML = '<div class="loading">暂无任务</div>';
                return;
            }

            container.innerHTML = tasks.map(task => `
                <div class="task-item">
                    <div class="task-header">
                        <div class="task-id">${task.id}</div>
                        <div class="task-status ${task.status}">${getStatusText(task.status)}</div>
                    </div>
                    <div class="task-info">
                        <strong>视频ID:</strong> ${task.video_info?.video_id || 'N/A'}<br>
                        <strong>设备ID:</strong> ${task.video_info?.device_id || 'N/A'}<br>
                        <strong>创建时间:</strong> ${formatTimestamp(task.created_at)}<br>
                        <strong>进度:</strong> ${(task.progress || 0).toFixed(1)}%<br>
                        ${task.error ? `<strong>错误信息:</strong> <span style="color: #e17055;">${task.error}</span><br>` : ''}
                    </div>
                    <div class="task-progress">
                        <div class="task-progress-bar ${task.status}" style="width: ${task.progress || 0}%"></div>
                    </div>
                    <div class="task-actions">
                        ${getTaskActions(task)}
                    </div>
                </div>
            `).join('');
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '等待中',
                'running': '进行中',
                'downloading': '下载中',
                'completed': '已完成',
                'failed': '失败',
                'cancelled': '已取消',
                'paused': '已暂停'
            };
            return statusMap[status] || status;
        }

        // 获取任务操作按钮
        function getTaskActions(task) {
            const actions = [];

            switch (task.status) {
                case 'pending':
                case 'running':
                case 'downloading':
                    actions.push(`<button class="btn btn-warning" onclick="pauseTask('${task.id}')">暂停</button>`);
                    actions.push(`<button class="btn btn-danger" onclick="cancelTask('${task.id}')">取消</button>`);
                    break;
                case 'paused':
                    actions.push(`<button class="btn btn-success" onclick="resumeTask('${task.id}')">恢复</button>`);
                    actions.push(`<button class="btn btn-danger" onclick="cancelTask('${task.id}')">取消</button>`);
                    break;
                case 'failed':
                    actions.push(`<button class="btn btn-primary" onclick="retryTask('${task.id}')">重试</button>`);
                    actions.push(`<button class="btn btn-danger" onclick="deleteTask('${task.id}')">删除</button>`);
                    break;
                case 'completed':
                case 'cancelled':
                    actions.push(`<button class="btn btn-danger" onclick="deleteTask('${task.id}')">删除</button>`);
                    break;
            }

            return actions.join('');
        }

        // 暂停任务
        async function pauseTask(taskId) {
            try {
                const response = await fetch(`/api/tasks/${taskId}/pause`, {
                    method: 'POST'
                });

                if (!response.ok) {
                    const data = await response.json();
                    throw new Error(data.error || '暂停失败');
                }

                // 刷新当前状态的任务列表
                loadTasksByStatus(currentTaskStatus, taskPage);
                loadTasks(); // 刷新状态统计
            } catch (error) {
                alert(`暂停任务失败: ${error.message}`);
            }
        }

        // 恢复任务
        async function resumeTask(taskId) {
            try {
                const response = await fetch(`/api/tasks/${taskId}/resume`, {
                    method: 'POST'
                });

                if (!response.ok) {
                    const data = await response.json();
                    throw new Error(data.error || '恢复失败');
                }

                // 刷新当前状态的任务列表
                loadTasksByStatus(currentTaskStatus, taskPage);
                loadTasks(); // 刷新状态统计
            } catch (error) {
                alert(`恢复任务失败: ${error.message}`);
            }
        }

        // 重试任务
        async function retryTask(taskId) {
            try {
                const response = await fetch(`/api/tasks/${taskId}/retry`, {
                    method: 'POST'
                });

                if (!response.ok) {
                    const data = await response.json();
                    throw new Error(data.error || '重试失败');
                }

                // 刷新当前状态的任务列表
                loadTasksByStatus(currentTaskStatus, taskPage);
                loadTasks(); // 刷新状态统计
            } catch (error) {
                alert(`重试任务失败: ${error.message}`);
            }
        }

        // 取消任务
        async function cancelTask(taskId) {
            if (!confirm('确定要取消这个任务吗？')) return;

            try {
                const response = await fetch(`/api/tasks/${taskId}/cancel`, {
                    method: 'POST'
                });

                if (!response.ok) {
                    const data = await response.json();
                    throw new Error(data.error || '取消失败');
                }

                // 刷新当前状态的任务列表
                loadTasksByStatus(currentTaskStatus, taskPage);
                loadTasks(); // 刷新状态统计
            } catch (error) {
                alert(`取消任务失败: ${error.message}`);
            }
        }

        // 删除任务
        async function deleteTask(taskId) {
            if (!confirm('确定要删除这个任务吗？')) return;

            try {
                const response = await fetch(`/api/tasks/${taskId}`, {
                    method: 'DELETE'
                });

                if (!response.ok) {
                    const data = await response.json();
                    throw new Error(data.error || '删除失败');
                }

                // 刷新当前状态的任务列表
                loadTasksByStatus(currentTaskStatus, taskPage);
                loadTasks(); // 刷新状态统计
            } catch (error) {
                alert(`删除任务失败: ${error.message}`);
            }
        }

        // 批量操作函数
        async function clearCompletedTasks() {
            if (!confirm('确定要清理所有已完成的任务吗？')) return;

            try {
                const response = await fetch('/api/tasks/clear-completed', {
                    method: 'POST'
                });

                if (!response.ok) {
                    const data = await response.json();
                    throw new Error(data.error || '清理失败');
                }

                const result = await response.json();
                alert(`已清理 ${result.deleted_count} 个已完成任务`);

                // 刷新任务列表
                loadTasks();
                if (currentTaskStatus !== 'all') {
                    loadTasksByStatus(currentTaskStatus, 1);
                }
            } catch (error) {
                alert(`清理失败: ${error.message}`);
            }
        }

        async function retryFailedTasks() {
            if (!confirm('确定要重试所有失败的任务吗？')) return;

            try {
                const response = await fetch('/api/tasks/retry-failed', {
                    method: 'POST'
                });

                if (!response.ok) {
                    const data = await response.json();
                    throw new Error(data.error || '重试失败');
                }

                const result = await response.json();
                alert(`已重试 ${result.retried_count} 个失败任务`);

                // 刷新任务列表
                loadTasks();
                if (currentTaskStatus !== 'all') {
                    loadTasksByStatus(currentTaskStatus, 1);
                }
            } catch (error) {
                alert(`重试失败: ${error.message}`);
            }
        }

        async function pauseAllTasks() {
            if (!confirm('确定要暂停所有正在进行的任务吗？')) return;

            try {
                const response = await fetch('/api/tasks/pause-all', {
                    method: 'POST'
                });

                if (!response.ok) {
                    const data = await response.json();
                    throw new Error(data.error || '暂停失败');
                }

                const result = await response.json();
                alert(`已暂停 ${result.paused_count} 个任务`);

                // 刷新任务列表
                loadTasks();
                if (currentTaskStatus !== 'all') {
                    loadTasksByStatus(currentTaskStatus, 1);
                }
            } catch (error) {
                alert(`暂停失败: ${error.message}`);
            }
        }

        async function resumeAllTasks() {
            if (!confirm('确定要恢复所有暂停的任务吗？')) return;

            try {
                const response = await fetch('/api/tasks/resume-all', {
                    method: 'POST'
                });

                if (!response.ok) {
                    const data = await response.json();
                    throw new Error(data.error || '恢复失败');
                }

                const result = await response.json();
                alert(`已恢复 ${result.resumed_count} 个任务`);

                // 刷新任务列表
                loadTasks();
                if (currentTaskStatus !== 'all') {
                    loadTasksByStatus(currentTaskStatus, 1);
                }
            } catch (error) {
                alert(`恢复失败: ${error.message}`);
            }
        }

        // 删除本地视频
        async function deleteVideo(videoId) {
            if (!confirm('确定要删除这个视频吗？')) return;

            try {
                const response = await fetch(`/api/videos/${videoId}`, {
                    method: 'DELETE'
                });

                if (!response.ok) {
                    const data = await response.json();
                    throw new Error(data.error || '删除失败');
                }

                loadLocalVideos();
                loadStats();
            } catch (error) {
                alert(`删除失败: ${error.message}`);
            }
        }

        // 清理存储
        async function cleanupStorage() {
            try {
                const response = await fetch('/api/storage/cleanup', {
                    method: 'POST'
                });

                if (!response.ok) {
                    const data = await response.json();
                    throw new Error(data.error || '清理失败');
                }

                alert('存储清理完成');
                loadStats();
            } catch (error) {
                alert(`清理失败: ${error.message}`);
            }
        }

        // 工具函数
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatTimestamp(timestamp) {
            if (!timestamp) return '未知';

            // 如果是字符串格式的时间（ISO 8601）
            if (typeof timestamp === 'string') {
                const date = new Date(timestamp);
                return date.toLocaleString();
            }

            // 如果是Unix时间戳（秒）
            if (typeof timestamp === 'number') {
                // 如果是毫秒级时间戳
                if (timestamp > 1000000000000) {
                    return new Date(timestamp).toLocaleString();
                }
                // 如果是秒级时间戳
                return new Date(timestamp * 1000).toLocaleString();
            }

            return '无效时间';
        }

        // ==================== 本地视频相关功能 ====================

        // 加载本地猫咪列表
        async function loadLocalCats() {
            const container = document.getElementById('local-cat-list');
            container.innerHTML = '<div class="loading">加载中...</div>';

            try {
                const response = await fetch('/api/cats');
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '加载失败');
                }

                renderLocalCatList(data.cats, container);
            } catch (error) {
                container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 渲染本地猫咪列表
        function renderLocalCatList(cats, container) {
            if (cats.length === 0) {
                container.innerHTML = '<div class="loading">暂无猫咪数据</div>';
                return;
            }

            container.innerHTML = cats.map(cat => `
                <div class="cat-item" onclick="selectLocalCat('${cat.id}', '${cat.name}')">
                    <div class="cat-item-name">${cat.name}</div>
                    <div class="cat-item-stats">本地视频: 加载中...</div>
                </div>
            `).join('');

            // 异步加载每个猫咪的本地视频数量
            cats.forEach(cat => loadLocalCatVideoCount(cat.id));
        }

        // 加载猫咪的本地视频数量
        async function loadLocalCatVideoCount(catId) {
            try {
                const response = await fetch(`/api/videos/local/cat/${catId}?page=1&page_size=1`);
                const data = await response.json();

                if (response.ok) {
                    // 更新显示
                    const catItems = document.querySelectorAll('.cat-item');
                    catItems.forEach(item => {
                        if (item.onclick.toString().includes(`'${catId}'`)) {
                            const statsDiv = item.querySelector('.cat-item-stats');
                            statsDiv.textContent = `本地视频: ${data.total || 0}`;
                        }
                    });
                }
            } catch (error) {
                console.error(`获取猫咪 ${catId} 的本地视频数量失败:`, error);
            }
        }

        // 选择本地猫咪
        function selectLocalCat(catId, catName) {
            currentLocalCat = catId;
            localVideoPage = 1;

            // 更新猫咪选中状态
            document.querySelectorAll('#local-cat-list .cat-item').forEach(item => {
                item.classList.remove('active');
            });

            // 找到并激活当前选中的猫咪
            const catItems = document.querySelectorAll('#local-cat-list .cat-item');
            catItems.forEach(item => {
                if (item.onclick.toString().includes(`'${catId}'`)) {
                    item.classList.add('active');
                }
            });

            // 更新标题
            document.getElementById('local-video-title').textContent = `${catName} 的本地视频`;

            // 清除日期筛选
            document.getElementById('local-start-date').value = '';
            document.getElementById('local-end-date').value = '';

            // 加载视频列表
            loadLocalCatVideos(catId, 1);
        }

        // 加载本地猫咪视频
        async function loadLocalCatVideos(catId, page = 1) {
            const container = document.getElementById('local-video-list');
            container.innerHTML = '<div class="loading">加载中...</div>';

            try {
                // 构建查询参数
                let url = `/api/videos/local/cat/${catId}?page=${page}&page_size=${localVideoPageSize}`;

                // 添加日期筛选
                const startDate = document.getElementById('local-start-date').value;
                const endDate = document.getElementById('local-end-date').value;

                if (startDate) {
                    const startTime = Math.floor(new Date(startDate).getTime() / 1000);
                    url += `&start_time=${startTime}`;
                }
                if (endDate) {
                    const endTime = Math.floor(new Date(endDate + ' 23:59:59').getTime() / 1000);
                    url += `&end_time=${endTime}`;
                }

                const response = await fetch(url);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '加载失败');
                }

                renderLocalVideoList(data.videos, container);
                renderPagination(data.page, data.total_pages, 'local-video-pagination', (page) => loadLocalCatVideos(catId, page));

                localVideoPage = page;
            } catch (error) {
                container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 渲染本地视频列表
        function renderLocalVideoList(videos, container) {
            if (videos.length === 0) {
                container.innerHTML = '<div class="loading">暂无视频数据</div>';
                return;
            }

            // 根据转换状态筛选
            let filteredVideos = videos;
            if (currentConversionFilter === 'converted') {
                filteredVideos = videos.filter(video => video.is_converted);
            } else if (currentConversionFilter === 'not_converted') {
                filteredVideos = videos.filter(video => !video.is_converted);
            }

            if (filteredVideos.length === 0) {
                const message = currentConversionFilter === 'all' ? '暂无视频数据' :
                               currentConversionFilter === 'converted' ? '暂无已转换视频' : '暂无未转换视频';
                container.innerHTML = `<div class="loading">${message}</div>`;
                return;
            }

            container.innerHTML = filteredVideos.map(video => {
                const conversionStatus = video.is_converted ? 'converted' : 'not-converted';
                const conversionText = video.is_converted ? '已转换' : '未转换';
                const isSelected = selectedLocalVideos.has(video.video_id);

                return `
                    <div class="video-item ${isSelected ? 'selected' : ''}"
                         onclick="toggleLocalVideoSelection('${video.video_id}')">
                        ${video.video_id}
                        <span class="video-conversion-status ${conversionStatus}">${conversionText}</span>
                    </div>
                `;
            }).join('');

            updateConvertButtonsState();
        }

        // 按日期筛选本地视频
        function filterLocalVideosByDate() {
            if (currentLocalCat) {
                loadLocalCatVideos(currentLocalCat, 1);
            }
        }

        // 清除本地视频日期筛选
        function clearLocalDateFilter() {
            document.getElementById('local-start-date').value = '';
            document.getElementById('local-end-date').value = '';
            if (currentLocalCat) {
                loadLocalCatVideos(currentLocalCat, 1);
            }
        }

        // 切换本地视频选择状态
        function toggleLocalVideoSelection(videoId) {
            if (selectedLocalVideos.has(videoId)) {
                selectedLocalVideos.delete(videoId);
            } else {
                selectedLocalVideos.add(videoId);
            }

            // 更新UI
            const items = document.querySelectorAll('#local-video-list .video-item');
            items.forEach(item => {
                if (item.textContent.includes(videoId)) {
                    item.classList.toggle('selected');
                }
            });

            updateConvertButtonsState();
        }

        // 按转换状态筛选
        function filterByConversionStatus() {
            const filter = document.getElementById('conversion-status-filter').value;
            currentConversionFilter = filter;
            selectedLocalVideos.clear();

            if (currentLocalCat) {
                loadLocalCatVideos(currentLocalCat, 1);
            }
        }

        // 更新转换按钮状态
        function updateConvertButtonsState() {
            const convertSelectedBtn = document.getElementById('convert-videos-btn');
            if (convertSelectedBtn) {
                convertSelectedBtn.disabled = selectedLocalVideos.size === 0;
                convertSelectedBtn.textContent = `转换选中视频 (${selectedLocalVideos.size})`;
            }
        }

        // 转换选中的视频
        async function convertSelectedVideos() {
            if (selectedLocalVideos.size === 0) return;

            const confirmed = confirm(`确定要转换选中的 ${selectedLocalVideos.size} 个视频吗？`);
            if (!confirmed) return;

            try {
                const response = await fetch('/api/conversions/convert', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        video_ids: Array.from(selectedLocalVideos)
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '转换失败');
                }

                alert(`已创建 ${data.task_count} 个转换任务，请在"转换任务"标签页查看进度`);
                selectedLocalVideos.clear();
                switchTab('conversions');
            } catch (error) {
                alert(`转换失败: ${error.message}`);
            }
        }

        // 转换所有未转换的视频
        async function convertAllVideos() {
            if (!currentLocalCat) {
                alert('请先选择一只猫咪');
                return;
            }

            const confirmed = confirm('确定要转换该猫咪的所有未转换视频吗？');
            if (!confirmed) return;

            try {
                const response = await fetch('/api/conversions/convert-all', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        cat_id: currentLocalCat
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '转换失败');
                }

                alert(`已创建 ${data.task_count} 个转换任务，请在"转换任务"标签页查看进度`);
                switchTab('conversions');
            } catch (error) {
                alert(`转换失败: ${error.message}`);
            }
        }

        // ==================== 猫咪相关功能 ====================

        // 加载猫咪列表
        async function loadCatList() {
            const container = document.getElementById('cat-list-sidebar');
            container.innerHTML = '<div class="loading">加载中...</div>';

            try {
                // 同时加载猫咪列表和本地视频ID列表
                const [catsResponse, localVideosResponse] = await Promise.all([
                    fetch('/api/cats'),
                    fetch('/api/videos/local/ids')
                ]);

                const catsData = await catsResponse.json();
                if (!catsResponse.ok) {
                    throw new Error(catsData.error || '加载猫咪列表失败');
                }

                // 加载本地视频ID列表
                if (localVideosResponse.ok) {
                    const localVideosData = await localVideosResponse.json();
                    localVideoIds = new Set(localVideosData.video_ids || []);
                } else {
                    console.warn('加载本地视频ID列表失败，将显示所有视频');
                    localVideoIds = new Set();
                }

                renderCatSidebar(catsData.cats, container);
            } catch (error) {
                container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 渲染猫咪侧边栏
        function renderCatSidebar(cats, container) {
            if (cats.length === 0) {
                container.innerHTML = '<div class="loading">暂无猫咪数据</div>';
                return;
            }

            container.innerHTML = cats.map(cat => `
                <div class="cat-item" onclick="selectCat('${cat.id}', '${cat.name}')">
                    <div class="cat-item-name">${cat.name}</div>
                    <div class="cat-item-stats">
                        视频数量: ${cat.video_count}<br>
                        ${cat.last_video_at ? `最后视频: ${formatTimestamp(cat.last_video_at)}` : '暂无视频'}
                    </div>
                </div>
            `).join('');
        }

        // 选择猫咪
        function selectCat(catId, catName) {
            currentCat = catId;
            currentCatName = catName;
            catVideosPage = 1;
            selectedCatVideos.clear();
            allCatVideos = []; // 清空之前的数据

            // 更新猫咪选中状态
            document.querySelectorAll('#cat-list-sidebar .cat-item').forEach(item => {
                item.classList.remove('active');
            });

            // 找到并激活当前选中的猫咪
            const catItems = document.querySelectorAll('#cat-list-sidebar .cat-item');
            catItems.forEach(item => {
                if (item.onclick.toString().includes(`'${catId}'`)) {
                    item.classList.add('active');
                }
            });

            // 更新标题
            document.getElementById('cat-video-title').textContent = `${catName} 的视频`;

            // 启用下载所有新视频按钮
            const downloadAllBtn = document.getElementById('download-all-new-btn');
            if (downloadAllBtn) {
                downloadAllBtn.disabled = false;
                downloadAllBtn.innerHTML = '<i class="icon-download"></i>下载该猫所有新视频';
            }

            // 加载视频列表
            loadCatVideos(catId, 1);
        }

        // 切换猫咪选择状态
        function toggleCatSelection(catId, event) {
            // 确保event对象存在
            if (!event) {
                event = window.event;
            }

            // 阻止事件冒泡，避免触发查看视频
            if (event && event.target && event.target.tagName === 'BUTTON') return;

            if (selectedCats.has(catId)) {
                selectedCats.delete(catId);
            } else {
                selectedCats.add(catId);
            }

            // 更新UI - 通过catId查找对应的卡片
            const cards = document.querySelectorAll('.cat-card');
            cards.forEach(card => {
                const cardOnclick = card.getAttribute('onclick');
                if (cardOnclick && cardOnclick.includes(`'${catId}'`)) {
                    card.classList.toggle('selected');
                }
            });
            updateCatDownloadButton();
        }

        // 查看猫咪视频
        function viewCatVideos(catId, event) {
            // 确保event对象存在
            if (!event) {
                event = window.event;
            }

            if (event && event.stopPropagation) {
                event.stopPropagation(); // 阻止事件冒泡
            }

            currentCat = catId;
            catVideosPage = 1;

            // 隐藏猫咪列表，显示视频详情
            document.getElementById('cat-list').style.display = 'none';
            document.getElementById('cat-videos-detail').style.display = 'block';

            loadCatVideos(catId, 1);
        }

        // 加载猫咪的视频列表
        async function loadCatVideos(catId, page = 1) {
            const container = document.getElementById('cat-video-list');

            container.innerHTML = '<div class="loading">加载中...</div>';

            try {
                // 构建查询参数
                let url = `/api/cats/${catId}/videos?page=${page}&page_size=${catVideosPageSize}`;

                // 添加日期筛选
                const startDate = document.getElementById('cat-start-date').value;
                const endDate = document.getElementById('cat-end-date').value;

                if (startDate) {
                    const startTime = Math.floor(new Date(startDate).getTime() / 1000);
                    url += `&start_time=${startTime}`;
                }
                if (endDate) {
                    const endTime = Math.floor(new Date(endDate + ' 23:59:59').getTime() / 1000);
                    url += `&end_time=${endTime}`;
                }

                const response = await fetch(url);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '加载失败');
                }

                // 收集所有视频数据（用于批量下载）
                if (page === 1) {
                    allCatVideos = [...data.videos]; // 第一页时重置
                } else {
                    allCatVideos = allCatVideos.concat(data.videos); // 后续页面追加
                }

                // 渲染视频列表
                renderCatVideoList(data.videos, container);
                renderPagination(data.page, data.total_pages, 'cat-video-pagination', (page) => loadCatVideos(catId, page));

                catVideosPage = page;
            } catch (error) {
                container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 渲染猫咪视频列表
        function renderCatVideoList(videos, container) {
            if (videos.length === 0) {
                container.innerHTML = '<div class="loading">暂无视频数据</div>';
                return;
            }

            // 根据设置过滤本地视频
            let filteredVideos = videos;
            if (!showLocalVideos) {
                filteredVideos = videos.filter(video => !localVideoIds.has(video.video_id));
            }

            if (filteredVideos.length === 0) {
                const message = showLocalVideos ? '暂无视频数据' : '暂无新视频（所有视频都已在本地）';
                container.innerHTML = `<div class="loading">${message}</div>`;
                return;
            }

            container.innerHTML = filteredVideos.map(video => {
                const isLocal = localVideoIds.has(video.video_id);
                const localClass = isLocal ? 'local-exists' : '';
                const localIndicator = isLocal ? ' 📁' : '';

                return `
                    <div class="video-item ${selectedCatVideos.has(video.video_id) ? 'selected' : ''} ${localClass}"
                         onclick="toggleCatVideoSelection('${video.video_id}')">
                        ${video.video_id}${localIndicator}
                    </div>
                `;
            }).join('');

            updateCatVideoDownloadButton();
        }

        // 切换猫咪视频选择状态（独立于云端视频）
        function toggleCatVideoSelection(videoId) {
            if (selectedCatVideos.has(videoId)) {
                selectedCatVideos.delete(videoId);
            } else {
                selectedCatVideos.add(videoId);
            }

            // 更新UI
            const items = document.querySelectorAll('#cat-video-list .video-item');
            items.forEach(item => {
                if (item.textContent.trim() === videoId) {
                    item.classList.toggle('selected');
                }
            });

            updateCatVideoDownloadButton();
        }

        // 返回猫咪列表
        function backToCatList() {
            document.getElementById('cat-list').style.display = 'block';
            document.getElementById('cat-videos-detail').style.display = 'none';
            currentCat = null;
            selectedCatVideos.clear(); // 清空猫咪视频选中状态

            // 清除日期筛选
            document.getElementById('start-date').value = '';
            document.getElementById('end-date').value = '';
        }

        // 按日期筛选猫咪视频
        function filterCatVideosByDate() {
            if (currentCat) {
                loadCatVideos(currentCat, 1);
            }
        }

        // 清除日期筛选
        function clearCatDateFilter() {
            document.getElementById('cat-start-date').value = '';
            document.getElementById('cat-end-date').value = '';
            if (currentCat) {
                loadCatVideos(currentCat, 1);
            }
        }

        // 切换本地视频显示
        function toggleLocalVideoFilter() {
            const checkbox = document.getElementById('show-local-videos');
            showLocalVideos = checkbox.checked;

            // 重新加载当前猫咪的视频列表
            if (currentCat) {
                loadCatVideos(currentCat, 1);
            }
        }

        // 下载当前猫咪的所有新视频
        async function downloadAllNewCatVideos() {
            if (!currentCat || !currentCatName) {
                alert('请先选择一只猫咪');
                return;
            }

            // 立即显示反馈，禁用按钮
            const btn = document.getElementById('download-all-new-btn');
            const originalHTML = btn.innerHTML;
            btn.disabled = true;
            btn.innerHTML = '<i class="icon-download"></i>正在获取视频列表...';

            try {
                // 获取该猫咪的所有视频（不分页）
                const response = await fetch(`/api/cats/${currentCat}/videos?page=1&page_size=10000`);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '获取视频列表失败');
                }

                // 过滤出本地不存在的视频
                const newVideos = data.videos.filter(video => !localVideoIds.has(video.video_id));

                if (newVideos.length === 0) {
                    alert(`${currentCatName} 的所有视频都已在本地，无需下载`);
                    return;
                }

                // 显示确认对话框
                const confirmed = confirm(
                    `确认下载 ${currentCatName} 的所有新视频吗？\n\n` +
                    `总视频数: ${data.videos.length}\n` +
                    `本地已有: ${data.videos.length - newVideos.length}\n` +
                    `需要下载: ${newVideos.length}\n\n` +
                    `点击确定开始下载，点击取消返回。`
                );

                if (!confirmed) {
                    return;
                }

                btn.innerHTML = '<i class="icon-download"></i>正在创建下载任务...';

                // 提取视频ID列表
                const videoIds = newVideos.map(video => video.video_id);

                // 发起下载请求
                const downloadResponse = await fetch('/api/videos/download', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        video_ids: videoIds
                    })
                });

                const downloadResult = await downloadResponse.json();

                if (!downloadResponse.ok) {
                    throw new Error(downloadResult.error || '下载失败');
                }

                alert(
                    `下载任务创建成功！\n\n` +
                    `猫咪: ${currentCatName}\n` +
                    `视频数量: ${downloadResult.video_count}\n\n` +
                    `请在"下载任务"标签页查看进度`
                );

                // 跳转到下载任务页面
                switchTab('tasks');

            } catch (error) {
                alert(`操作失败: ${error.message}`);
            } finally {
                // 恢复按钮状态
                btn.disabled = false;
                btn.innerHTML = originalHTML;
            }
        }

        // 全选猫咪视频
        function selectAllCatVideos() {
            document.querySelectorAll('#cat-video-list .video-item').forEach(item => {
                const videoId = item.textContent.trim();
                selectedCatVideos.add(videoId);
                item.classList.add('selected');
            });
            updateCatVideoDownloadButton();
        }

        // 清空猫咪视频选择
        function clearCatVideoSelection() {
            selectedCatVideos.clear();
            document.querySelectorAll('#cat-video-list .video-item').forEach(item => {
                item.classList.remove('selected');
            });
            updateCatVideoDownloadButton();
        }

        // 更新猫咪视频下载按钮状态
        function updateCatVideoDownloadButton() {
            const btn = document.getElementById('download-cat-videos-btn');
            if (btn) {
                btn.disabled = selectedCatVideos.size === 0;
                btn.textContent = `下载选中视频 (${selectedCatVideos.size})`;
            }
        }

        // 下载选中的猫咪视频
        async function downloadSelectedCatVideos() {
            if (selectedCatVideos.size === 0) return;

            // 立即显示反馈，禁用按钮
            const btn = document.getElementById('download-cat-videos-btn');
            const originalText = btn.textContent;
            btn.disabled = true;
            btn.textContent = '正在创建下载任务...';

            try {
                const response = await fetch('/api/videos/download', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        video_ids: Array.from(selectedCatVideos)
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '下载失败');
                }

                alert(`正在为 ${data.video_count} 个视频创建下载任务，请稍后在"下载任务"标签页查看进度`);
                clearCatVideoSelection();
                switchTab('tasks');
            } catch (error) {
                alert(`下载失败: ${error.message}`);
            } finally {
                // 恢复按钮状态
                btn.disabled = false;
                btn.textContent = originalText;
            }
        }

        // 下载选中的视频（在猫咪视频详情页面）
        async function downloadSelectedVideos() {
            if (selectedCatVideos.size === 0) return;

            // 立即显示反馈，禁用按钮
            const btn = document.getElementById('download-videos-btn');
            const originalText = btn.textContent;
            btn.disabled = true;
            btn.textContent = '正在创建下载任务...';

            try {
                const response = await fetch('/api/videos/download', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        video_ids: Array.from(selectedCatVideos)
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '下载失败');
                }

                alert(`正在为 ${data.video_count} 个视频创建下载任务，请稍后在"下载任务"标签页查看进度`);
                clearVideoSelection();
                switchTab('tasks');
            } catch (error) {
                alert(`下载失败: ${error.message}`);
            } finally {
                // 恢复按钮状态
                btn.disabled = false;
                btn.textContent = originalText;
            }
        }

        // ==================== 转换任务管理功能 ====================

        // 加载转换任务状态统计
        async function loadConversionTasks() {
            const statusContainer = document.getElementById('conversion-status-list');
            statusContainer.innerHTML = '<div class="loading">加载中...</div>';

            try {
                const response = await fetch('/api/conversions/stats');
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '加载失败');
                }

                conversionStatusCounts = data.status_counts || {};
                renderConversionStatusList(data.status_counts, statusContainer);

                // 默认选择"所有任务"
                selectConversionStatus('all');
            } catch (error) {
                statusContainer.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 渲染转换任务状态列表
        function renderConversionStatusList(statusCounts, container) {
            // 合并running和converting状态
            const convertingCount = (statusCounts.converting || 0) + (statusCounts.running || 0);

            const statusList = [
                { key: 'all', name: '所有任务', count: Object.values(statusCounts).reduce((a, b) => a + b, 0) },
                { key: 'pending', name: '等待中', count: statusCounts.pending || 0 },
                { key: 'converting', name: '转换中', count: convertingCount },
                { key: 'completed', name: '已完成', count: statusCounts.completed || 0 },
                { key: 'failed', name: '失败', count: statusCounts.failed || 0 },
                { key: 'paused', name: '已暂停', count: statusCounts.paused || 0 },
                { key: 'cancelled', name: '已取消', count: statusCounts.cancelled || 0 }
            ];

            container.innerHTML = statusList.map(status => `
                <div class="task-status-item ${currentConversionStatus === status.key ? 'active' : ''}"
                     onclick="selectConversionStatus('${status.key}')">
                    <div class="task-status-name">${status.name}</div>
                    <div class="task-status-count">${status.count}</div>
                </div>
            `).join('');
        }

        // 选择转换任务状态
        function selectConversionStatus(status) {
            currentConversionStatus = status;
            conversionPage = 1;

            // 更新状态选中状态
            document.querySelectorAll('#conversion-status-list .task-status-item').forEach(item => {
                item.classList.remove('active');
            });

            // 找到并激活当前选中的状态
            const statusItems = document.querySelectorAll('#conversion-status-list .task-status-item');
            statusItems.forEach(item => {
                if (item.onclick.toString().includes(`'${status}'`)) {
                    item.classList.add('active');
                }
            });

            // 更新标题
            const statusNames = {
                'all': '所有转换任务',
                'pending': '等待中的转换任务',
                'converting': '转换中的任务',
                'completed': '已完成的转换任务',
                'failed': '失败的转换任务',
                'paused': '已暂停的转换任务',
                'cancelled': '已取消的转换任务'
            };
            document.getElementById('conversion-detail-title').textContent = statusNames[status] || '转换任务列表';

            // 加载转换任务列表
            loadConversionsByStatus(status, 1);
        }

        // 按状态加载转换任务列表
        async function loadConversionsByStatus(status, page = 1) {
            const container = document.getElementById('conversion-task-list');
            container.innerHTML = '<div class="loading">加载中...</div>';

            try {
                let url = `/api/conversions?page=${page}&page_size=${conversionPageSize}`;
                if (status !== 'all') {
                    url += `&status=${status}`;
                }

                const response = await fetch(url);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '加载失败');
                }

                renderConversionDetailList(data.tasks, container);
                renderPagination(data.page, data.total_pages, 'conversion-pagination', (page) => loadConversionsByStatus(status, page));

                conversionPage = page;
            } catch (error) {
                container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 渲染转换任务详情列表
        function renderConversionDetailList(tasks, container) {
            if (tasks.length === 0) {
                container.innerHTML = '<div class="loading">暂无转换任务</div>';
                return;
            }

            container.innerHTML = tasks.map(task => `
                <div class="task-item">
                    <div class="task-header">
                        <div class="task-id">${task.id}</div>
                        <div class="task-status ${task.status}">${getConversionStatusText(task.status)}</div>
                    </div>
                    <div class="task-info">
                        <strong>视频ID:</strong> ${task.video_id || 'N/A'}<br>
                        <strong>输入文件:</strong> ${task.input_path || 'N/A'}<br>
                        <strong>输出文件:</strong> ${task.output_path || 'N/A'}<br>
                        <strong>创建时间:</strong> ${formatTimestamp(task.created_at)}<br>
                        <strong>进度:</strong> ${(task.progress || 0).toFixed(1)}%<br>
                        ${task.error ? `<strong>错误信息:</strong> <span style="color: #e17055;">${task.error}</span><br>` : ''}
                    </div>
                    <div class="task-progress">
                        <div class="task-progress-bar ${task.status}" style="width: ${task.progress || 0}%"></div>
                    </div>
                    <div class="task-actions">
                        ${getConversionTaskActions(task)}
                    </div>
                </div>
            `).join('');
        }

        // 获取转换状态文本
        function getConversionStatusText(status) {
            const statusMap = {
                'pending': '等待中',
                'converting': '转换中',
                'completed': '已完成',
                'failed': '失败',
                'cancelled': '已取消',
                'paused': '已暂停'
            };
            return statusMap[status] || status;
        }

        // 获取转换任务操作按钮
        function getConversionTaskActions(task) {
            const actions = [];

            switch (task.status) {
                case 'pending':
                case 'converting':
                    actions.push(`<button class="btn btn-warning" onclick="pauseConversionTask('${task.id}')">暂停</button>`);
                    actions.push(`<button class="btn btn-danger" onclick="cancelConversionTask('${task.id}')">取消</button>`);
                    break;
                case 'paused':
                    actions.push(`<button class="btn btn-success" onclick="resumeConversionTask('${task.id}')">恢复</button>`);
                    actions.push(`<button class="btn btn-danger" onclick="cancelConversionTask('${task.id}')">取消</button>`);
                    break;
                case 'failed':
                    actions.push(`<button class="btn btn-primary" onclick="retryConversionTask('${task.id}')">重试</button>`);
                    actions.push(`<button class="btn btn-danger" onclick="deleteConversionTask('${task.id}')">删除</button>`);
                    break;
                case 'completed':
                case 'cancelled':
                    actions.push(`<button class="btn btn-danger" onclick="deleteConversionTask('${task.id}')">删除</button>`);
                    break;
            }

            return actions.join('');
        }
    </script>
</body>
</html>
