<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .stat-card .value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .tabs {
            display: flex;
            background: white;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .tab.active {
            border-bottom-color: #3498db;
            background: #f8f9fa;
        }
        
        .tab-content {
            background: white;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            min-height: 400px;
        }
        
        .tab-pane {
            display: none;
            padding: 20px;
        }
        
        .tab-pane.active {
            display: block;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
        }
        
        .video-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s;
        }

        .video-card.local {
            font-size: 12px;
            padding: 12px;
        }

        .video-card.local .video-info {
            line-height: 1.4;
        }

        .video-card.local .video-info strong {
            font-size: 11px;
            color: #495057;
        }

        .cat-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .cat-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
        }

        .cat-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .cat-card.selected {
            border-color: #3498db;
            background: #e3f2fd;
        }

        .cat-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 10px;
            background: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #666;
        }

        .cat-name {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .cat-stats {
            font-size: 12px;
            color: #7f8c8d;
        }

        .cat-info-header {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .cat-info-header h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        
        .video-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .video-card.selected {
            border-color: #3498db;
            background: #e3f2fd;
        }
        
        .video-info {
            margin-bottom: 10px;
        }
        
        .video-info strong {
            color: #2c3e50;
        }
        
        .task-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .task-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .task-info {
            flex: 1;
        }
        
        .task-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-pending { background: #f39c12; color: white; }
        .status-downloading { background: #3498db; color: white; }
        .status-completed { background: #27ae60; color: white; }
        .status-failed { background: #e74c3c; color: white; }
        .status-cancelled { background: #95a5a6; color: white; }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #ecf0f1;
            border-radius: 3px;
            margin: 5px 0;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: #3498db;
            transition: width 0.3s;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{.title}}</h1>
            <p>管理和下载猫砂盆视频数据</p>
        </div>
        
        <div class="stats" id="stats">
            <div class="stat-card">
                <h3>存储大小</h3>
                <div class="value" id="storage-size">-</div>
            </div>
            <div class="stat-card">
                <h3>设备数量</h3>
                <div class="value" id="device-count">-</div>
            </div>
            <div class="stat-card">
                <h3>猫咪数量</h3>
                <div class="value" id="cat-count">-</div>
            </div>
        </div>
        
        <div class="tabs">
            <div class="tab active" onclick="switchTab('remote')">云端视频</div>
            <div class="tab" onclick="switchTab('local')">本地视频</div>
            <div class="tab" onclick="switchTab('cats')">按猫展示</div>
            <div class="tab" onclick="switchTab('tasks')">下载任务</div>
        </div>
        
        <div class="tab-content">
            <!-- 云端视频 -->
            <div class="tab-pane active" id="remote-pane">
                <div class="controls">
                    <button class="btn btn-primary" onclick="loadRemoteVideos()">刷新列表</button>
                    <button class="btn btn-success" onclick="downloadSelected()" id="download-btn" disabled>下载选中</button>
                    <button class="btn btn-primary" onclick="selectAll()">全选</button>
                    <button class="btn btn-primary" onclick="clearSelection()">清空选择</button>
                </div>
                <div id="remote-videos" class="video-grid"></div>
                <div class="pagination" id="remote-pagination"></div>
            </div>
            
            <!-- 本地视频 -->
            <div class="tab-pane" id="local-pane">
                <div class="controls">
                    <button class="btn btn-primary" onclick="loadLocalVideos()">刷新列表</button>
                    <button class="btn btn-danger" onclick="cleanupStorage()">清理存储</button>
                </div>
                <div id="local-videos" class="video-grid"></div>
                <div class="pagination" id="local-pagination"></div>
            </div>

            <!-- 按猫展示 -->
            <div class="tab-pane" id="cats-pane">
                <div class="controls">
                    <button class="btn btn-primary" onclick="loadCatList()">刷新猫咪列表</button>
                    <button class="btn btn-success" onclick="downloadSelectedCatVideos()" id="download-cat-btn" disabled>下载选中猫咪的视频</button>
                    <button class="btn btn-primary" onclick="selectAllCats()">全选猫咪</button>
                    <button class="btn btn-primary" onclick="clearCatSelection()">清空选择</button>
                </div>

                <!-- 猫咪列表 -->
                <div id="cat-list" class="cat-grid" style="display: block;"></div>

                <!-- 猫咪视频详情 -->
                <div id="cat-videos-detail" style="display: none;">
                    <div class="controls">
                        <button class="btn btn-primary" onclick="backToCatList()">返回猫咪列表</button>
                        <button class="btn btn-success" onclick="downloadSelectedVideos()" id="download-videos-btn" disabled>下载选中视频</button>
                        <button class="btn btn-primary" onclick="selectAllVideos()">全选视频</button>
                        <button class="btn btn-primary" onclick="clearVideoSelection()">清空选择</button>

                        <!-- 日期筛选 -->
                        <input type="date" id="start-date" placeholder="开始日期">
                        <input type="date" id="end-date" placeholder="结束日期">
                        <button class="btn btn-primary" onclick="filterCatVideosByDate()">按日期筛选</button>
                        <button class="btn btn-primary" onclick="clearDateFilter()">清除筛选</button>
                    </div>
                    <div id="cat-info" class="cat-info-header"></div>
                    <div id="cat-videos" class="video-grid"></div>
                    <div class="pagination" id="cat-videos-pagination"></div>
                </div>
            </div>
            
            <!-- 下载任务 -->
            <div class="tab-pane" id="tasks-pane">
                <div class="controls">
                    <button class="btn btn-primary" onclick="loadTasks()">刷新任务</button>
                </div>
                <div id="tasks" class="task-list"></div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let selectedVideos = new Set();
        let currentPage = 1;
        let pageSize = 20;
        let localPageSize = 5; // 本地视频每页显示5个

        // 猫咪相关变量
        let selectedCats = new Set();
        let currentCat = null;
        let catVideosPage = 1;
        let catVideosPageSize = 10;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadRemoteVideos();

            // 定时刷新任务状态
            setInterval(loadTasks, 5000);
        });

        // 切换标签页
        function switchTab(tabName) {
            // 更新标签样式
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));

            event.target.classList.add('active');
            document.getElementById(tabName + '-pane').classList.add('active');

            // 加载对应数据
            if (tabName === 'remote') {
                loadRemoteVideos();
            } else if (tabName === 'local') {
                loadLocalVideos();
            } else if (tabName === 'cats') {
                loadCatList();
            } else if (tabName === 'tasks') {
                loadTasks();
            }
        }

        // 加载统计信息
        async function loadStats() {
            try {
                const response = await fetch('/api/storage/stats');
                const stats = await response.json();

                document.getElementById('storage-size').textContent = formatBytes(stats.total_size || 0);
                document.getElementById('device-count').textContent = stats.device_count || 0;
                document.getElementById('cat-count').textContent = stats.cat_count || 0;
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }

        // 加载云端视频列表
        async function loadRemoteVideos(page = 1) {
            const container = document.getElementById('remote-videos');
            container.innerHTML = '<div class="loading">加载中...</div>';

            try {
                const response = await fetch(`/api/videos/remote?page=${page}&page_size=${pageSize}`);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '加载失败');
                }

                renderVideoGrid(data.videos, container, 'remote');
                renderPagination(data.page, data.total_pages, 'remote-pagination', loadRemoteVideos);

                currentPage = page;
            } catch (error) {
                container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 加载本地视频列表
        async function loadLocalVideos(page = 1) {
            const container = document.getElementById('local-videos');
            container.innerHTML = '<div class="loading">加载中...</div>';

            try {
                const response = await fetch(`/api/videos/local?page=${page}&page_size=${localPageSize}`);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '加载失败');
                }

                renderLocalVideoGrid(data.videos, container);
                renderPagination(data.page, data.total_pages, 'local-pagination', loadLocalVideos);
            } catch (error) {
                container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 渲染视频网格
        function renderVideoGrid(videos, container, type) {
            if (videos.length === 0) {
                container.innerHTML = '<div class="loading">暂无数据</div>';
                return;
            }

            container.innerHTML = videos.map(video => `
                <div class="video-card ${selectedVideos.has(video.video_id) ? 'selected' : ''}"
                     onclick="toggleVideoSelection('${video.video_id}')">
                    <div class="video-info">
                        <strong>视频ID:</strong> ${video.video_id}<br>
                        <strong>设备ID:</strong> ${video.device_id}<br>
                        <strong>猫ID:</strong> ${video.animal_id || '未知'}<br>
                        <strong>开始时间:</strong> ${formatTimestamp(video.start_time)}<br>
                        <strong>行为类型:</strong> ${video.behavior_type}<br>
                        <strong>置信度:</strong> ${(video.cat_confidence * 100).toFixed(1)}%
                    </div>
                </div>
            `).join('');

            updateDownloadButton();
        }

        // 渲染本地视频网格
        function renderLocalVideoGrid(videos, container) {
            if (videos.length === 0) {
                container.innerHTML = '<div class="loading">暂无数据</div>';
                return;
            }

            container.innerHTML = videos.map(video => `
                <div class="video-card local">
                    <div class="video-info">
                        <strong>视频ID:</strong> ${video.video_id}<br>
                        <strong>设备ID:</strong> ${video.device_id}<br>
                        <strong>猫ID:</strong> ${video.animal_id || '未知'}<br>
                        <strong>开始时间:</strong> ${formatTimestamp(video.start_time)}<br>
                        <strong>文件大小:</strong> ${formatBytes(video.file_size)}<br>
                        <strong>文件数量:</strong> ${video.file_count}<br>
                        <strong>下载时间:</strong> ${new Date(video.downloaded_at).toLocaleString()}
                    </div>
                    <button class="btn btn-danger" onclick="deleteVideo('${video.video_id}')" style="font-size: 11px; padding: 6px 12px; margin-top: 8px;">删除</button>
                </div>
            `).join('');
        }

        // 渲染分页
        function renderPagination(currentPage, totalPages, containerId, loadFunction) {
            const container = document.getElementById(containerId);
            if (totalPages <= 1) {
                container.innerHTML = '';
                return;
            }

            let pagination = '';

            // 上一页
            if (currentPage > 1) {
                pagination += `<button class="btn btn-primary" onclick="${loadFunction.name}(${currentPage - 1})">上一页</button>`;
            }

            // 页码
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                const active = i === currentPage ? 'btn-success' : 'btn-primary';
                pagination += `<button class="btn ${active}" onclick="${loadFunction.name}(${i})">${i}</button>`;
            }

            // 下一页
            if (currentPage < totalPages) {
                pagination += `<button class="btn btn-primary" onclick="${loadFunction.name}(${currentPage + 1})">下一页</button>`;
            }

            container.innerHTML = pagination;
        }

        // 切换视频选择状态
        function toggleVideoSelection(videoId) {
            if (selectedVideos.has(videoId)) {
                selectedVideos.delete(videoId);
            } else {
                selectedVideos.add(videoId);
            }

            // 更新UI
            const card = event.target.closest('.video-card');
            card.classList.toggle('selected');
            updateDownloadButton();
        }

        // 全选
        function selectAll() {
            document.querySelectorAll('#remote-videos .video-card').forEach(card => {
                const videoId = card.textContent.match(/视频ID:\s*([^\s]+)/)[1];
                selectedVideos.add(videoId);
                card.classList.add('selected');
            });
            updateDownloadButton();
        }

        // 清空选择
        function clearSelection() {
            selectedVideos.clear();
            document.querySelectorAll('.video-card').forEach(card => {
                card.classList.remove('selected');
            });
            updateDownloadButton();
        }

        // 更新下载按钮状态
        function updateDownloadButton() {
            const btn = document.getElementById('download-btn');
            btn.disabled = selectedVideos.size === 0;
            btn.textContent = `下载选中 (${selectedVideos.size})`;
        }

        // 下载选中的视频
        async function downloadSelected() {
            if (selectedVideos.size === 0) return;

            // 立即显示反馈，禁用按钮
            const btn = document.getElementById('download-btn');
            const originalText = btn.textContent;
            btn.disabled = true;
            btn.textContent = '正在创建下载任务...';

            try {
                const response = await fetch('/api/videos/download', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        video_ids: Array.from(selectedVideos)
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '下载失败');
                }

                // 显示成功消息
                alert(`正在为 ${data.video_count} 个视频创建下载任务，请稍后在"下载任务"标签页查看进度`);
                clearSelection();
                switchTab('tasks');
            } catch (error) {
                alert(`下载失败: ${error.message}`);
            } finally {
                // 恢复按钮状态
                btn.disabled = false;
                btn.textContent = originalText;
            }
        }

        // 加载任务列表
        async function loadTasks() {
            const container = document.getElementById('tasks');

            try {
                const response = await fetch('/api/tasks');
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '加载失败');
                }

                renderTasks(data.tasks, container);
            } catch (error) {
                container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 渲染任务列表
        function renderTasks(tasks, container) {
            if (tasks.length === 0) {
                container.innerHTML = '<div class="loading">暂无任务</div>';
                return;
            }

            container.innerHTML = tasks.map(task => `
                <div class="task-item">
                    <div class="task-info">
                        <strong>${task.video_info.video_id}</strong> - ${task.video_info.device_id}
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${task.progress}%"></div>
                        </div>
                        <small>${task.progress.toFixed(1)}% - ${task.error || ''}</small>
                    </div>
                    <div>
                        <span class="task-status status-${task.status}">${getStatusText(task.status)}</span>
                        ${task.status === 'downloading' || task.status === 'pending' ?
                            `<button class="btn btn-danger" onclick="cancelTask('${task.id}')">取消</button>` : ''}
                    </div>
                </div>
            `).join('');
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '等待中',
                'downloading': '下载中',
                'completed': '已完成',
                'failed': '失败',
                'cancelled': '已取消'
            };
            return statusMap[status] || status;
        }

        // 取消任务
        async function cancelTask(taskId) {
            try {
                const response = await fetch(`/api/tasks/${taskId}`, {
                    method: 'DELETE'
                });

                if (!response.ok) {
                    const data = await response.json();
                    throw new Error(data.error || '取消失败');
                }

                loadTasks();
            } catch (error) {
                alert(`取消任务失败: ${error.message}`);
            }
        }

        // 删除本地视频
        async function deleteVideo(videoId) {
            if (!confirm('确定要删除这个视频吗？')) return;

            try {
                const response = await fetch(`/api/videos/${videoId}`, {
                    method: 'DELETE'
                });

                if (!response.ok) {
                    const data = await response.json();
                    throw new Error(data.error || '删除失败');
                }

                loadLocalVideos();
                loadStats();
            } catch (error) {
                alert(`删除失败: ${error.message}`);
            }
        }

        // 清理存储
        async function cleanupStorage() {
            try {
                const response = await fetch('/api/storage/cleanup', {
                    method: 'POST'
                });

                if (!response.ok) {
                    const data = await response.json();
                    throw new Error(data.error || '清理失败');
                }

                alert('存储清理完成');
                loadStats();
            } catch (error) {
                alert(`清理失败: ${error.message}`);
            }
        }

        // 工具函数
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatTimestamp(timestamp) {
            return new Date(timestamp * 1000).toLocaleString();
        }

        // ==================== 猫咪相关功能 ====================

        // 加载猫咪列表
        async function loadCatList() {
            const container = document.getElementById('cat-list');
            container.innerHTML = '<div class="loading">加载中...</div>';

            // 显示猫咪列表，隐藏视频详情
            document.getElementById('cat-list').style.display = 'block';
            document.getElementById('cat-videos-detail').style.display = 'none';

            try {
                const response = await fetch('/api/cats');
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '加载失败');
                }

                renderCatGrid(data.cats, container);
            } catch (error) {
                container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 渲染猫咪网格
        function renderCatGrid(cats, container) {
            if (cats.length === 0) {
                container.innerHTML = '<div class="loading">暂无猫咪数据</div>';
                return;
            }

            container.innerHTML = cats.map(cat => `
                <div class="cat-card ${selectedCats.has(cat.id) ? 'selected' : ''}"
                     onclick="toggleCatSelection('${cat.id}', event)">
                    <div class="cat-avatar">
                        ${cat.avatar_url ? `<img src="${cat.avatar_url}" style="width:100%;height:100%;border-radius:50%;object-fit:cover;">` : '🐱'}
                    </div>
                    <div class="cat-name">${cat.name}</div>
                    <div class="cat-stats">
                        视频数量: ${cat.video_count}<br>
                        ${cat.last_video_at ? `最后视频: ${formatTimestamp(cat.last_video_at)}` : '暂无视频'}
                    </div>
                    <button class="btn btn-primary" onclick="viewCatVideos('${cat.id}', event)" style="margin-top: 10px; font-size: 12px;">查看视频</button>
                </div>
            `).join('');

            updateCatDownloadButton();
        }

        // 切换猫咪选择状态
        function toggleCatSelection(catId, event) {
            // 阻止事件冒泡，避免触发查看视频
            if (event.target.tagName === 'BUTTON') return;

            if (selectedCats.has(catId)) {
                selectedCats.delete(catId);
            } else {
                selectedCats.add(catId);
            }

            // 更新UI
            const card = event.target.closest('.cat-card');
            card.classList.toggle('selected');
            updateCatDownloadButton();
        }

        // 查看猫咪视频
        function viewCatVideos(catId, event) {
            event.stopPropagation(); // 阻止事件冒泡
            currentCat = catId;
            catVideosPage = 1;

            // 隐藏猫咪列表，显示视频详情
            document.getElementById('cat-list').style.display = 'none';
            document.getElementById('cat-videos-detail').style.display = 'block';

            loadCatVideos(catId, 1);
        }

        // 加载猫咪的视频列表
        async function loadCatVideos(catId, page = 1) {
            const container = document.getElementById('cat-videos');
            const infoContainer = document.getElementById('cat-info');

            container.innerHTML = '<div class="loading">加载中...</div>';

            try {
                // 构建查询参数
                let url = `/api/cats/${catId}/videos?page=${page}&page_size=${catVideosPageSize}`;

                // 添加日期筛选
                const startDate = document.getElementById('start-date').value;
                const endDate = document.getElementById('end-date').value;

                if (startDate) {
                    const startTime = Math.floor(new Date(startDate).getTime() / 1000);
                    url += `&start_time=${startTime}`;
                }
                if (endDate) {
                    const endTime = Math.floor(new Date(endDate + ' 23:59:59').getTime() / 1000);
                    url += `&end_time=${endTime}`;
                }

                const response = await fetch(url);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '加载失败');
                }

                // 显示猫咪信息
                infoContainer.innerHTML = `
                    <h3>${data.cat_info.name} 的视频</h3>
                    <p>总视频数: ${data.cat_info.video_count} | 当前页: ${data.page}/${data.total_pages} | 共 ${data.total} 个视频</p>
                `;

                // 渲染视频列表
                renderCatVideoGrid(data.videos, container);
                renderPagination(data.page, data.total_pages, 'cat-videos-pagination', (page) => loadCatVideos(catId, page));

                catVideosPage = page;
            } catch (error) {
                container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 渲染猫咪视频网格
        function renderCatVideoGrid(videos, container) {
            if (videos.length === 0) {
                container.innerHTML = '<div class="loading">暂无视频数据</div>';
                return;
            }

            container.innerHTML = videos.map(video => `
                <div class="video-card ${selectedVideos.has(video.video_id) ? 'selected' : ''}"
                     onclick="toggleVideoSelection('${video.video_id}')">
                    <div class="video-info">
                        <strong>视频ID:</strong> ${video.video_id}<br>
                        <strong>设备ID:</strong> ${video.device_id}<br>
                        <strong>开始时间:</strong> ${formatTimestamp(video.start_time)}<br>
                        <strong>行为类型:</strong> ${video.behavior_type}<br>
                        <strong>置信度:</strong> ${(video.cat_confidence * 100).toFixed(1)}%
                    </div>
                </div>
            `).join('');

            updateVideoDownloadButton();
        }

        // 返回猫咪列表
        function backToCatList() {
            document.getElementById('cat-list').style.display = 'block';
            document.getElementById('cat-videos-detail').style.display = 'none';
            currentCat = null;
            selectedVideos.clear();

            // 清除日期筛选
            document.getElementById('start-date').value = '';
            document.getElementById('end-date').value = '';
        }

        // 按日期筛选猫咪视频
        function filterCatVideosByDate() {
            if (currentCat) {
                loadCatVideos(currentCat, 1);
            }
        }

        // 清除日期筛选
        function clearDateFilter() {
            document.getElementById('start-date').value = '';
            document.getElementById('end-date').value = '';
            if (currentCat) {
                loadCatVideos(currentCat, 1);
            }
        }

        // 全选猫咪
        function selectAllCats() {
            document.querySelectorAll('#cat-list .cat-card').forEach(card => {
                const catId = card.onclick.toString().match(/'([^']+)'/)[1];
                selectedCats.add(catId);
                card.classList.add('selected');
            });
            updateCatDownloadButton();
        }

        // 清空猫咪选择
        function clearCatSelection() {
            selectedCats.clear();
            document.querySelectorAll('#cat-list .cat-card').forEach(card => {
                card.classList.remove('selected');
            });
            updateCatDownloadButton();
        }

        // 全选视频
        function selectAllVideos() {
            document.querySelectorAll('#cat-videos .video-card').forEach(card => {
                const videoId = card.textContent.match(/视频ID:\s*([^\s]+)/)[1];
                selectedVideos.add(videoId);
                card.classList.add('selected');
            });
            updateVideoDownloadButton();
        }

        // 清空视频选择
        function clearVideoSelection() {
            selectedVideos.clear();
            document.querySelectorAll('#cat-videos .video-card').forEach(card => {
                card.classList.remove('selected');
            });
            updateVideoDownloadButton();
        }

        // 更新猫咪下载按钮状态
        function updateCatDownloadButton() {
            const btn = document.getElementById('download-cat-btn');
            btn.disabled = selectedCats.size === 0;
            btn.textContent = `下载选中猫咪的视频 (${selectedCats.size})`;
        }

        // 更新视频下载按钮状态
        function updateVideoDownloadButton() {
            const btn = document.getElementById('download-videos-btn');
            btn.disabled = selectedVideos.size === 0;
            btn.textContent = `下载选中视频 (${selectedVideos.size})`;
        }

        // 下载选中猫咪的所有视频
        async function downloadSelectedCatVideos() {
            if (selectedCats.size === 0) return;

            // 立即显示反馈，禁用按钮
            const btn = document.getElementById('download-cat-btn');
            const originalText = btn.textContent;
            btn.disabled = true;
            btn.textContent = '正在获取视频列表...';

            try {
                // 为每个选中的猫咪获取所有视频ID
                let allVideoIds = [];
                let totalCats = selectedCats.size;
                let processedCats = 0;

                for (const catId of selectedCats) {
                    processedCats++;
                    btn.textContent = `正在获取视频列表... (${processedCats}/${totalCats})`;

                    const response = await fetch(`/api/cats/${catId}/videos?page=1&page_size=1000`);
                    const data = await response.json();

                    if (response.ok && data.videos) {
                        const videoIds = data.videos.map(video => video.video_id);
                        allVideoIds = allVideoIds.concat(videoIds);
                    }
                }

                if (allVideoIds.length === 0) {
                    alert('选中的猫咪没有可下载的视频');
                    return;
                }

                btn.textContent = '正在创建下载任务...';

                // 发起下载请求
                const response = await fetch('/api/videos/download', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        video_ids: allVideoIds
                    })
                });

                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.error || '下载失败');
                }

                alert(`正在为 ${result.video_count} 个视频创建下载任务，请稍后在"下载任务"标签页查看进度`);
                clearCatSelection();
                switchTab('tasks');
            } catch (error) {
                alert(`下载失败: ${error.message}`);
            } finally {
                // 恢复按钮状态
                btn.disabled = false;
                btn.textContent = originalText;
            }
        }

        // 下载选中的视频（在猫咪视频详情页面）
        async function downloadSelectedVideos() {
            if (selectedVideos.size === 0) return;

            // 立即显示反馈，禁用按钮
            const btn = document.getElementById('download-videos-btn');
            const originalText = btn.textContent;
            btn.disabled = true;
            btn.textContent = '正在创建下载任务...';

            try {
                const response = await fetch('/api/videos/download', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        video_ids: Array.from(selectedVideos)
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '下载失败');
                }

                alert(`正在为 ${data.video_count} 个视频创建下载任务，请稍后在"下载任务"标签页查看进度`);
                clearVideoSelection();
                switchTab('tasks');
            } catch (error) {
                alert(`下载失败: ${error.message}`);
            } finally {
                // 恢复按钮状态
                btn.disabled = false;
                btn.textContent = originalText;
            }
        }
    </script>
</body>
</html>
