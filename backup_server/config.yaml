# 备份服务器配置文件

server:
  host: "0.0.0.0"
  port: "8080"

# 后端服务配置
backend:
  url: "http://127.0.0.1:5678"  # 替换为实际的后端服务地址
  auth_token: "FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"

# MinIO配置（将从后端API获取，这里作为备用）
minio:
  endpoint: "***************:9000"
  access_key: "animsuper"
  secret_key: "4vbtCeEQgcN2uB"
  use_ssl: false
  bucket: "records"

# 存储配置
storage:
  data_path: "./data"  # 本地数据存储路径

# 下载配置
download:
  max_concurrent: 5      # 最大并发下载数
  chunk_size: 1048576    # 下载块大小 (1MB)

# 代理配置
proxy:
  http_proxy: "http://127.0.0.1:7897"
  https_proxy: "http://127.0.0.1:7897"
  all_proxy: "socks5://127.0.0.1:7897"
