# 视频备份系统 (Backup Server)

## 概述

视频备份系统是一个用于从云端下载和管理猫砂盆视频数据的工具，专为开发者设计，用于模型训练和数据分析。

## 功能特性

- 🎥 **视频管理**: 从云端获取和下载normal_poop类型的视频
- 📁 **智能分类**: 按设备和猫咪ID自动分类存储
- ⚡ **并发下载**: 支持多任务并发下载，提高效率
- 🔗 **符号链接**: 自动创建按猫分类的符号链接，避免重复存储
- 📊 **实时监控**: Web界面实时显示下载进度和任务状态
- 🗂️ **元数据管理**: 自动保存视频元数据和重量数据
- 🌐 **代理支持**: 支持HTTP/HTTPS/SOCKS5代理

## 系统架构

```
backup_server/
├── cmd/                    # 命令行工具
├── pkg/
│   ├── config/            # 配置管理
│   ├── models/            # 数据模型
│   ├── services/          # 业务服务
│   │   ├── backend_client.go    # 后端API客户端
│   │   ├── download_service.go  # 下载服务
│   │   └── storage_service.go   # 存储服务
│   ├── handlers/          # HTTP处理器
│   └── utils/             # 工具函数
├── web/
│   ├── static/            # 静态资源
│   └── templates/         # HTML模板
├── data/                  # 数据存储目录
│   ├── by_device/         # 按设备分类
│   └── by_cat/            # 按猫分类（符号链接）
├── logs/                  # 日志文件
├── config.yaml           # 配置文件
└── main.go               # 主程序
```

## 数据存储结构

```
data/
├── by_device/
│   └── device202502270220f7cbb4421000/
│       └── 2025-01-23_20-46-05_hls/
│           ├── playlist.m3u8
│           ├── segment_0001.ts
│           ├── segment_0002.ts
│           ├── cat_weight_1737712707.json
│           └── metadata.json
└── by_cat/
    └── cat_12345/
        └── 2025-01-23_20-46-05_hls/  # 符号链接到设备目录
```

## 安装和配置

### 1. 编译程序

```bash
cd backup_server
go mod tidy
go build -o backup_server
```

### 2. 配置文件

编辑 `config.yaml` 文件：

```yaml
server:
  host: "0.0.0.0"
  port: "8080"

backend:
  url: "http://your-backend-server"  # 后端服务地址
  auth_token: "FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"

storage:
  data_path: "./data"

download:
  max_concurrent: 5
  chunk_size: 1048576

proxy:
  http_proxy: "http://127.0.0.1:7897"
  https_proxy: "http://127.0.0.1:7897"
  all_proxy: "socks5://127.0.0.1:7897"
```

### 3. 设置代理（如需要）

```bash
export https_proxy=http://127.0.0.1:7897
export http_proxy=http://127.0.0.1:7897
export all_proxy=socks5://127.0.0.1:7897
```

### 4. 启动服务

```bash
./backup_server
```

服务将在 `http://localhost:8080` 启动。

## 使用方法

### Web界面

1. 打开浏览器访问 `http://localhost:8080`
2. 在"云端视频"标签页查看可下载的视频
3. 选择需要下载的视频（支持多选）
4. 点击"下载选中"开始下载
5. 在"下载任务"标签页监控下载进度
6. 在"本地视频"标签页管理已下载的视频

### API接口

#### 获取云端视频列表
```bash
GET /api/videos/remote?page=1&page_size=20
```

#### 下载视频
```bash
POST /api/videos/download
Content-Type: application/json

{
  "video_ids": ["video_123", "video_456"]
}
```

#### 获取任务状态
```bash
GET /api/tasks
GET /api/tasks/{task_id}
```

#### 获取本地视频
```bash
GET /api/videos/local?page=1&page_size=20
```

#### 获取存储统计
```bash
GET /api/storage/stats
```

## 元数据格式

每个下载的视频都包含一个 `metadata.json` 文件：

```json
{
  "video_id": "video_123",
  "device_id": "device202502270220f7cbb4421000",
  "animal_id": "cat_456",
  "start_time": 1737712707,
  "end_time": 1737712800,
  "weight_litter": 2.5,
  "weight_cat": 4.2,
  "weight_waste": 0.3,
  "behavior_type": "normal_poop",
  "cat_confidence": 0.95,
  "created_at": "2025-01-23 20:46:05",
  "downloaded_at": "2025-01-24 10:30:15"
}
```

## 重量数据格式

`cat_weight_{timestamp}.json` 文件包含重量序列数据：

```json
{
  "timestamp": 1737712707,
  "weight_data": [
    {"time": 0, "weight": 2.5},
    {"time": 1000, "weight": 2.8},
    {"time": 2000, "weight": 3.1}
  ]
}
```

## 故障排除

### 1. 连接后端失败
- 检查 `config.yaml` 中的后端URL是否正确
- 确认网络连接和代理设置
- 验证认证token是否有效

### 2. 下载失败
- 检查MinIO连接配置
- 确认存储空间是否足够
- 查看日志文件了解详细错误信息

### 3. 代理问题
- 确认代理服务器是否正常运行
- 检查代理配置格式是否正确
- 尝试直连（临时禁用代理）

## 开发说明

### 添加新功能
1. 在 `pkg/models/` 中定义数据模型
2. 在 `pkg/services/` 中实现业务逻辑
3. 在 `pkg/handlers/` 中添加HTTP处理器
4. 在 `main.go` 中注册路由

### 测试
```bash
go test ./...
```

### 构建
```bash
go build -ldflags "-s -w" -o backup_server
```

## 许可证

本项目仅供内部开发使用。
