# Backup Server

视频备份和管理服务器，提供本地视频存储、转换和管理功能。

## 快速开始

### 1. 架构检测

首先检测您的系统架构和Docker环境：

```bash
# 检测系统架构和Docker兼容性
./scripts/detect_arch.sh
```

### 2. 配置环境

复制示例配置文件并编辑：

```bash
cp .env.example .env
# 编辑 .env 文件，配置后端服务器连接信息
```

### 3. 快速部署

使用Docker Compose快速部署（自动检测架构）：

```bash
# 构建并启动服务（自动检测架构）
./scripts/quick_deploy.sh

# 跳过构建步骤（如果镜像已存在）
./scripts/quick_deploy.sh --skip-build

# 部署后跟随日志
./scripts/quick_deploy.sh --logs
```

**支持的架构：**
- ✅ **x86_64** (Intel/AMD 64-bit) - `linux/amd64`
- ✅ **ARM64** (Apple Silicon, ARM 64-bit) - `linux/arm64`
- ⚠️ **ARM v7** (32-bit ARM) - `linux/arm/v7` (有限支持)
- ⚠️ **x86** (32-bit) - `linux/386` (有限支持)

### 3. 查看日志

```bash
# 查看最近100行日志
./scripts/logs.sh

# 跟随日志输出
./scripts/logs.sh -f

# 查看最近50行日志
./scripts/logs.sh -n 50

# 跟随日志，从最近200行开始
./scripts/logs.sh -f -n 200
```

### 4. 功能测试

```bash
# 运行API测试套件
./scripts/test.py
```

## 服务访问

部署成功后，可以通过以下地址访问：

- **Web界面**: http://localhost:8080
- **API文档**: http://localhost:8080/api/

## 主要功能

### 本地视频管理
- 查看已下载的视频
- 按猫咪分类管理
- 视频转换状态跟踪

### 视频下载
- 从后端服务器下载视频
- 批量下载支持
- 下载任务管理

### 视频转换
- HLS到MP4格式转换
- 批量转换支持
- 转换任务监控

### 任务管理
- 下载任务状态跟踪
- 转换任务状态跟踪
- 任务暂停、恢复、重试

## 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `TZ` | 时区设置 | `Asia/Shanghai` |
| `DATA_PATH` | 数据存储路径 | `./data` |
| `MAX_CONCURRENT_DOWNLOADS` | 最大并发下载数 | `5` |
| `BACKEND_URL` | 后端服务器地址 | `http://localhost:5678` |
| `BACKEND_USERNAME` | 后端服务器用户名 | - |
| `BACKEND_PASSWORD` | 后端服务器密码 | - |

### 目录结构

```
backup_server/
├── data/                   # 数据存储目录
│   └── by_device/         # 按设备分类的视频
├── web/                   # Web界面资源
├── scripts/               # 部署和管理脚本
│   ├── quick_deploy.sh   # 快速部署脚本
│   ├── logs.sh           # 日志查看脚本
│   └── test.py           # 功能测试脚本
├── docker-compose.yml    # Docker Compose配置
├── Dockerfile           # Docker镜像构建文件
└── .env                 # 环境配置文件
```

## 多架构支持

### 自动架构检测

部署脚本会自动检测您的系统架构并构建相应的Docker镜像：

```bash
# 系统会自动检测并显示：
# Detected architecture: arm64
# Docker platform: linux/arm64
# Go architecture: arm64
```

### 手动指定架构

如果需要为特定架构构建：

```bash
# 为 x86_64 构建
GOOS=linux GOARCH=amd64 go build -o backup-server .
docker build --platform linux/amd64 -t backup-server .

# 为 ARM64 构建
GOOS=linux GOARCH=arm64 go build -o backup-server .
docker build --platform linux/arm64 -t backup-server .
```

### 跨平台构建

使用Docker Buildx进行多平台构建：

```bash
# 创建多平台构建器
docker buildx create --name multiarch --use

# 构建多平台镜像
docker buildx build --platform linux/amd64,linux/arm64 -t backup-server .
```

## 开发模式

如果需要在开发模式下运行（不使用Docker）：

```bash
# 安装依赖
go mod download

# 运行服务
go run main.go
```

## 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看详细日志
   ./scripts/logs.sh -f
   
   # 检查容器状态
   docker-compose ps
   ```

2. **后端连接失败**
   - 检查 `.env` 文件中的 `BACKEND_URL`、`BACKEND_USERNAME`、`BACKEND_PASSWORD` 配置
   - 确保后端服务器正在运行

3. **端口冲突**
   - 检查端口8080是否被其他服务占用
   - 修改 `docker-compose.yml` 中的端口映射

### 重新部署

```bash
# 停止服务
docker-compose down

# 清理数据（可选）
sudo rm -rf data/*

# 重新部署
./scripts/quick_deploy.sh
```

## API文档

主要API端点：

- `GET /api/storage/stats` - 存储统计信息
- `GET /api/cats` - 获取猫咪列表
- `GET /api/videos/local/cat/{cat_id}` - 获取指定猫咪的本地视频
- `GET /api/tasks` - 获取下载任务列表
- `GET /api/conversions` - 获取转换任务列表
- `POST /api/videos/download` - 创建下载任务
- `POST /api/conversions/convert` - 创建转换任务
