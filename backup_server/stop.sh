#!/bin/bash

# 视频备份系统停止脚本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 配置
LOG_DIR="logs"
PID_FILE="$LOG_DIR/backup_server.pid"

# 停止服务
stop_service() {
    echo -e "${YELLOW}正在停止视频备份系统...${NC}"
    
    if [ ! -f "$PID_FILE" ]; then
        echo -e "${YELLOW}PID文件不存在，检查是否有运行的进程...${NC}"
        
        # 查找可能运行的进程
        local pids=$(pgrep -f "backup_server" || true)
        if [ -n "$pids" ]; then
            echo "发现运行中的进程: $pids"
            echo -n "是否要停止这些进程? (y/N): "
            read -r response
            if [[ "$response" =~ ^[Yy]$ ]]; then
                echo "$pids" | xargs kill
                echo -e "${GREEN}✓ 进程已停止${NC}"
            else
                echo "操作已取消"
            fi
        else
            echo -e "${GREEN}没有发现运行中的backup_server进程${NC}"
        fi
        return 0
    fi
    
    local pid=$(cat "$PID_FILE")
    
    # 检查进程是否存在
    if ! ps -p "$pid" > /dev/null 2>&1; then
        echo -e "${YELLOW}进程 $pid 不存在，清理PID文件${NC}"
        rm -f "$PID_FILE"
        return 0
    fi
    
    echo "停止进程 $pid..."
    
    # 尝试优雅停止
    kill "$pid"
    
    # 等待进程停止
    local count=0
    while ps -p "$pid" > /dev/null 2>&1; do
        if [ $count -ge 10 ]; then
            echo -e "${YELLOW}优雅停止超时，强制终止进程...${NC}"
            kill -9 "$pid" 2>/dev/null || true
            break
        fi
        sleep 1
        count=$((count + 1))
        echo -n "."
    done
    echo ""
    
    # 清理PID文件
    rm -f "$PID_FILE"
    
    echo -e "${GREEN}✓ 视频备份系统已停止${NC}"
}

# 显示状态
show_status() {
    echo "=== 视频备份系统状态 ==="
    
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo -e "${GREEN}状态: 运行中${NC}"
            echo "PID: $pid"
            
            # 显示进程信息
            echo "进程信息:"
            ps -p "$pid" -o pid,ppid,cmd,etime,pcpu,pmem
            
            # 显示端口占用
            echo ""
            echo "端口占用:"
            lsof -p "$pid" -i 2>/dev/null || echo "无网络连接"
            
        else
            echo -e "${RED}状态: 已停止 (PID文件存在但进程不存在)${NC}"
            echo "清理PID文件..."
            rm -f "$PID_FILE"
        fi
    else
        # 检查是否有其他backup_server进程
        local pids=$(pgrep -f "backup_server" || true)
        if [ -n "$pids" ]; then
            echo -e "${YELLOW}状态: 可能运行中 (无PID文件)${NC}"
            echo "发现进程: $pids"
        else
            echo -e "${RED}状态: 已停止${NC}"
        fi
    fi
}

# 强制停止所有相关进程
force_stop() {
    echo -e "${YELLOW}强制停止所有backup_server进程...${NC}"
    
    local pids=$(pgrep -f "backup_server" || true)
    if [ -n "$pids" ]; then
        echo "发现进程: $pids"
        echo "$pids" | xargs kill -9
        echo -e "${GREEN}✓ 所有进程已强制停止${NC}"
    else
        echo -e "${GREEN}没有发现运行中的进程${NC}"
    fi
    
    # 清理PID文件
    rm -f "$PID_FILE"
}

# 重启服务
restart_service() {
    echo -e "${YELLOW}重启视频备份系统...${NC}"
    
    # 先停止
    stop_service
    
    # 等待一下
    sleep 2
    
    # 再启动
    if [ -f "start.sh" ]; then
        echo "启动服务..."
        ./start.sh
    else
        echo -e "${RED}错误: 找不到启动脚本 start.sh${NC}"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "视频备份系统停止脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help      显示帮助信息"
    echo "  -s, --status    显示服务状态"
    echo "  -f, --force     强制停止所有相关进程"
    echo "  -r, --restart   重启服务"
    echo ""
    echo "示例:"
    echo "  $0              # 正常停止服务"
    echo "  $0 -s           # 查看服务状态"
    echo "  $0 -f           # 强制停止所有进程"
    echo "  $0 -r           # 重启服务"
}

# 主函数
main() {
    case "${1:-stop}" in
        -h|--help)
            show_help
            ;;
        -s|--status)
            show_status
            ;;
        -f|--force)
            force_stop
            ;;
        -r|--restart)
            restart_service
            ;;
        stop|"")
            stop_service
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
